export const formatFileSize = (bytes: number, format?: string) => {
  if (bytes === 0) return "0 B";
  const k = 1024;

  const sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  if (format) {
    return (
      parseFloat((bytes / Math.pow(k, sizes.indexOf(format))).toFixed(2)) +
      " " +
      format
    );
  }
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};
