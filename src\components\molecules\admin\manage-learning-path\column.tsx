import { BaseButton } from "@/components/atoms/button";
import { BaseSwitch } from "@/components/atoms/switch";

import {
  IGetLearningCodeListResponse,
  IGetLearningLevelListResponse,
} from "@/interfaces/admin/manage-learning-path/list";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { ChevronDown, Pencil, Trash2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { BaseSeparator } from "@/components/atoms/separator";

interface Props {
  onEdit: (
    id: IGetLearningCodeListResponse | IGetLearningLevelListResponse
  ) => void;
  onDelete: (
    id: IGetLearningCodeListResponse | IGetLearningLevelListResponse
  ) => void;
  onStatusChange: (
    id: IGetLearningCodeListResponse | IGetLearningLevelListResponse
  ) => void;
}

export const getColumnsManageLearningCode = ({
  onEdit,
  onDelete,
  onStatusChange,
}: Props): ColumnDef<IGetLearningCodeListResponse>[] => {
  return [
    {
      accessorKey: "code",
      header: "Learning Code",
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.code}
          </span>
        );
      },
    },
    { accessorKey: "name", header: "Learning Code Name" },
    {
      accessorKey: "related_job",
      header: "Related Job Position",
      cell(props) {
        const data = props.row.original.related_job;

        return (
          <PillDropdown
            options={data.map((job) => ({
              value: job.job_name ?? "",
              id: job.job_name_id?.toString() ?? "",
            }))}
            selected={`${data.length} Job Position`}
          />
        );
      },
    },
    {
      accessorKey: "related_modul",
      header: "Associated Module",
      cell(props) {
        const data = props.row.original.related_modul;

        return (
          <PillDropdown
            options={data.map((job) => ({
              value: job.modul_name ?? "",
              id: job.modul_id?.toString() ?? "",
            }))}
            selected={`${data.length} Module`}
          />
        );
      },
    },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell(props) {
        const createdAt = props.row.original.created_at;

        return (
          <span>
            {createdAt ? dayjs(createdAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "created_by", header: "Created By" },
    {
      accessorKey: "last_updated",
      header: "Last Updated",
      cell(props) {
        const updatedAt = props.row.original.last_updated;

        return (
          <span>
            {updatedAt ? dayjs(updatedAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "updated_by", header: "Updated By" },
    {
      accessorKey: "status",
      header: "Status",
      cell({ row }) {
        return (
          <BaseSwitch
            checked={
              row.original.status ? row.original.status == "active" : false
            }
            className="cursor-pointer"
            onCheckedChange={() => onStatusChange(row.original)}
          />
        );
      },
    },
    {
      accessorKey: "id",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center justify-start gap-2">
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => onEdit(row.original)}
            >
              <Pencil
                size={24}
                className="fill-gray-700 text-white"
                strokeWidth={1}
              />
            </BaseButton>
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => onDelete(row.original)}
            >
              <Trash2 size={24} className="text-gray-700" strokeWidth={2.5} />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};

export const getColumnsManageLearningLevel = ({
  onEdit,
  onDelete,
  onStatusChange,
}: Props): ColumnDef<IGetLearningLevelListResponse>[] => {
  return [
    {
      accessorKey: "level",
      header: "Level",
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.level}
          </span>
        );
      },
    },
    { accessorKey: "name", header: "Level Name" },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell(props) {
        const createdAt = props.row.original.created_at;
        return (
          <span>
            {createdAt ? dayjs(createdAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "created_by", header: "Created By" },
    {
      accessorKey: "last_updated",
      header: "Last Updated",
      cell(props) {
        const updatedAt = props.row.original.last_updated;

        return (
          <span>
            {updatedAt ? dayjs(updatedAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "updated_by", header: "Updated By" },
    {
      accessorKey: "is_active",
      header: "Status",
      cell({ row }) {
        return (
          <BaseSwitch
            checked={
              row.original.status ? row.original.status == "active" : false
            }
            className="cursor-pointer"
            onCheckedChange={() => onStatusChange(row.original)}
          />
        );
      },
    },
    {
      accessorKey: "id",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center justify-start gap-2">
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => onEdit(row.original)}
            >
              <Pencil
                size={24}
                className="fill-gray-700 text-white"
                strokeWidth={1}
              />
            </BaseButton>
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => onDelete(row.original)}
            >
              <Trash2 size={24} className="text-gray-700" strokeWidth={2.5} />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};

const PillDropdown = ({
  selected,
  options,
}: {
  selected: string;
  options: Array<{
    value: string;
    id: string;
  }>;
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex items-center gap-1 cursor-pointer bg-[#DEDEDE] px-2 py-1 text-xs text-comp-content-primary max-w-max rounded-full">
          <span>{selected}</span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64 max-h-48">
        {options.map((option) => (
          <>
            <DropdownMenuItem key={option.value}>
              {option.value}
            </DropdownMenuItem>
            <BaseSeparator />
          </>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default PillDropdown;
