import ImageRepositoryDeleteConfirmationModal from '@/components/molecules/admin/manage-test/image-repository/delete-image-repository-modal';
import ImageRepositoryNewModal from '@/components/molecules/admin/manage-test/image-repository/new-modal';
import ImageRespositoryTable from '@/components/molecules/admin/manage-test/image-repository/table';
import ImageRepositoryTableHeader from '@/components/molecules/admin/manage-test/image-repository/table-header';

const ImageRespository = () => {
  return (
    <div className="flex flex-col gap-4 h-full">
      <ImageRepositoryTableHeader />
      <ImageRespositoryTable />
      <ImageRepositoryDeleteConfirmationModal />
      <ImageRepositoryNewModal />
    </div>
  );
};

export default ImageRespository;
