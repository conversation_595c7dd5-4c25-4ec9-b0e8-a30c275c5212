"use client";

import React from "react";
import { FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { ArrowLef<PERSON> } from "lucide-react";
import { useRouter } from "next/navigation";
import { BaseButton } from "@/components/atoms/button";
import SectionInformation from "@/components/molecules/admin/manage-section/add/section-information";
import SectionPreview from "@/components/molecules/admin/manage-section/add/section-preview";
import RelatedCompetencies from "@/components/molecules/admin/manage-section/add/related-competencies";
import SectionDuration from "@/components/molecules/admin/manage-section/add/section-duration";
import { useCreateSectionMutation } from "@/services/mutation/admin/manage-section";
import {
  ICreateSectionBody,
  SECTION_TYPE_MAPPING,
  SectionTypeKey,
} from "@/interfaces/admin/manage-section/new";
import { notifyHotError } from "@/components/molecules/toast/hot-toast";

// Dynamic form schema based on section type
const createDynamicSchema = (sectionType: string) => {
  const baseSchema = {
    sectionName: z.string().min(1, "Section name is required"),
    sectionId: z.string().optional(),
    sectionType: z.enum([
      "video",
      "audio",
      "document",
      "quiz",
      "pre-test",
      "post-test isian",
      "post-test pilihan ganda",
      "assignment",
    ]),
    level: z
      .array(z.object({ label: z.string(), value: z.string() }))
      .min(1, "Please select at least one level"),
    categories: z
      .array(z.object({ label: z.string(), value: z.string() }))
      .min(1, "Please select at least one category"),
    technicalCompetencies: z
      .array(z.object({ label: z.string(), value: z.string() }))
      .optional(),
    softCompetencies: z
      .array(z.object({ label: z.string(), value: z.string() }))
      .optional(),
    hours: z.number().min(0).optional(),
    minutes: z.number().min(0).max(59).optional(),
    seconds: z.number().min(0).max(59).optional(),
  };

  // Add type-specific validations
  if (["video", "audio", "document"].includes(sectionType)) {
    return z.object({
      ...baseSchema,
      materialId: z.string().min(1, "Material selection is required"),
      materialLink: z.string().min(1, "Material link is required"),
    });
  }

  if (
    ["quiz", "pre-test", "post-test isian", "post-test pilihan ganda"].includes(
      sectionType
    )
  ) {
    return z.object({
      ...baseSchema,
      questionId: z
        .array(z.string())
        .min(1, "Please select at least one question"),
      selectedQuestions: z
        .array(
          z.object({
            id: z.number(),
            question: z.string(),
            type: z.string(),
            source: z.enum(["bank", "template"]),
            templateName: z.string().optional(),
            options: z
              .object({
                a: z.string(),
                b: z.string(),
                c: z.string(),
                d: z.string(),
              })
              .optional(),
            correctAnswer: z.string().optional(),
          })
        )
        .min(1, "Please select at least one question"),
      passingGrade: z
        .number()
        .min(0, "Passing grade must be at least 0")
        .max(100, "Passing grade cannot exceed 100"),
      numberOfQuestions: z.number().min(1).optional(), // This will be auto-calculated
      withTestTimer: z.enum(["yes", "no"]).refine((val) => val !== undefined, {
        message: "Please select timer option",
      }),
    });
  }

  if (sectionType === "assignment") {
    return z.object({
      ...baseSchema,
      assignmentInstruction: z
        .string()
        .min(1, "Assignment instruction is required")
        .max(500, "Assignment instruction cannot exceed 500 characters"),
    });
  }

  // Default schema for unknown types
  return z.object(baseSchema);
};

// Complete form data type that includes all possible fields
export type AddSectionFormData = {
  sectionName: string;
  sectionId?: string;
  sectionType:
    | "video"
    | "audio"
    | "document"
    | "quiz"
    | "pre-test"
    | "post-test isian"
    | "post-test pilihan ganda"
    | "assignment";
  level: Array<{ label: string; value: string }>;
  categories: Array<{ label: string; value: string }>;
  technicalCompetencies?: Array<{ label: string; value: string }>;
  softCompetencies?: Array<{ label: string; value: string }>;
  hours?: number;
  minutes?: number;
  seconds?: number;
  // Material-related fields
  materialId?: string;
  materialLink?: string;
  // Quiz-related fields
  questionId?: string[];
  selectedQuestions?: Array<{
    id: number;
    question: string;
    type: string;
    source: "bank" | "template";
    templateName?: string;
    options?: {
      a: string;
      b: string;
      c: string;
      d: string;
    };
    correctAnswer?: string;
  }>;
  passingGrade?: number;
  numberOfQuestions?: number;
  withTestTimer?: "yes" | "no";
  // Assignment-related fields
  assignmentInstruction?: string;
};

// Initial schema (will be updated dynamically)
const addSectionSchema = createDynamicSchema("video");

const AddNewSection = () => {
  const router = useRouter();
  const createSectionMutation = useCreateSectionMutation();

  const form = useForm<AddSectionFormData>({
    resolver: zodResolver(addSectionSchema),
    defaultValues: {
      sectionName: "",
      sectionId: "",
      sectionType: "video",
      materialId: "",
      materialLink: "",
      passingGrade: 70,
      numberOfQuestions: 10,
      withTestTimer: "no",
      technicalCompetencies: [],
      softCompetencies: [],
      assignmentInstruction: "",
      level: [],
      categories: [],
      hours: 0,
      minutes: 0,
      seconds: 0,
      questionId: [],
    },
  });

  const {
    handleSubmit,
    watch,
    control,
    reset,
    setValue,
    formState: { errors },
  } = form;
  const sectionType = watch("sectionType");

  // Watch for section type changes and reset related fields
  React.useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (name === "sectionType" && value.sectionType) {
        const newSectionType = value.sectionType;

        // Reset fields based on the new section type
        if (["video", "audio", "document"].includes(newSectionType)) {
          // Clear quiz/assignment fields
          setValue("questionId", []);
          setValue("passingGrade", undefined);
          setValue("numberOfQuestions", undefined);
          setValue("withTestTimer", undefined);
          setValue("assignmentInstruction", "");
        } else if (
          [
            "quiz",
            "pre-test",
            "post-test isian",
            "post-test pilihan ganda",
          ].includes(newSectionType)
        ) {
          // Clear material/assignment fields
          setValue("materialId", "");
          setValue("materialLink", "");
          setValue("assignmentInstruction", "");
        } else if (newSectionType === "assignment") {
          // Clear material/quiz fields
          setValue("materialId", "");
          setValue("materialLink", "");
          setValue("questionId", []);
          setValue("passingGrade", undefined);
          setValue("numberOfQuestions", undefined);
          setValue("withTestTimer", undefined);
        }

        // Update the form schema for validation
        const newSchema = createDynamicSchema(newSectionType);
        // Note: We would need to update the resolver here, but react-hook-form doesn't support dynamic schema changes easily
        // For now, we'll handle validation in the submit handler
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, setValue]);

  // Auto-calculate number of questions based on selected questions
  React.useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (name === "questionId" && value.questionId) {
        setValue("numberOfQuestions", value.questionId.length);
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, setValue]);

  // Transform form data to API payload
  const transformFormDataToPayload = (
    data: AddSectionFormData
  ): ICreateSectionBody => {
    // Calculate duration in seconds from hours, minutes, seconds
    const duration =
      (data.hours || 0) * 3600 + (data.minutes || 0) * 60 + (data.seconds || 0);

    // Map section type to API format
    const sectionType =
      SECTION_TYPE_MAPPING[data.sectionType as SectionTypeKey] ||
      data.sectionType;

    // Extract IDs from selected options
    const competencyIds = [
      ...(data.technicalCompetencies?.map((comp) => parseInt(comp.value)) ||
        []),
      ...(data.softCompetencies?.map((comp) => parseInt(comp.value)) || []),
    ];

    const categoryIds =
      data.categories?.map((cat) => parseInt(cat.value)) || [];
    const levelIds = data.level?.map((level) => parseInt(level.value)) || [];
    const questionIds = data.questionId?.map((id) => parseInt(id)) || [];

    const payload: ICreateSectionBody = {
      section_name: data.sectionName,
      section_type: sectionType,
      duration: duration > 0 ? duration : undefined,
      competency_id: competencyIds.length > 0 ? competencyIds : [],
      category_id: categoryIds.length > 0 ? categoryIds : [],
      level_id: levelIds.length > 0 ? levelIds : [],
      question_id: questionIds.length > 0 ? questionIds : [],
    };

    // Add type-specific fields
    if (["video", "audio", "document"].includes(data.sectionType)) {
      if (data.materialId) {
        payload.material_repository_id = parseInt(data.materialId);
      }
      if (data.materialLink) {
        payload.material_link = data.materialLink;
      }
      payload.passing_grade = 0;
    }

    if (
      [
        "quiz",
        "pre-test",
        "post-test isian",
        "post-test pilihan ganda",
      ].includes(data.sectionType)
    ) {
      if (data.passingGrade !== undefined) {
        payload.passing_grade = data.passingGrade;
      }
      if (data.numberOfQuestions !== undefined) {
        payload.number_of_question = data.numberOfQuestions;
      }
      if (data.withTestTimer !== undefined) {
        payload.with_test_timer = data.withTestTimer === "yes";
      }
    }

    if (data.sectionType === "assignment" && data.assignmentInstruction) {
      payload.assignment_instruction = data.assignmentInstruction;
    }

    return payload;
  };

  const onSubmit = async (data: AddSectionFormData) => {
    try {
      // Validate with dynamic schema
      const currentSchema = createDynamicSchema(data.sectionType);
      const validationResult = currentSchema.safeParse(data);

      if (!validationResult.success) {
        console.error("Validation errors:", validationResult.error.issues);
        // Handle validation errors - you might want to show them to the user
        validationResult.error.issues.forEach((issue) => {
          console.error(`${issue.path.join(".")}: ${issue.message}`);
        });
        return;
      }

      // Transform data to API payload
      const payload = transformFormDataToPayload(validationResult.data);
      console.log("Submitting payload:", payload);

      // Submit to API
      const result = await createSectionMutation.mutateAsync(payload);
      console.log("Section created successfully:", result);
    } catch (error) {
      notifyHotError("Error creating section. Please try again.");
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const showPreview =
    ["video", "audio", "document"].includes(sectionType) && watch("materialId");

  return (
    <div className="min-h-screen">
      <div>
        {/* Header */}
        <div className="flex items-center gap-4 mb-5">
          <button
            onClick={handleCancel}
            className="flex items-center justify-center w-10 h-10 rounded-lg bg-white"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </button>
          <h1 className="text-base font-bold text-gray-900 bg-white p-3 rounded-lg w-full">
            Add New Section
          </h1>
        </div>
        <FormProvider {...form}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="space-y-5 bg-white p-5 rounded-lg">
              {/* Section Information */}
              <SectionInformation
                control={control}
                errors={errors}
                form={form}
              />
              {/* Section Preview */}

              <SectionPreview
                sectionType={sectionType}
                control={control}
                errors={errors}
              />

              {/* Related Competencies */}
              <RelatedCompetencies />
              {/* Section Duration */}
              <SectionDuration control={control} errors={errors} />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-4 py-4 bg-white mt-4 -mx-8 w-[calc(100%+48px)]">
              <BaseButton
                type="button"
                variant="outline"
                onClick={handleCancel}
                className="px-4 py-3 text-sm"
              >
                Cancel
              </BaseButton>
              <BaseButton
                type="submit"
                className="px-4 py-3 text-sm bg-fill-brand-primary"
              >
                Add New Section
              </BaseButton>
            </div>
          </form>
        </FormProvider>
      </div>
    </div>
  );
};

export default AddNewSection;
