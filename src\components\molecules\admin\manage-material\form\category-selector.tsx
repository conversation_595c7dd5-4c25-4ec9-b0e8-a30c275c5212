"use client";

import { BaseLabel } from "@/components/atoms/label";
import { Controller, useFormContext } from "react-hook-form";
import MultipleSelector from "@/components/atoms/multiselect";
import { useGetCategoryListQuery } from "@/services/query/admin/master";
import { useMemo } from "react";
import { cn } from "@/lib/utils";
interface CategorySelectorProps {
  labelClassName?: string;
}

const CategorySelector = ({ labelClassName }: CategorySelectorProps) => {
  const form = useFormContext();
  const selectedCategories = form.watch("categories");
  const { data: categories, isPending: pendingCategories } =
    useGetCategoryListQuery();

  const categoriesOptions = useMemo(
    () =>
      categories?.data?.map((category) => ({
        label: category.category_name ?? "",
        value: category.id.toString(),
      })) ?? [],
    [categories]
  );

  const handleSelectAllCategories = () => {
    const isAllSelected =
      form.watch("categories").length === categoriesOptions?.length;
    form.setValue(
      "categories",
      isAllSelected
        ? []
        : categoriesOptions?.map((cat) => ({
            value: cat.value,
            label: cat.label,
          })) ?? []
    );
  };

  return (
    <div className="space-y-3">
      <BaseLabel className={cn("text-sm font-medium", labelClassName)}>
        Category
      </BaseLabel>

      <Controller
        name="categories"
        control={form.control}
        render={({ field }) => (
          <MultipleSelector
            isPending={pendingCategories}
            value={field.value}
            options={categoriesOptions ?? []}
            onChange={field.onChange}
            placeholder="Select Category"
            badgeClassName="bg-base-gray-20 text-comp-content-primary"
            loadingIndicator={
              <p className="py-2 text-center text-sm leading-10 text-muted-foreground">
                loading...
              </p>
            }
            emptyIndicator={
              <p className="w-full text-center text-lg leading-10 text-muted-foreground">
                no results found.
              </p>
            }
          />
        )}
      />
      {form.formState.errors.categories && (
        <p className="text-red-500 text-sm mt-1">
          {form.formState.errors.categories?.message as string}
        </p>
      )}
      {/* Select All Checkbox */}
      <label className="flex items-center gap-2 cursor-pointer w-fit">
        <input
          type="checkbox"
          checked={selectedCategories.length === categoriesOptions.length}
          onChange={handleSelectAllCategories}
          className="w-4 h-4 text-orange-500 border-gray-300 rounded focus:ring-orange-500"
        />
        <span className={cn("text-xs text-gray-700")}>Select All Category</span>
      </label>
    </div>
  );
};

export default CategorySelector;
