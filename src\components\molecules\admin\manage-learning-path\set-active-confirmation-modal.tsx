import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { DialogClose } from "@/components/ui/dialog";
import { useSetActiveLearningCodeMutation } from "@/services/mutation/learning-path/set-active";

import { useUpdateLearningLevelMutation } from "@/services/mutation/learning-path/update";
import {
  useGetLearningCodeDetailQuery,
  useGetLearningLevelDetailQuery,
} from "@/services/query/learning-path/detail";
import {
  useGetLearningCodeListQuery,
  useGetLearningLevelListQuery,
} from "@/services/query/learning-path/list";
import { useManageLearningPathFilterStore } from "@/store/admin/manage-learning-path/filter";
import { useManageLearningPathModalStore } from "@/store/admin/manage-learning-path/modal";
import { useManageLearningPathTabStore } from "@/store/admin/manage-learning-path/tab";
import { CircleAlert } from "lucide-react";
import React from "react";
import toast from "react-hot-toast";
import { useShallow } from "zustand/react/shallow";

const ManageLearningPathSetActiveConfirmationModal = () => {
  const activeTab = useManageLearningPathTabStore((state) => state.activeTab);

  const { openSetActiveModal, setOpenSetActiveModal } =
    useManageLearningPathModalStore(
      useShallow(({ openSetActiveModal, setOpenSetActiveModal }) => ({
        openSetActiveModal,
        setOpenSetActiveModal,
      }))
    );

  const { currentData, learningCodeQuery, learningLevelQuery, setCurrentData } =
    useManageLearningPathFilterStore(
      useShallow(
        ({
          currentData,
          learningCodeQuery,
          learningLevelQuery,
          setCurrentData,
        }) => ({
          currentData,
          learningCodeQuery,
          learningLevelQuery,
          setCurrentData,
        })
      )
    );

  const learningCodes = useGetLearningCodeListQuery(
    learningCodeQuery,
    activeTab === "learning-code"
  );
  const learningLevels = useGetLearningLevelListQuery(
    learningLevelQuery,
    activeTab === "learning-level"
  );
  const setActiveLearningCode = useSetActiveLearningCodeMutation();
  const updateLearningLevel = useUpdateLearningLevelMutation();

  const handleSetActiveData = () => {
    if (currentData) {
      if (activeTab === "learning-code") {
        setActiveLearningCode.mutate(
          {
            params: {
              id: currentData,
            },
            status:
              learningCodeDetail.data?.data.status === "active"
                ? "inactive"
                : "active",
          },
          {
            onSuccess: (data) => {
              handleOpenChange(false);
              learningCodes.refetch();
              toast.success(data.message);
            },
            onError: (data) => {
              toast.error(data.message);
            },
          }
        );
      } else {
        updateLearningLevel.mutate(
          {
            params: {
              id: currentData,
            },
            body: {
              status:
                learningLevelDetail.data?.data.status === "active"
                  ? "inactive"
                  : "active",
            },
          },
          {
            onSuccess: (data) => {
              handleOpenChange(false);
              learningLevels.refetch();
              toast.success(data.message);
            },
            onError: (data) => {
              toast.error(data.message);
            },
          }
        );
      }
    }
  };

  const learningCodeDetail = useGetLearningCodeDetailQuery({
    id: activeTab === "learning-code" ? currentData : null,
  });
  const learningLevelDetail = useGetLearningLevelDetailQuery({
    id: activeTab === "learning-level" ? currentData : null,
  });

  const handleOpenChange = (state: boolean) => {
    if (!state) {
      setCurrentData(null);
    }

    setOpenSetActiveModal(state);
  };

  return (
    <BaseDialog open={openSetActiveModal} onOpenChange={handleOpenChange}>
      <BaseDialogContent className="h-fit min-w-4/12" showCloseButton={false}>
        <BaseDialogHeader>
          <div className="bg-orange-200 w-fit p-2 rounded-full border-8 border-orange-100 bg">
            <CircleAlert className="text-orange-400" size={28} />
          </div>
          <BaseDialogTitle className="flex flex-col gap-4 text-xl mt-3">
            Update status{" "}
            {activeTab === "learning-code" ? "Learning Code" : "Learning Level"}
            ?
          </BaseDialogTitle>
          <BaseDialogDescription className="text-gray-500 text-sm -mt-1">
            Status{" "}
            {activeTab === "learning-code" ? "Learning Code" : "Learning Level"}{" "}
            yang dipilih akan diubah statusnya
          </BaseDialogDescription>
        </BaseDialogHeader>
        <div className="flex justify-end gap-3 mt-4">
          <DialogClose asChild>
            <BaseButton className="w-34 h-11" variant={"outline"}>
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            className="w-34 h-11"
            onClick={() => handleSetActiveData()}
            disabled={
              activeTab === "learning-code"
                ? learningCodeDetail.isLoading
                : learningLevelDetail.isLoading
            }
          >
            Update
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default ManageLearningPathSetActiveConfirmationModal;
