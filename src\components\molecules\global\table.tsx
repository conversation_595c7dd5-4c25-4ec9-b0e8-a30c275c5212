'use client';

import {
  BasePagination,
  BasePaginationContent,
  BasePaginationEllipsis,
  BasePaginationItem,
  BasePaginationLink,
  BasePaginationNext,
  BasePaginationPrevious,
} from '@/components/atoms/pagination';
import {
  BaseTable,
  BaseTableBody,
  BaseTableCell,
  BaseTableHead,
  BaseTableHeader,
  BaseTableRow,
} from '@/components/atoms/table';
import { IGlobalPaginationDto } from '@/interfaces/global/pagination';
import { cn } from '@/lib/utils';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { useMemo } from 'react';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  pagination?: IGlobalPaginationDto;
  onPageChange?: (page: number) => void;
  containerClassName?: string;
  containerTableClassName?: string;
  tableClassName?: string;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  pagination,
  onPageChange = () => {},
  containerClassName,
  containerTableClassName,
  tableClassName,
}: Readonly<DataTableProps<TData, TValue>>) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return (
    <div className={cn('w-full space-y-4', containerClassName)}>
      <div
        className={cn(
          'rounded-md w-full overflow-hidden p-2 bg-white',
          containerTableClassName
        )}
      >
        <BaseTable className={cn('w-full table-auto', tableClassName)}>
          <BaseTableHeader className="bg-white">
            {table.getHeaderGroups().map((headerGroup) => (
              <BaseTableRow
                key={headerGroup.id}
                className="hover:bg-white"
              >
                {headerGroup.headers.map((header) => (
                  <BaseTableHead
                    key={header.id}
                    className={cn(
                      header.column.columnDef.meta?.className,
                      'text-left'
                    )}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </BaseTableHead>
                ))}
              </BaseTableRow>
            ))}
          </BaseTableHeader>
          <BaseTableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, rowIndex) => (
                <BaseTableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className={cn(
                    rowIndex % 2 === 0 ? 'bg-[#FAFAFA]' : 'bg-[#FFFFFF]'
                  )}
                >
                  {row.getVisibleCells().map((cell) => (
                    <BaseTableCell
                      key={cell.id}
                      className={cn(
                        cell.column.columnDef.meta?.className,
                        'truncate'
                      )}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </BaseTableCell>
                  ))}
                </BaseTableRow>
              ))
            ) : (
              <BaseTableRow>
                <BaseTableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </BaseTableCell>
              </BaseTableRow>
            )}
          </BaseTableBody>
        </BaseTable>
      </div>
      {pagination && (
        <DataTablePagination
          pagination={pagination}
          setPage={onPageChange}
        />
      )}
    </div>
  );
}

type PageItem = number | 'ellipsis';

function getDisplayPages(current: number, total: number): PageItem[] {
  if (total <= 5) return Array.from({ length: total }, (_, i) => i + 1);

  const pages: PageItem[] = [1];
  const start = Math.max(2, current - 1);
  const end = Math.min(total - 1, current + 1);

  if (start > 2) pages.push('ellipsis');
  for (let p = start; p <= end; p++) pages.push(p);
  if (end < total - 1) pages.push('ellipsis');

  pages.push(total);
  return pages;
}

export const DataTablePagination = ({
  pagination,
  setPage,
}: {
  pagination?: IGlobalPaginationDto;
  setPage: (page: number) => void;
}) => {
  const current = Math.max(1, pagination?.current_page ?? 1);
  const total = Math.max(1, pagination?.total_page ?? 1);

  const pages = useMemo(
    () => getDisplayPages(current, total),
    [current, total]
  );
  const hasPrev = current > 1;
  const hasNext = current < total;
  const disabledCls = 'pointer-events-none opacity-60';

  return (
    <div className="flex justify-between items-center mt-4">
      <span className="text-sm whitespace-nowrap">
        {getPaginationSummary(pagination)}
      </span>

      <BasePagination className="flex justify-end">
        <BasePaginationContent>
          <BasePaginationItem>
            <BasePaginationPrevious
              className={cn(
                'cursor-pointer hover:bg-stone-200',
                !hasPrev && disabledCls
              )}
              onClick={() => hasPrev && setPage(current - 1)}
            />
          </BasePaginationItem>

          {pages.map((it, idx) =>
            it === 'ellipsis' ? (
              <BasePaginationItem key={`page-${idx + 1}`}>
                <BasePaginationEllipsis />
              </BasePaginationItem>
            ) : (
              <BasePaginationItem key={it}>
                <BasePaginationLink
                  isActive={it === current}
                  className="cursor-pointer hover:bg-stone-200"
                  onClick={() => it !== current && setPage(it)}
                  aria-current={it === current ? 'page' : undefined}
                >
                  {it}
                </BasePaginationLink>
              </BasePaginationItem>
            )
          )}

          <BasePaginationItem>
            <BasePaginationNext
              className={cn(
                'cursor-pointer hover:bg-stone-200',
                !hasNext && disabledCls
              )}
              onClick={() => hasNext && setPage(current + 1)}
            />
          </BasePaginationItem>
        </BasePaginationContent>
      </BasePagination>
    </div>
  );
};

const getPaginationSummary = (pagination?: IGlobalPaginationDto): string => {
  if (!pagination) return `Showing 0 to 0 of 0 entries`;

  const total = Math.max(0, pagination.total_data ?? 0);

  const pageSize =
    (pagination as any).limit ??
    (pagination as any).per_page ??
    (pagination as any).page_size ??
    10;

  const current = Math.max(1, pagination.current_page ?? 1);

  const from = total === 0 ? 0 : (current - 1) * pageSize + 1;

  const to = total === 0 ? 0 : Math.min(from + pageSize - 1, total);

  return `Showing ${from} to ${to} of ${total} entries`;
};
