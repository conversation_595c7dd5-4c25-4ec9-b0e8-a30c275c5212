'use client';

import React from 'react';
import { BaseLabel } from '@/components/atoms/label';
import { CloudUpload, X } from 'lucide-react';

interface FileUploaderProps {
  title: string;
  acceptedTypes: string;
  supportedText: string;
  banner?: React.ReactNode;
  onFileUpload: (file: File | null) => void;
  maxPreviewHeight?: number;
  maxSizeBytes?: number;
}

const FileUploader = ({
  title,
  acceptedTypes,
  supportedText,
  banner,
  onFileUpload,
  maxPreviewHeight = 320,
  maxSizeBytes,
}: FileUploaderProps) => {
  const inputId = React.useId();
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [previewUrl, setPreviewUrl] = React.useState<string | null>(null);
  const [fileName, setFileName] = React.useState<string>('');
  const [error, setError] = React.useState<string | null>(null);

  const revoke = () => {
    if (previewUrl) URL.revokeObjectURL(previewUrl);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setError(null);
    revoke();

    if (!file) {
      setPreviewUrl(null);
      setFileName('');
      onFileUpload(null);
      return;
    }

    if (maxSizeBytes && file.size > maxSizeBytes) {
      setError('File size exceeds the maximum allowed.');
      if (inputRef.current) inputRef.current.value = '';
      onFileUpload(null);
      return;
    }

    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
    setFileName(file.name);
    onFileUpload(file);
  };

  const handleRemove = () => {
    revoke();
    setPreviewUrl(null);
    setFileName('');
    setError(null);
    onFileUpload(null);
    if (inputRef.current) inputRef.current.value = '';
  };

  return (
    <div className="space-y-3">
      <BaseLabel className="text-sm font-medium">{title}</BaseLabel>

      {banner}

      {!previewUrl ? (
        <label
          htmlFor={inputId}
          className="block w-full cursor-pointer border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
        >
          <div className="flex items-center justify-center gap-3">
            <CloudUpload
              className="text-[#C0C0C0]"
              size={44}
            />
            <div className="space-y-2">
              <p className="text-gray-600 text-sm">
                <span className="font-medium">Drag &amp; drop</span> your files
                or{' '}
                <span className="text-orange-500 hover:text-orange-600 font-medium underline">
                  browse
                </span>
              </p>
            </div>
          </div>
        </label>
      ) : (
        <div className="rounded-lg border bg-white p-2 relative">
          <button
            type="button"
            onClick={handleRemove}
            className="absolute top-2 right-2 inline-flex items-center gap-1 rounded-md bg-white/90 border px-2 py-1 text-xs hover:bg-white"
            aria-label="Remove selected file"
          >
            <X size={14} /> Remove
          </button>
          <img
            src={previewUrl}
            alt={fileName || 'Uploaded preview'}
            className="w-full object-contain"
            style={{ maxHeight: maxPreviewHeight }}
          />
        </div>
      )}

      <input
        id={inputId}
        ref={inputRef}
        type="file"
        accept={acceptedTypes}
        onChange={handleChange}
        className="sr-only"
      />

      {fileName && !error ? (
        <p className="text-xs text-gray-500">Selected: {fileName}</p>
      ) : null}
      {error ? <p className="text-xs text-red-600">{error}</p> : null}

      <p className="text-sm text-gray-500">{supportedText}</p>
    </div>
  );
};

export default FileUploader;
