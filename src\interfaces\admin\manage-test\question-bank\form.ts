import * as yup from 'yup';

export type THowToAddImage = 'select_image' | 'upload_image';
export type TQuestionTypeUI = 'pilihan_ganda' | 'benar_salah' | 'isian';

export const createQuestionBankBodySchema = yup.object({
  id: yup.string().optional(),

  howToAddQuestionImage: yup
    .mixed<'select_image' | 'upload_image'>()
    .oneOf(['select_image', 'upload_image'])
    .required(),

  imageFromRepository: yup.string().when('howToAddQuestionImage', {
    is: 'select_image',
    then: (s) => s.required('Please select image from repository'),
    otherwise: (s) => s.strip(),
  }),

  imageName: yup.string().when('howToAddQuestionImage', {
    is: 'upload_image',
    then: (s) => s.required('Image name is required'),
    otherwise: (s) => s.strip(),
  }),

  image: yup.mixed<File>().when('howToAddQuestionImage', {
    is: 'upload_image',
    then: (s) => s.required('Please upload an image'),
    otherwise: (s) => s.strip(),
  }),

  category: yup
    .array(
      yup.object({
        label: yup.string().required(),
        value: yup.string().required(),
      })
    )
    .min(1, 'Category is required')
    .required(),

  level: yup
    .array(
      yup.object({
        label: yup.string(),
        value: yup.string(),
      })
    )
    .min(1, 'Level is required')
    .required(),

  questionType: yup
    .mixed<'pilihan_ganda' | 'benar_salah' | 'isian'>()
    .oneOf(['pilihan_ganda', 'benar_salah', 'isian'])
    .required(),

  question: yup.string().required('Question is required'),

  option: yup.array(yup.string()).when('questionType', {
    is: 'pilihan_ganda',
    then: (s) => s.of(yup.string().required()).min(2).max(4).required(),
    otherwise: (s) => s.strip(),
  }),

  keyAnswer: yup
    .string()
    .when(['questionType'], ([qt], schema: yup.StringSchema) => {
      if (qt === 'pilihan_ganda') {
        return schema
          .oneOf(['a', 'b', 'c', 'd'])
          .required('Key answer is required');
      }
      if (qt === 'benar_salah') {
        return schema
          .oneOf(['benar', 'salah'])
          .required('Key answer is required');
      }
      return schema.trim().min(1, 'Key answer is required').required();
    }),

  feature: yup.mixed<'OnlineLearning' | 'InClassTraining'>().optional(),
  imagePath: yup.string().optional().strip(false),
});

export interface ICreateQuestionBankBody
  extends yup.InferType<typeof createQuestionBankBodySchema> {}
