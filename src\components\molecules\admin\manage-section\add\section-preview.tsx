"use client";

import React, { useMemo } from "react";
import { Control, FieldErrors, useFormContext } from "react-hook-form";
import { AddSectionFormData } from "@/components/organisms/admin/manage-section/add-new-section";
import VideoPreview from "@/components/molecules/admin/manage-material/preview/video-preview";
import AudioPreview from "@/components/molecules/admin/manage-material/preview/audio-preview";
import DocumentPreview from "@/components/molecules/admin/manage-material/document-preview";
import QuizTable from "./quiz-table";
import { Textarea } from "@/components/ui/textarea";
import { useGetMaterialRepositoryListQuery } from "@/services/query/admin/master";

interface SectionPreviewProps {
  sectionType:
    | "video"
    | "audio"
    | "document"
    | "quiz"
    | "pre-test"
    | "post-test isian"
    | "post-test pilihan ganda"
    | "assignment";
  control: Control<AddSectionFormData>;
  errors: FieldErrors<AddSectionFormData>;
}

const SectionPreview = ({
  sectionType,
  control,
  errors,
}: SectionPreviewProps) => {
  const form = useFormContext<AddSectionFormData>();
  const materialId = form.watch("materialId");

  // Fetch materials to get the selected material data
  const { data: materials } = useGetMaterialRepositoryListQuery({
    order_by: "name",
  });

  // Find the selected material
  const selectedMaterial = useMemo(() => {
    if (!materialId || !materials?.data) return null;
    return (
      materials.data.find(
        (material) => material.id.toString() === materialId
      ) || null
    );
  }, [materialId, materials]);

  const preview = ["video", "audio", "document"].includes(sectionType)
    ? materialId
    : true;
  const renderPreviewContent = () => {
    // Convert material repository data to the format expected by preview components
    const materialForPreview = selectedMaterial
      ? {
          id: selectedMaterial.id,
          name: selectedMaterial.name,
          type: selectedMaterial.type,
          file_format: selectedMaterial.file_format,
          link: selectedMaterial.link,
          // Add other required fields with defaults
          levels: [],
          categories: [],
          associated: [],
          filesize: 0,
          feature: "",
          created_at: new Date(),
          created_by: "",
          updated_by: "",
          last_updated: new Date(),
        }
      : null;

    switch (sectionType) {
      case "video":
        return (
          <div className="h-[400px]">
            <VideoPreview material={materialForPreview} />
          </div>
        );
      case "audio":
        return (
          <div className="h-[400px]">
            <AudioPreview material={materialForPreview} />
          </div>
        );
      case "document":
        return (
          <div className="h-[400px]">
            <DocumentPreview material={materialForPreview} />
          </div>
        );
      case "quiz":
      case "pre-test":
      case "post-test isian":
      case "post-test pilihan ganda":
        return <QuizTable control={control} errors={errors} />;
      case "assignment":
        return (
          <div>
            <Textarea
              className="w-full h-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter assignment description"
              rows={4}
              maxLength={500}
              onChange={(e) => {
                if (e.target.value.length <= 500) {
                  form.setValue("assignmentInstruction", e.target.value);
                }
              }}
            />
            <p className="text-xs text-gray-500 mt-1 text-right">
              {form.watch("assignmentInstruction")?.length ?? 0}/500
            </p>
          </div>
        );
      default:
        return null;
    }
  };

  const titlePreview = () => {
    switch (sectionType) {
      case "video":
        return "Video Preview";
      case "audio":
        return "Audio Preview";
      case "document":
        return "Document Preview";
      case "pre-test":
        return "Pre-test Question";
      case "post-test isian":
        return "Post-test isian Question";
      case "post-test pilihan ganda":
        return "Post-test pilihan ganda Question";
      case "quiz":
        return "Quiz Question";
      case "assignment":
        return "Assignment Instruction";
      default:
        return "Preview";
    }
  };

  return (
    <div className="bg-white">
      <h2 className="text-sm font-semibold text-comp-content-primary mb-3">
        {titlePreview()}
      </h2>
      {preview ? (
        <div className="w-full overflow-hidden">{renderPreviewContent()}</div>
      ) : (
        <div className="w-full h-[200px] flex flex-col justify-center items-center bg-gray-100 rounded-lg">
          <p className="text-center">No Materials selected.</p>
          <p className="text-center">Please select the Materials first</p>
        </div>
      )}
    </div>
  );
};

export default SectionPreview;
