"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { Check } from "lucide-react";
import { cn } from "@/utils/common";
import { useShallow } from "zustand/react/shallow";
import { IGetJobPositionListResponse } from "@/interfaces/admin/manage-job/list";
import { useManageJobFilterStore } from "@/store/admin/manage-job/filter";
import { useGetJobPositionListQuery } from "@/services/query/manage-job/list";
import ManageJobTableHeader from "./table-header";
import { DataTable } from "@/components/molecules/global/table";
import { useFormContext } from "react-hook-form";
import { Checkbox } from "@/components/ui/checkbox";
import { ICreateLearningCodeForm } from "@/interfaces/admin/manage-learning-path/new";
import { useManageLearningPathFilterStore } from "@/store/admin/manage-learning-path/filter";

const AddLearningCodeTable = () => {
  const { query, setQuery } = useManageJobFilterStore(
    useShallow(({ query, setQuery }) => ({
      query,
      setQuery,
    }))
  );

  const { selectedJobPositions, setSelectedJobPositions } =
    useManageLearningPathFilterStore(
      useShallow(({ setSelectedJobPositions, selectedJobPositions }) => ({
        setSelectedJobPositions,
        selectedJobPositions,
      }))
    );

  const jobPositions = useGetJobPositionListQuery(query);

  const form = useFormContext<ICreateLearningCodeForm>();

  const jobNameIds = form.watch("job_name_id") ?? [];
  const currentItems = jobPositions.data?.data ?? [];
  const currentIds = currentItems.map((it) => it.id);

  const allChecked =
    currentIds.length > 0 && currentIds.every((id) => jobNameIds.includes(id));

  const dedupById = (items: IGetJobPositionListResponse[]) => {
    const map = new Map<number, IGetJobPositionListResponse>();
    for (const it of items) map.set(it.id, it);
    return Array.from(map.values());
  };

  const handleCheckAll = () => {
    if (allChecked) {
      // unselect hanya item di halaman ini
      const nextIds = jobNameIds.filter(
        (id) => !currentIds.includes(id as number)
      );
      const nextSelected = selectedJobPositions.filter(
        (item) => !currentIds.includes(item.id)
      );

      form.setValue("job_name_id", nextIds, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
      });
      setSelectedJobPositions(nextSelected);
    } else {
      // select semua di halaman ini (merge + dedup)
      const nextIds = Array.from(
        new Set<number>([...(jobNameIds as number[]), ...currentIds])
      );
      const nextSelected = dedupById([
        ...selectedJobPositions,
        ...currentItems,
      ]);

      form.setValue("job_name_id", nextIds, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
      });
      setSelectedJobPositions(nextSelected);
    }
  };

  const columns: ColumnDef<IGetJobPositionListResponse>[] = [
    {
      accessorKey: "id",
      header: () => (
        <Checkbox
          className="mx-3"
          checked={allChecked}
          onCheckedChange={handleCheckAll}
        />
      ),
      cell({ row }) {
        const id = row.original.id;
        const checked = jobNameIds.includes(id);

        return (
          <Checkbox
            className="mx-3"
            checked={checked}
            onCheckedChange={(value) => {
              // value bisa true | false | "indeterminate"
              const nextChecked = value === true || value === "indeterminate";
              if (nextChecked) {
                // tambah id jika belum ada
                if (!checked) {
                  setSelectedJobPositions([
                    ...selectedJobPositions,
                    row.original,
                  ]);
                  form.setValue("job_name_id", [...jobNameIds, id], {
                    shouldDirty: true,
                    shouldTouch: true,
                    shouldValidate: true,
                  });
                }
              } else {
                // hapus id
                form.setValue(
                  "job_name_id",
                  jobNameIds.filter((x) => x !== id),
                  { shouldDirty: true, shouldTouch: true, shouldValidate: true }
                );
                setSelectedJobPositions(
                  selectedJobPositions.filter((x) => x.id !== id)
                );
              }
            }}
          />
        );
      },
    },

    {
      accessorKey: "job_id",
      header: "Job Position ID",
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.job_id}
          </span>
        );
      },
    },
    { accessorKey: "job_name", header: "Job Position Name" },
    { accessorKey: "job_position_type", header: "Job Type" },
    { accessorKey: "department_name", header: "Department" },
    { accessorKey: "job_function", header: "Job Function" },
    {
      accessorKey: "level",
      header: "Starting Learning Level",
    },
    {
      accessorKey: "is_need_neop",
      header: "Need NEOP?",
      cell({ row }) {
        return (
          <Check
            size={24}
            className={cn(
              row.original.is_need_neop
                ? "bg-orange-400 text-white"
                : "bg-gray-400 text-white",
              "rounded-full p-1"
            )}
            strokeWidth={3}
            absoluteStrokeWidth
          />
        );
      },
    },
    {
      accessorKey: "is_need_welcoming_kit",
      header: "Need Welcoming Kit?",
      cell({ row }) {
        return (
          <Check
            size={24}
            className={cn(
              row.original.is_need_welcoming_kit
                ? "bg-orange-400 text-white"
                : "bg-gray-400 text-white",
              "rounded-full p-1"
            )}
            strokeWidth={3}
            absoluteStrokeWidth
          />
        );
      },
    },
    {
      accessorKey: "starter_module_priority",
      header: "Starter Module Priority",
    },
    { accessorKey: "entity_name", header: "Entity" },

    {
      accessorKey: "last_updated",
      header: "Last Updated",
      cell: (props) => {
        return (
          <span>
            {props.row.original.last_updated
              ? dayjs(props.row.original.last_updated).format(
                  "DD MMM YYYY HH:mm"
                )
              : "-"}
          </span>
        );
      },
    },
    { accessorKey: "updated_by", header: "Updated By" },
  ];

  const handlePageChange = (page: number) => {
    setQuery({ ...query, page });
  };

  return (
    <div className="flex flex-col gap-4">
      <ManageJobTableHeader />

      <DataTable
        columns={columns}
        data={jobPositions.data?.data ?? []}
        pagination={jobPositions.data?.pagination}
        onPageChange={handlePageChange}
      />
    </div>
  );
};

export default AddLearningCodeTable;
