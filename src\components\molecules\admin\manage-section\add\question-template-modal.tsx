"use client";

import { useState, useEffect } from "react";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseButton } from "@/components/atoms/button";
import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import {
  BaseTable,
  BaseTableBody,
  BaseTableCell,
  BaseTableHead,
  BaseTableHeader,
  BaseTableRow,
} from "@/components/atoms/table";
import { Search, Filter, X, Eye } from "lucide-react";
import { useGetQuestionTemplateListQuery } from "@/services/query/admin/manage-test/question-template";
import {
  IGetQuestionTemplateListResponse,
  TQTSearchBy,
} from "@/interfaces/admin/manage-test/question-template/list";

import Pagination from "../common/pagination";
import {
  useGetCategoryListQuery,
  useGetStartingLevelListQuery,
} from "@/services/query/admin/master";

interface QuestionTemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddQuestions: (questions: IGetQuestionTemplateListResponse[]) => void;
}

const searchByOptions = [
  { value: "template_name", label: "Template Name" },
  { value: "template_id", label: "Template ID" },
  { value: "question", label: "Question" },
  { value: "created_by", label: "Created By" },
  { value: "updated_by", label: "Updated By" },
];

const templateTypes = [
  { value: "quiz", label: "Quiz" },
  { value: "pre_test", label: "Pre Test" },
  { value: "post_test", label: "Post Test" },
];

const QuestionTemplateModal = ({
  isOpen,
  onClose,
  onAddQuestions,
}: QuestionTemplateModalProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchBy, setSearchBy] = useState<TQTSearchBy>("template_name");
  const [categoryFilter, setCategoryFilter] = useState<string>("");
  const [levelFilter, setLevelFilter] = useState<string>("");
  const [templateTypeFilter, setTemplateTypeFilter] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTemplate, setSelectedTemplate] =
    useState<IGetQuestionTemplateListResponse | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // API queries
  const { data: templateData, isLoading } = useGetQuestionTemplateListQuery({
    search: searchQuery || undefined,
    search_by: searchBy,
    category_id: categoryFilter ? parseInt(categoryFilter) : undefined,
    level_id: levelFilter ? parseInt(levelFilter) : undefined,
    template_type: templateTypeFilter || undefined,
    feature: "OnlineLearning",
    page: currentPage,
    limit: 10,
  });

  const { data: categoriesData } = useGetCategoryListQuery();

  const { data: levelsData } = useGetStartingLevelListQuery();

  const templates = templateData?.data || [];
  const pagination = templateData?.pagination;
  const categories = categoriesData?.data || [];
  const levels = levelsData?.data || [];

  useEffect(() => {
    if (!isOpen) {
      setSearchQuery("");
      setCategoryFilter("");
      setLevelFilter("");
      setTemplateTypeFilter("");
      setCurrentPage(1);
      setShowFilters(false);
      setSelectedTemplate(null);
      setShowPreview(false);
    }
  }, [isOpen]);

  const handleAddTemplate = (template: IGetQuestionTemplateListResponse) => {
    onAddQuestions([template]);
    onClose();
  };

  const handlePreviewTemplate = (
    template: IGetQuestionTemplateListResponse
  ) => {
    setSelectedTemplate(template);
    setShowPreview(true);
  };

  const handleClearFilters = () => {
    setSearchQuery("");
    setCategoryFilter("");
    setLevelFilter("");
    setTemplateTypeFilter("");
    setCurrentPage(1);
  };

  return (
    <BaseDialog open={isOpen} onOpenChange={onClose}>
      <BaseDialogContent className="sm:max-w-6xl sm:w-[90vw] max-h-[90vh] overflow-auto p-5 flex flex-col">
        <BaseDialogHeader className="flex flex-col items-start justify-between space-y-0">
          <BaseDialogTitle className="text-base font-medium text-comp-content-primary">
            Add Questions from Template
          </BaseDialogTitle>
          <hr className="border-gray-200 -mx-5 w-[calc(100%+40px)]" />
        </BaseDialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          {/* Search and Filter Controls */}
          <div className="space-y-3">
            {/* Search Bar */}
            <div className="flex items-center gap-2">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <BaseInput
                  placeholder="Search templates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <BaseSelect
                value={searchBy}
                onValueChange={(value) => setSearchBy(value as TQTSearchBy)}
              >
                <BaseSelectTrigger className="w-48">
                  <BaseSelectValue />
                </BaseSelectTrigger>
                <BaseSelectContent>
                  {searchByOptions.map((option) => (
                    <BaseSelectItem key={option.value} value={option.value}>
                      {option.label}
                    </BaseSelectItem>
                  ))}
                </BaseSelectContent>
              </BaseSelect>
              <BaseButton
                type="button"
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="w-4 h-4" />
                Filters
              </BaseButton>
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Advanced Filters</h4>
                  <BaseButton
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleClearFilters}
                    className="text-gray-500"
                  >
                    <X className="w-4 h-4 mr-1" />
                    Clear All
                  </BaseButton>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Category
                    </label>
                    <BaseSelect
                      value={categoryFilter}
                      onValueChange={setCategoryFilter}
                    >
                      <BaseSelectTrigger>
                        <BaseSelectValue placeholder="All Categories" />
                      </BaseSelectTrigger>
                      <BaseSelectContent>
                        <BaseSelectItem value="">All Categories</BaseSelectItem>
                        {categories.map((category) => (
                          <BaseSelectItem
                            key={category.id}
                            value={category.id.toString()}
                          >
                            {category.category_name}
                          </BaseSelectItem>
                        ))}
                      </BaseSelectContent>
                    </BaseSelect>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Level
                    </label>
                    <BaseSelect
                      value={levelFilter}
                      onValueChange={setLevelFilter}
                    >
                      <BaseSelectTrigger>
                        <BaseSelectValue placeholder="All Levels" />
                      </BaseSelectTrigger>
                      <BaseSelectContent>
                        <BaseSelectItem value="">All Levels</BaseSelectItem>
                        {levels.map((level) => (
                          <BaseSelectItem
                            key={level.id}
                            value={level.id.toString()}
                          >
                            {level.level}
                          </BaseSelectItem>
                        ))}
                      </BaseSelectContent>
                    </BaseSelect>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Template Type
                    </label>
                    <BaseSelect
                      value={templateTypeFilter}
                      onValueChange={setTemplateTypeFilter}
                    >
                      <BaseSelectTrigger>
                        <BaseSelectValue placeholder="All Types" />
                      </BaseSelectTrigger>
                      <BaseSelectContent>
                        <BaseSelectItem value="">All Types</BaseSelectItem>
                        {templateTypes.map((type) => (
                          <BaseSelectItem key={type.value} value={type.value}>
                            {type.label}
                          </BaseSelectItem>
                        ))}
                      </BaseSelectContent>
                    </BaseSelect>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Results Summary */}
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>{templates.length} templates available</span>
          </div>

          {/* Templates Table */}
          <div className="flex-1 overflow-auto border rounded-lg">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-gray-500">Loading templates...</div>
              </div>
            ) : templates.length === 0 ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-gray-500">No templates found</div>
              </div>
            ) : (
              <BaseTable>
                <BaseTableHeader>
                  <BaseTableRow>
                    <BaseTableHead className="w-20">ID</BaseTableHead>
                    <BaseTableHead>Template Name</BaseTableHead>
                    <BaseTableHead className="w-32">Type</BaseTableHead>
                    <BaseTableHead className="w-32">Questions</BaseTableHead>
                    <BaseTableHead className="w-32">Category</BaseTableHead>
                    <BaseTableHead className="w-32">Level</BaseTableHead>
                    <BaseTableHead className="w-32">Actions</BaseTableHead>
                  </BaseTableRow>
                </BaseTableHeader>
                <BaseTableBody>
                  {templates.map((template) => (
                    <BaseTableRow key={template.id}>
                      <BaseTableCell className="font-mono text-sm">
                        {template.id}
                      </BaseTableCell>
                      <BaseTableCell>
                        <div>
                          <p className="font-medium">
                            {template.question_template_name}
                          </p>
                          {/* <p className="text-sm text-gray-500">
                            {template.description}
                          </p> */}
                        </div>
                      </BaseTableCell>
                      <BaseTableCell>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {template.type}
                        </span>
                      </BaseTableCell>
                      <BaseTableCell>
                        <span className="font-medium">
                          {template.questions?.length || 0}
                        </span>
                      </BaseTableCell>
                      <BaseTableCell>
                        <div className="space-y-1">
                          {template.categories?.map((category) => (
                            <span
                              key={category.id}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                            >
                              {category.name}
                            </span>
                          ))}
                        </div>
                      </BaseTableCell>
                      <BaseTableCell>
                        <div className="space-y-1">
                          {template.levels?.map((level) => (
                            <span
                              key={level.id}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                            >
                              {level.name}
                            </span>
                          ))}
                        </div>
                      </BaseTableCell>
                      <BaseTableCell>
                        <div className="flex items-center gap-2">
                          <BaseButton
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => handlePreviewTemplate(template)}
                            className="flex items-center gap-1"
                          >
                            <Eye className="w-3 h-3" />
                            Preview
                          </BaseButton>
                          <BaseButton
                            type="button"
                            size="sm"
                            onClick={() => handleAddTemplate(template)}
                            className="bg-orange-500 hover:bg-orange-600"
                          >
                            Add
                          </BaseButton>
                        </div>
                      </BaseTableCell>
                    </BaseTableRow>
                  ))}
                </BaseTableBody>
              </BaseTable>
            )}
          </div>

          {/* Pagination */}
          {/* {pagination && (
            <Pagination
              totalItems={pagination.total}
              currentPage={currentPage}
              pageSize={pagination.limit}
              pageSizeOptions={[10, 20, 50]}
              onPageChange={setCurrentPage}
              onPageSizeChange={() => {}} // API handles page size
            />
          )} */}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <BaseButton variant="outline" onClick={onClose}>
            Cancel
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default QuestionTemplateModal;
