import { useQuery } from '@tanstack/react-query';
import {
  apiGetQuestionTemplateDetail,
  apiGetQuestionTemplateList,
} from '@/services/api/admin/manage-test/question-template';
import { IGetQuestionTemplateListQuery } from '@/interfaces/admin/manage-test/question-template/list';

export const useGetQuestionTemplateListQuery = (
  params?: IGetQuestionTemplateListQuery,
  enabled = true
) => {
  return useQuery({
    queryKey: ['question-template', 'list', params],
    queryFn: () => apiGetQuestionTemplateList(params),
    enabled,
  });
};

export const useGetQuestionTemplateDetailQuery = (
  id?: number,
  enabled = !!id
) => {
  return useQuery({
    queryKey: ['question-template', 'detail', id],
    queryFn: () => apiGetQuestionTemplateDetail(id as number),
    enabled: enabled && !!id,
  });
};
