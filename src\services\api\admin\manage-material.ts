"use server";

import {
  IGetDetailMaterialResponse,
  IGetListMaterialQuery,
  IGetListMaterialResponse,
} from "@/interfaces/admin/manage-material/list";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetListMaterials = async (params?: IGetListMaterialQuery) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetListMaterialResponse[]>
    >("cms/admin/learning/material/list", { params });

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiGetLearningMaterialUrl = async (params: { url: string }) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<{ filename: string; url: string }>
    >("cms/admin/learning/material/url", { params });
    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiInsertLearningMaterial = async (body: FormData) => {
  try {
    const response = await api.post<IGlobalResponseDto<any>>(
      "cms/admin/learning/material/insert",
      body
    );
    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiUpdateLearningMaterial = async (id: number, body: FormData) => {
  try {
    const response = await api.post<IGlobalResponseDto<any>>(
      `cms/admin/learning/material/update/${id}`,
      body
    );
    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiGetDetailLearningMaterial = async (id: string) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetDetailMaterialResponse>
    >(`cms/admin/learning/material/detail/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiDeleteMaterial = async (id: number) => {
  try {
    const response = await api.post<IGlobalResponseDto<any>>(
      `cms/admin/learning/material/delete/${id}`,
      { is_deleted: true }
    );
    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};
