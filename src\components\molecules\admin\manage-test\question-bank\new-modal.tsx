'use client';

import { BaseButton } from '@/components/atoms/button';
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from '@/components/atoms/dialog';
import { BaseInput } from '@/components/atoms/input';
import { BaseLabel } from '@/components/atoms/label';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/atoms/select';
import { BaseSeparator } from '@/components/atoms/separator';
import { DialogClose } from '@/components/ui/dialog';
import { IGetQuestionBankListResponse } from '@/interfaces/admin/manage-test/question-bank/list';
import { useQuestionBankModal } from '@/store/admin/manage-test/question-bank/modal';
import { yupResolver } from '@hookform/resolvers/yup';
import React, { useEffect, useState } from 'react';
import { useForm, useFormContext, Path, FormProvider } from 'react-hook-form';
import { useShallow } from 'zustand/react/shallow';
import { Option } from '@/components/atoms/multiselect';
import {
  createQuestionBankBodySchema,
  ICreateQuestionBankBody,
  TQuestionTypeUI,
} from '@/interfaces/admin/manage-test/question-bank/form';
import FileUploader from '../common/file-uploader';
import { Info } from 'lucide-react';
import MultipleSelectorComponent from '@/components/atoms/multiple-selector';
import {
  useGetCategoryListQuery,
  useGetMasterImageRepository,
  useGetStartingLevelListQuery,
} from '@/services/query/admin/master';
import {
  useCreateQuestionBankMutation,
  useUpdateQuestionBankMutation,
} from '@/services/mutation/admin/manage-test/question-bank';
import { buildCreateQuestionBankPayload } from '@/store/admin/manage-test/question-bank/mapper';
import ImageRepoCombobox, { ImageRepoOption } from './image-repo-combobox';
import { useGetFileQuery } from '@/services/query/file/get';
import { bufferToFile } from '@/utils/common/file';
import { useGetQuestionBankDetailQuery } from '@/services/query/admin/manage-test/question-bank';

const QuestionBankNewModal = () => {
  const {
    openedQuestionBank,
    openAddModal,
    setOpenAddModal,
    setOpenedQuestionBank,
  } = useQuestionBankModal(
    useShallow(
      ({
        openedQuestionBank,
        openAddModal,
        setOpenAddModal,
        setOpenedQuestionBank,
      }) => ({
        openedQuestionBank,
        openAddModal,
        setOpenAddModal,
        setOpenedQuestionBank,
      })
    )
  );

  const handleOpenChangeModal = (state: boolean) => {
    if (!state) {
      setOpenedQuestionBank(null);
    }

    setOpenAddModal(state);
  };

  return (
    <BaseDialog
      open={openAddModal}
      onOpenChange={handleOpenChangeModal}
    >
      <BaseDialogContent className="sm:max-w-[800px] overflow-y-auto max-h-[95%]">
        <BaseDialogHeader>
          <BaseDialogTitle className="flex flex-col gap-4">
            <span>{openedQuestionBank?.id ? 'Edit' : 'Add New'} Question</span>
            <BaseSeparator />
          </BaseDialogTitle>
        </BaseDialogHeader>
        <span className="font-medium">Question Image (Optional)</span>
        <NewQuestionBankForm
          isOpen={openAddModal}
          data={openedQuestionBank}
          onCloseModal={() => handleOpenChangeModal(false)}
        />
      </BaseDialogContent>
    </BaseDialog>
  );
};

interface IFormProps {
  isOpen: boolean;
  data: IGetQuestionBankListResponse | null;
  onCloseModal: VoidFunction;
}

const QUESTION_TYPE_OPTION: Option[] = [
  { value: 'pilihan_ganda', label: 'Pilihan Ganda' },
  { value: 'benar_salah', label: 'Benar Salah' },
  { value: 'isian', label: 'Isian' },
];

const ADD_QUESTION_IMAGE_OPTION: Option[] = [
  { value: 'select_image', label: 'Select Image from Repository' },
  { value: 'upload_image', label: 'Upload New Image' },
];

const KEY_ANSWER_OPTION: Option[] = [
  { value: 'a', label: 'A' },
  { value: 'b', label: 'B' },
  { value: 'c', label: 'C' },
  { value: 'd', label: 'D' },
];

const KEY_TRUE_FALSE_OPTION: Option[] = [
  { value: 'benar', label: 'Benar' },
  { value: 'salah', label: 'Salah' },
];

const NewQuestionBankForm = ({ data, isOpen, onCloseModal }: IFormProps) => {
  const form = useForm({
    resolver: yupResolver(createQuestionBankBodySchema),
    defaultValues: {
      questionType: 'pilihan_ganda',
      howToAddQuestionImage: 'select_image',
      keyAnswer: '',
      category: [],
      level: [],
    },
  });

  const [searchTerm, setSearchTerm] = useState('');
  const [debounced, setDebounced] = useState('');
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  useEffect(() => {
    const t = setTimeout(() => setDebounced(searchTerm), 350);
    return () => clearTimeout(t);
  }, [searchTerm]);

  const createQB = useCreateQuestionBankMutation();
  const updateQB = useUpdateQuestionBankMutation();

  const detailQuery = useGetQuestionBankDetailQuery(data?.id, !!data?.id);

  const masterStartingLearningLevels = useGetStartingLevelListQuery({
    order_by: 'name',
    order: 'asc',
  });
  const masterCategory = useGetCategoryListQuery();
  const masterImageRepo = useGetMasterImageRepository(
    {
      search: debounced,
    },
    true
  );

  const levelOptions: Option[] =
    masterStartingLearningLevels?.data?.data?.map?.((it: any) => ({
      value: String(it?.id ?? ''),
      label: String(it?.name ?? ''),
    })) ?? [];

  const categoryOptions: Option[] =
    masterCategory?.data?.data?.map?.((it: any) => ({
      value: String(it?.id ?? ''),
      label: String(it?.category_name ?? ''),
    })) ?? [];

  const imageRepoOptions: ImageRepoOption[] =
    masterImageRepo?.data?.data?.map?.((it: any) => ({
      value: String(it?.id ?? ''),
      label: String(it?.image_name ?? `Image #${it?.id}`),
      link: it?.link ?? '',
    })) ?? [];

  const selectedRepoPath = React.useMemo(() => {
    const id = form.watch('imageFromRepository');
    const found = imageRepoOptions.find((o) => o.value === id);
    return found?.link || '';
  }, [form.watch('imageFromRepository'), imageRepoOptions]);

  const repoFile = useGetFileQuery({ path: selectedRepoPath });

  const howMode = form.watch('howToAddQuestionImage');

  useEffect(() => {
    if (howMode === 'select_image') {
      form.setValue('image', undefined as any, { shouldValidate: true });
      form.setValue('imageName', '', { shouldValidate: true });
    } else if (howMode === 'upload_image') {
      form.setValue('imageFromRepository', '', { shouldValidate: true });
    }
  }, [howMode]);

  useEffect(() => {
    const guessMimeFromName = (name: string) => {
      const n = name.toLowerCase();
      if (n.endsWith('.png')) return 'image/png';
      if (n.endsWith('.jpg') || n.endsWith('.jpeg')) return 'image/jpeg';
      if (n.endsWith('.webp')) return 'image/webp';
      if (n.endsWith('.svg')) return 'image/svg+xml';
      return 'image/png';
    };

    if (repoFile?.data && selectedRepoPath) {
      const mime = guessMimeFromName(selectedRepoPath);
      const file = bufferToFile(repoFile.data, selectedRepoPath, mime);
      const url = URL.createObjectURL(file);

      setPreviewUrl((prev) => {
        if (prev?.startsWith('blob:')) URL.revokeObjectURL(prev);
        return url;
      });
    }

    return () => {
      setPreviewUrl((prev) => {
        if (prev?.startsWith('blob:')) URL.revokeObjectURL(prev);
        return null;
      });
    };
  }, [repoFile?.data, selectedRepoPath]);

  useEffect(() => {
    if (isOpen && detailQuery.data) {
      const detail = detailQuery.data.data;

      const mapApiTypeToUI = (
        t: string | null | undefined
      ): TQuestionTypeUI => {
        switch (t) {
          case 'Pilihan Ganda':
            return 'pilihan_ganda';
          case 'Benar Salah':
            return 'benar_salah';
          case 'Isian':
            return 'isian';
          default:
            return 'pilihan_ganda';
        }
      };

      form.reset({
        id: detail.id.toString(),
        question: detail.question ?? '',
        questionType: mapApiTypeToUI(detail.type),
        option: [
          detail.option_a ?? '',
          detail.option_b ?? '',
          detail.option_c ?? '',
          detail.option_d ?? '',
        ],
        keyAnswer: detail.correct_answer ?? '',
        category:
          detail.categories?.map((c) => ({
            value: String(c.id),
            label: String(c.name),
          })) ?? [],
        level:
          detail.levels?.map((l) => ({
            value: String(l.id),
            label: String(l.level),
          })) ?? [],
        imageFromRepository: detail.image_id ? String(detail.image_id) : '',
        imageName: '',
        howToAddQuestionImage: detail.image_id
          ? 'select_image'
          : 'upload_image',
      });
    }

    if (!isOpen) {
      form.reset({
        questionType: 'pilihan_ganda',
        howToAddQuestionImage: 'select_image',
        category: [],
        level: [],
      });
    }
  }, [isOpen, detailQuery.data]);

  const handleQuestionTypeChange = (val: TQuestionTypeUI) => {
    form.setValue('questionType', val, {
      shouldValidate: true,
      shouldDirty: true,
    });

    form.setValue('keyAnswer', '' as any, {
      shouldValidate: true,
      shouldDirty: true,
    });

    if (val !== 'pilihan_ganda') {
      form.setValue('option', [] as any, {
        shouldValidate: true,
        shouldDirty: true,
      });
    }
  };

  const onSubmit = (body: ICreateQuestionBankBody) => {
    const payload = buildCreateQuestionBankPayload(body);

    if (!data) {
      createQB.mutate(payload, { onSuccess: onCloseModal });
    } else {
      updateQB.mutate(
        { params: { id: data.id }, ...payload },
        { onSuccess: onCloseModal }
      );
    }
  };

  const renderImageSection = () => {
    if (form.watch('howToAddQuestionImage') === 'select_image') {
      if (previewUrl) {
        return (
          <img
            src={previewUrl}
            alt="Repository preview"
            className="w-full max-h-80 object-contain"
          />
        );
      }
      return null;
    }

    return (
      <FileUploader
        title="Upload Image"
        supportedText="Supported file types: PNG, JPG, JPEG, SVG max. size 2 MB."
        acceptedTypes="image/*"
        onFileUpload={(img) =>
          form.setValue('image', img!, { shouldValidate: true })
        }
        banner={
          <div className="border-[#F8A644] border bg-[#FEF4E9] h-[52px] px-4 rounded-[8px] w-full flex items-center gap-2">
            <Info
              size={20}
              fill="#F8A644"
              stroke="white"
            />
            <span className="text-sm text-[#767676]">
              Please adjust your image aspect ratio to 4:3 with minimum
              resolutions in 640 x 480 pixel for better quality.
            </span>
          </div>
        }
      />
    );
  };

  return (
    <FormProvider {...form}>
      <form
        className="flex flex-col gap-4"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <div className="flex gap-2 items-center w-full">
          <InputSelect<ICreateQuestionBankBody>
            label="How to Add Question Image"
            id="howToAddQuestionImage"
            placeholder="Select"
            value={form.watch('howToAddQuestionImage')}
            options={ADD_QUESTION_IMAGE_OPTION}
          />

          {form.watch('howToAddQuestionImage') === 'select_image' ? (
            <ImageRepoCombobox
              label="Image"
              placeholder="Search / select image"
              value={form.watch('imageFromRepository')}
              options={imageRepoOptions}
              onChange={(val) => {
                form.setValue('imageFromRepository', val, {
                  shouldValidate: true,
                });
              }}
              onSearchChange={(term) => setSearchTerm(term)}
              maxHeight={300}
            />
          ) : (
            <div className="w-full">
              <InputString<ICreateQuestionBankBody>
                label="Image Name"
                id="imageName"
                placeholder="Input image name"
              />
            </div>
          )}
        </div>

        {renderImageSection()}

        <span className="font-medium">Question Information</span>

        <MultipleSelectorComponent
          title="Category"
          placeholder="Select Category"
          selectAllTitle="Select All Category"
          options={categoryOptions}
          value={(form.watch('category') as Option[]) ?? []}
          onSelect={(vals) => {
            form.setValue('category', vals, {
              shouldValidate: true,
              shouldDirty: true,
            });
          }}
          onSelectAll={() => {
            const current = (form.getValues('category') as Option[]) ?? [];
            const all = categoryOptions;
            const isAllSelected =
              current.length === all.length &&
              all.every((a) => current.some((c) => c.value === a.value));

            form.setValue('category', isAllSelected ? [] : all, {
              shouldValidate: true,
              shouldDirty: true,
            });
          }}
        />

        <div className="flex gap-2 items-center w-full">
          <MultipleSelectorComponent
            title="Level"
            placeholder="Select Level"
            options={levelOptions}
            value={(form.watch('level') as Option[]) ?? []}
            onSelect={(vals) => {
              form.setValue('level', vals, {
                shouldValidate: true,
                shouldDirty: true,
              });
            }}
          />

          <InputSelect<ICreateQuestionBankBody>
            label="Question Type"
            id="questionType"
            placeholder="Select question type"
            value={form.watch('questionType')}
            options={QUESTION_TYPE_OPTION}
            onChangeOverride={(val) =>
              handleQuestionTypeChange(val as TQuestionTypeUI)
            }
          />
        </div>

        <InputString<ICreateQuestionBankBody>
          label="Question"
          id="question"
          placeholder="Input question"
        />

        {form.watch('questionType') === 'pilihan_ganda' && (
          <>
            {['A', 'B', 'C', 'D'].map((k, idx) => (
              <InputString<ICreateQuestionBankBody>
                key={k}
                label={`Option ${k}`}
                id={`option[${idx}]` as any}
                placeholder="Input answer"
              />
            ))}
            <InputSelect<ICreateQuestionBankBody>
              label="Key Answer"
              id="keyAnswer"
              placeholder="Select key answer"
              value={form.watch('keyAnswer')}
              options={KEY_ANSWER_OPTION}
            />
          </>
        )}

        {form.watch('questionType') === 'benar_salah' && (
          <InputSelect<ICreateQuestionBankBody>
            label="Key Answer"
            id="keyAnswer"
            placeholder="Select key answer"
            value={form.watch('keyAnswer')}
            options={KEY_TRUE_FALSE_OPTION}
          />
        )}

        {form.watch('questionType') === 'isian' && (
          <InputString<ICreateQuestionBankBody>
            label="Key Answer"
            id="keyAnswer"
            placeholder="Input key answer"
          />
        )}

        <BaseSeparator className="mt-4 -mb-2" />
        <div className="flex justify-end gap-3 -mb-3">
          <DialogClose asChild>
            <BaseButton
              className="h-11 w-32"
              variant={'outline'}
            >
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            className="h-11 w-32"
            type="submit"
            disabled={createQB.isPending}
          >
            {data ? 'Save Changes' : 'Add New Question'}
          </BaseButton>
        </div>
      </form>
    </FormProvider>
  );
};

const InputString = <T extends ICreateQuestionBankBody>({
  label,
  id,
  placeholder,
  optional = false,
  readonly = false,
}: {
  label: string;
  id: keyof T;
  placeholder?: string;
  optional?: boolean;
  readonly?: boolean;
}) => {
  const form = useFormContext<T>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel
        className="text-sm"
        htmlFor={id as string}
      >
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseInput
        id={id as string}
        placeholder={placeholder}
        {...form.register(id as Path<T>)}
        className="h-11 disabled:bg-gray-100"
        readOnly={readonly}
        disabled={readonly}
      />
    </div>
  );
};

const InputSelect = <T extends ICreateQuestionBankBody>({
  label,
  id,
  placeholder,
  options,
  value,
  optional = false,
  onChangeOverride,
}: {
  label: string;
  id: keyof T;
  placeholder?: string;
  value?: string;
  options: { value: string; label: string }[];
  optional?: boolean;
  onChangeOverride?: (val: string) => void;
}) => {
  const form = useFormContext<T>();

  return (
    <div className="flex flex-col gap-1 w-full">
      <BaseLabel
        className="text-sm"
        htmlFor={id as string}
      >
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseSelect
        {...form.register(id as Path<T>)}
        value={value}
        onValueChange={(val) => {
          if (!val) return;
          if (onChangeOverride) {
            onChangeOverride(val);
            return;
          }
          const key = id as Path<T>;
          if (key === ('level' as any)) {
            form.setValue(key, [val] as any, { shouldValidate: true });
          } else {
            form.setValue(key, val as any, { shouldValidate: true });
          }
        }}
      >
        <BaseSelectTrigger
          className="w-full min-h-11"
          id={id as string}
        >
          <BaseSelectValue placeholder={placeholder} />
        </BaseSelectTrigger>
        <BaseSelectContent>
          {options.map((option) => (
            <BaseSelectItem
              key={option.value}
              value={option.value}
            >
              {option.label}
            </BaseSelectItem>
          ))}
        </BaseSelectContent>
      </BaseSelect>
    </div>
  );
};

export default QuestionBankNewModal;
