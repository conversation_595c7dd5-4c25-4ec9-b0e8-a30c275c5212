'use client';

import { useState } from 'react';
import { WAF_TEST_RESPONSES } from '../api/test-waf/test-utils';
import axios from 'axios';

export default function TestWafPage() {
  const [isRunning, setIsRunning] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);



  const runTest = async (testType: 'detection' | 'logging' | 'full', testName: string) => {
    setIsRunning(true);
    setLogs([`Starting ${testName}...`]);
    
    try {
      const response = await axios.put(`/api/test-waf?test=${testType}`);
      setLogs(prev => [...prev, `✅ ${testName} completed successfully!`]);
      setLogs(prev => [...prev, `📄 Result: ${JSON.stringify(response.data, null, 2)}`]);
    } catch (error: any) {
      setLogs(prev => [...prev, `❌ ${testName} failed: ${error.message}`]);
      if (error.response) {
        setLogs(prev => [...prev, `📄 Error details: ${JSON.stringify(error.response.data, null, 2)}`]);
      }
    } finally {
      setIsRunning(false);
    }
  };

  const clearLogs = () => setLogs([]);

  // Test real API calls with simulated WAF responses
  const testApiCall = async (method: 'GET' | 'POST', wafType: keyof typeof WAF_TEST_RESPONSES, shouldBlock: boolean) => {
    setLogs(prev => [...prev, `🌐 Testing ${method} API call with ${wafType} response (WAF: ${shouldBlock ? 'ON' : 'OFF'})`]);
    
    try {
      const url = `/api/test-waf?type=${wafType}&waf=${shouldBlock}`;
      
      let response;
      if (method === 'GET') {
        response = await axios.get(url);
      } else {
        response = await axios.post(url, { 
          testData: 'sensitive information',
          timestamp: new Date().toISOString() 
        });
      }
      
      setLogs(prev => [...prev, `✅ API call successful: ${response.status} ${response.statusText}`]);
      setLogs(prev => [...prev, `📄 Response: ${JSON.stringify(response.data, null, 2)}`]);
      
    } catch (error: any) {
      setLogs(prev => [...prev, `❌ API call failed: ${error.message}`]);
      if (error.response) {
        setLogs(prev => [...prev, `📄 Error response: ${error.response.status} ${error.response.statusText}`]);
      }
    }
  };

  return (
    <div className="h-screen bg-gray-50 overflow-y-auto">
      <div className="container mx-auto p-6 max-w-6xl">
        <h1 className="text-3xl font-bold mb-6 text-gray-800">WAF Testing Dashboard</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          {/* Test Controls */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">Test Controls</h2>
            
            <div className="space-y-3">
              <button
                onClick={() => runTest('full', 'Full WAF Test Suite')}
                disabled={isRunning}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-3 rounded-lg font-medium transition-colors"
              >
                {isRunning ? 'Running...' : 'Run Full Test Suite'}
              </button>
              
              <button
                onClick={() => runTest('detection', 'WAF Detection Test')}
                disabled={isRunning}
                className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-3 rounded-lg font-medium transition-colors"
              >
                Test WAF Detection Only
              </button>
              
              <button
                onClick={() => runTest('logging', 'WAF Logging Test')}
                disabled={isRunning}
                className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-4 py-3 rounded-lg font-medium transition-colors"
              >
                Test WAF Logging Only
              </button>
              
              <button
                onClick={clearLogs}
                className="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-3 rounded-lg font-medium transition-colors"
              >
                Clear Logs
              </button>
            </div>
            
            <div className="border-t pt-4 mt-4">
              <h3 className="font-medium mb-3 text-gray-700">API Interceptor Tests</h3>
              <div className="space-y-2">
                <button
                  onClick={() => testApiCall('GET', 'standard', true)}
                  disabled={isRunning}
                  className="w-full bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
                >
                  GET with WAF Block
                </button>
                <button
                  onClick={() => testApiCall('POST', 'standard', true)}
                  disabled={isRunning}
                  className="w-full bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
                >
                  POST with WAF Block
                </button>
                <button
                  onClick={() => testApiCall('GET', 'standard', false)}
                  disabled={isRunning}
                  className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
                >
                  Normal GET Request
                </button>
              </div>
            </div>
          </div>
          
          {/* Test Responses */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">Test Responses</h2>
            <div className="text-sm space-y-3 max-h-96 overflow-y-auto">
              {Object.entries(WAF_TEST_RESPONSES).map(([key, value]) => (
                <details key={key} className="border rounded-lg p-3 bg-gray-50">
                  <summary className="cursor-pointer font-medium capitalize text-gray-700 hover:text-gray-900">
                    {key} Response
                  </summary>
                  <pre className="mt-3 text-xs bg-white p-3 rounded border overflow-x-auto text-gray-600">
                    {value.trim()}
                  </pre>
                </details>
              ))}
            </div>
          </div>
          
          {/* Instructions */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="font-semibold text-gray-800 mb-3">Testing Instructions</h3>
            <ul className="text-sm text-gray-600 space-y-2">
              <li className="flex items-start">
                <span className="text-blue-500 mr-2">•</span>
                <div>
                  <strong className="text-gray-700">Detection Test:</strong> Tests all WAF response patterns against the detector
                </div>
              </li>
              <li className="flex items-start">
                <span className="text-purple-500 mr-2">•</span>
                <div>
                  <strong className="text-gray-700">Logging Test:</strong> Tests database logging functionality (requires PostgreSQL)
                </div>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">•</span>
                <div>
                  <strong className="text-gray-700">Full Suite:</strong> Runs both detection and logging tests
                </div>
              </li>
              <li className="flex items-start">
                <span className="text-orange-500 mr-2">•</span>
                <div>Check browser console and server logs for additional details</div>
              </li>
              <li className="flex items-start">
                <span className="text-red-500 mr-2">•</span>
                <div>Make sure your DATABASE_URL is configured for logging tests</div>
              </li>
            </ul>
          </div>
        </div>
        
        {/* Test Logs */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-800">Test Logs</h2>
            <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
              {logs.length} log entries
            </span>
          </div>
          
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg min-h-64 max-h-96 overflow-y-auto font-mono text-sm border">
            {logs.length === 0 ? (
              <div className="text-gray-500 text-center py-8">
                No logs yet. Run a test to see output.
              </div>
            ) : (
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <div key={index} className="leading-relaxed">
                    <span className="text-gray-500 text-xs mr-2">
                      [{String(index + 1).padStart(3, '0')}]
                    </span>
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}