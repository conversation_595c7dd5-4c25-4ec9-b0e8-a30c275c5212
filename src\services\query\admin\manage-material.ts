import {
  apiDeleteMaterial,
  apiGetDetailLearningMaterial,
  apiGetLearningMaterialUrl,
  apiGetListMaterials,
} from "@/services/api/admin/manage-material";
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { IGetListMaterialQuery } from "@/interfaces/admin/manage-material/list";

export const queryKey = {
  list: (params?: IGetListMaterialQuery) => ["material", params],
  detail: (id: string) => ["material", id],
  url: (params: { url: string }) => ["material-url", params],
};

export const useGetListMaterialQuery = (
  params?: IGetListMaterialQuery,
  enabled = true
) => {
  return useQuery({
    queryKey: queryKey.list(params),
    queryFn: async () => {
      return await apiGetListMaterials(params);
    },
    placeholderData: keepPreviousData,
    enabled,
  });
};

export const useGetLearningMaterialUrlQuery = (params: { url: string }) => {
  return useQuery({
    queryKey: queryKey.url(params),
    queryFn: async () => {
      return await apiGetLearningMaterialUrl(params);
    },
    enabled: !!params.url,
    placeholderData: keepPreviousData,
  });
};

export const useGetDetailLearningMaterialQuery = (id: string) => {
  return useQuery({
    queryKey: queryKey.detail(id),
    queryFn: async () => {
      return await apiGetDetailLearningMaterial(id);
    },
    enabled: !!id,
    placeholderData: keepPreviousData,
  });
};

export const useDeleteMaterialMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => apiDeleteMaterial(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["material"] });
    },
  });
};
