"use client";

import { BaseButton } from "@/components/atoms/button";
import { cn } from "@/utils/common";
import { Plus, Settings2 } from "lucide-react";
import { useShallow } from "zustand/react/shallow";
import { useManageJobFilterStore } from "@/store/admin/manage-job/filter";
import { useManageLearningPathModalStore } from "@/store/admin/manage-learning-path/modal";

const ManageJobTableHeaderFilter = () => {
  const { openFilter, setOpenFilter } = useManageJobFilterStore(
    useShallow((state) => ({
      openFilter: state.openFilter,
      setOpenFilter: state.setOpenFilter,
    }))
  );

  const { setOpenAddLearningLevelModal } = useManageLearningPathModalStore(
    useShallow(({ setOpenAddLearningLevelModal }) => ({
      setOpenAddLearningLevelModal,
    }))
  );

  return (
    <div className="flex justify-end gap-3">
      <BaseButton
        variant={"outline"}
        className={cn("h-12 px-8", openFilter && "bg-gray-200")}
        onClick={() => setOpenFilter(!openFilter)}
        ref={null}
        asChild
      >
        <div className="flex items-center gap-2">
          <Settings2 />
          Filter
        </div>
      </BaseButton>
      <BaseButton
        className="h-12 px-5"
        onClick={() => setOpenAddLearningLevelModal(true)}
        asChild
      >
        <div className="flex items-center gap-2">
          <Plus />
          Add Job Position
        </div>
      </BaseButton>
    </div>
  );
};

export default ManageJobTableHeaderFilter;
