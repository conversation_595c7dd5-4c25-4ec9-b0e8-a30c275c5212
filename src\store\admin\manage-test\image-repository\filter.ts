import { create } from 'zustand';
import {
  IGetImageRepositoryListQuery,
  TIRFeature,
  TIRSearchBy,
} from '@/interfaces/admin/manage-test/image-repository/list';

interface IImageRepositoryFilterStore {
  openFilter: boolean;
  setOpenFilter: (open: boolean) => void;

  query: IGetImageRepositoryListQuery;

  draft: IGetImageRepositoryListQuery;

  setDraft: (q: Partial<IGetImageRepositoryListQuery>) => void;
  resetDraft: () => void;
  initDraftFromQuery: () => void;
  applyDraft: () => void;

  setQuery: (q: Partial<IGetImageRepositoryListQuery>) => void;
  resetQuery: () => void;

  setSearch: (val?: string | number) => void;
  setSearchBy: (val: TIRSearchBy) => void;

  setFeature: (val: TIRFeature) => void;
}

const DEFAULT_QUERY: IGetImageRepositoryListQuery = {
  page: 1,
  limit: 10,
  feature: 'OnlineLearning',
  search_by: 'image_name',
};

export const useImageRepositoryFilterStore =
  create<IImageRepositoryFilterStore>()((set, get) => ({
    openFilter: false,
    setOpenFilter: (open) => set({ openFilter: open }),

    query: { ...DEFAULT_QUERY },
    draft: { ...DEFAULT_QUERY },

    setDraft: (q) => set({ draft: { ...(get().draft as any), ...q } }),
    resetDraft: () => set({ draft: { ...DEFAULT_QUERY } }),
    initDraftFromQuery: () => set({ draft: { ...(get().query as any) } }),
    applyDraft: () => set({ query: { ...(get().draft as any) } }),

    setQuery: (q) => set({ query: { ...(get().query as any), ...q } }),
    resetQuery: () => set({ query: { ...DEFAULT_QUERY } }),

    setSearch: (val) =>
      set((state) => {
        const nextSearchBy = state.draft.search_by ?? state.query.search_by;
        return {
          draft: { ...state.draft, page: 1, search: val },
          query: {
            ...state.query,
            page: 1,
            search: val,
            search_by: nextSearchBy,
          },
        };
      }),

    setSearchBy: (val) =>
      set((state) => ({
        draft: { ...state.draft, page: 1, search_by: val },
      })),

    setFeature: (val) =>
      set({
        draft: { ...(get().draft as any), feature: val },
      }),
  }));
