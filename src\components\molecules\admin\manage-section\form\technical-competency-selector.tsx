"use client";

import { BaseLabel } from "@/components/atoms/label";
import MultipleSelector from "@/components/atoms/multiselect";
import { useGetCompetencyListQuery } from "@/services/query/admin/master";
import { Controller, useFormContext } from "react-hook-form";
import { cn } from "@/lib/utils";
import { useMemo, useState } from "react";

interface TechnicalCompetencySelectorProps {
  labelClassName?: string;
  name?: string;
}

const TechnicalCompetencySelector = ({
  labelClassName,
  name = "technicalCompetencies",
}: TechnicalCompetencySelectorProps) => {
  const form = useFormContext();
  const [search, setSearch] = useState("");

  const { data: competencies, isPending: pendingCompetencies } =
    useGetCompetencyListQuery({
      type: "technical",
      order_by: "name",

      ...(search && { search }),
    });

  const competencyOptions = useMemo(
    () =>
      competencies?.data?.map((competency) => ({
        label: competency.name,
        value: competency.id.toString(),
      })) ?? [],
    [competencies]
  );

  return (
    <div className="space-y-2">
      <BaseLabel className={cn("text-sm font-medium", labelClassName)}>
        Technical Competencies (Optional)
      </BaseLabel>

      <div className="flex flex-col">
        <Controller
          name={name}
          render={({ field }) => {
            return (
              <MultipleSelector
                // onSearch={(value) => {
                //   setSearch(value);
                //   if (pendingCompetencies)
                //     return Promise.resolve(competencyOptions);
                //   return Promise.resolve(competencyOptions);
                // }}
                isPending={pendingCompetencies}
                value={field.value || []}
                options={competencyOptions}
                onChange={field.onChange}
                placeholder="Select technical competencies"
                badgeClassName="bg-green-100 text-green-800"
                loadingIndicator={
                  <p className="py-2 text-center text-sm leading-10 text-muted-foreground">
                    Loading technical competencies...
                  </p>
                }
                emptyIndicator={
                  <p className="w-full text-center text-sm leading-10 text-muted-foreground">
                    No technical competencies found.
                  </p>
                }
              />
            );
          }}
        />
        {form.formState.errors[name] && (
          <p className="text-red-500 text-sm mt-1">
            {form.formState.errors[name]?.message as string}
          </p>
        )}
      </div>
    </div>
  );
};

export default TechnicalCompetencySelector;
