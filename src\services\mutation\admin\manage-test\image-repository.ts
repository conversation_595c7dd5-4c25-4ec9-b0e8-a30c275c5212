import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  apiCreateImageRepository,
  apiDeleteImageRepository,
  apiUpdateImageRepository,
} from '@/services/api/admin/manage-test/image-repository';
import { ICreateImageRepositoryBodyData } from '@/interfaces/admin/manage-test/image-repository/new';
import {
  IUpdateImageRepositoryBodyData,
  IUpdateImageRepositoryParams,
} from '@/interfaces/admin/manage-test/image-repository/update';
import toast from 'react-hot-toast';

export const useCreateImageRepositoryMutation = () => {
  const qc = useQueryClient();

  return useMutation({
    mutationKey: ['image-repo', 'create'],
    mutationFn: async ({
      body,
      file,
    }: {
      body: ICreateImageRepositoryBodyData;
      file?: File | null;
    }) => apiCreateImageRepository(body, file),
    onSuccess: () => {
      toast.success('Image Repository created');
      qc.invalidateQueries({ queryKey: ['image-repo', 'list'] });
    },
    onError: () => {
      toast.error('Failed to create Image Repository');
    },
  });
};

export const useUpdateImageRepositoryMutation = () => {
  const qc = useQueryClient();

  return useMutation({
    mutationKey: ['image-repo', 'update'],
    mutationFn: async ({
      params,
      body,
      file,
    }: {
      params: IUpdateImageRepositoryParams;
      body: IUpdateImageRepositoryBodyData;
      file?: File | null;
    }) => apiUpdateImageRepository(params, body, file),
    onSuccess: (_data, variables) => {
      toast.success('Image Repository updated');
      qc.invalidateQueries({ queryKey: ['image-repo', 'list'] });
      if (variables?.params?.id) {
        qc.invalidateQueries({
          queryKey: ['image-repo', 'detail', variables.params.id],
        });
      }
    },
    onError: () => {
      toast.error('Failed to update Image Repository');
    },
  });
};

export const useDeleteImageRepositoryMutation = () => {
  const qc = useQueryClient();

  return useMutation({
    mutationKey: ['image-repo', 'delete'],
    mutationFn: ({ id }: { id: number }) => apiDeleteImageRepository({ id }),
    onSuccess: (_data, variables) => {
      toast.success('Image Repository deleted');
      qc.invalidateQueries({ queryKey: ['image-repo', 'list'] });
      if (variables?.id) {
        qc.invalidateQueries({
          queryKey: ['image-repo', 'detail', variables.id],
        });
      }
    },
    onError: () => {
      toast.error('Failed to delete Image Repository');
    },
  });
};
