export interface IMaterial {
  id: string;
  document_id: string;
  category: string;
  level: string;
  document_name: string;
  size: string;
  associated_sections: string[];
  uploaded_at: string;
  uploaded_by: string;
  updated_at: string;
  updated_by: string;
}

export interface IGetListMaterialQuery {
  page: number;
  limit?: number;
  search?: string;
  search_by?: "name" | "associated" | "created_by" | "updated_by";
  category_id?: string;
  level_id?: string;
  type: "video" | "document" | "audio";
  feature?: "OnlineLearning";
}

export type IGetListMaterialResponse = {
  id: number;
  type: string;
  name: string;
  file_format: string;
  levels: ILevel[];
  categories: ICategory[];
  associated: IAssociated[];
  link: string;
  filesize: number;
  feature: string;
  created_at: Date;
  created_by: string;
  updated_by: string;
  last_updated: Date;
};

export type IGetDetailMaterialResponse = Omit<
  IGetListMaterialResponse,
  "associated"
> & {
  associated: IAssociatedSection[];
};

export type IAssociatedSection = {
  section_id: number;
  section_name: string;
  categories: ICategorySection[];
  levels: ILevelSection[];
};

export type ICategorySection = {
  section_category_id: number;
  section_category_name: string;
};

export type ILevelSection = {
  section_level_id: number;
  section_level_name: string;
};

export type IAssociated = {
  id: number;
  name: string;
};

export type ICategory = {
  id: number;
  name: string;
};

export type ILevel = {
  id: number;
  level: string;
};

export type IInsertLearningMaterialData = {
  feature: "OnlineLearning" | "InClassTraining";
  category_id: number[];
  level_id: number[];
  file_name?: string;
};
