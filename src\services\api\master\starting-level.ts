'use server';

import {
  IGetCategoryListQuery,
  IGetCategoryListResponse,
} from '@/interfaces/admin/master/category';
import {
  IGetImageRepositoryListQuery,
  IGetImageRepositoryListResponse,
} from '@/interfaces/admin/master/image-category';
import {
  IGetStartingLevelListQuery,
  IGetStartingLevelListResponse,
} from '@/interfaces/admin/master/starting-level';
import { IGlobalResponseDto } from '@/interfaces/global/response';
import { api } from '@/services/satellite/';
import { handleAxiosError } from '@/utils/common/axios';

export const apiGetStartingLevelList = async (
  query?: IGetStartingLevelListQuery
) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetStartingLevelListResponse[]>
    >('/cms/admin/master/starting-level', { params: query });

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};

export const apiGetCategoryList = async (query?: IGetCategoryListQuery) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetCategoryListResponse[]>
    >('/cms/admin/master/category', { params: query });

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};

export const apiGetImageRepository = async (
  query?: IGetImageRepositoryListQuery
) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetImageRepositoryListResponse[]>
    >('/cms/admin/master/image-repository', { params: query });

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
