import { IGetQuestionTemplateListResponse } from '@/interfaces/admin/manage-test/question-template/list';
import { create } from 'zustand';

interface IQuestionTemplateModal {
  openedQuestionTemplate: IGetQuestionTemplateListResponse | null;
  setOpenedQuestionTemplate: (
    data: IGetQuestionTemplateListResponse | null
  ) => void;
  openAddModal: boolean;
  setOpenAddModal: (open: boolean) => void;
  openDeleteModal: boolean;
  setOpenDeleteModal: (open: boolean) => void;
}

export const useQuestionTemplateModal = create<IQuestionTemplateModal>()(
  (set) => ({
    openedQuestionTemplate: null,
    setOpenedQuestionTemplate: (
      data: IGetQuestionTemplateListResponse | null
    ) => set({ openedQuestionTemplate: data }),
    openAddModal: false,
    setOpenAddModal: (open: boolean) => set({ openAddModal: open }),
    openDeleteModal: false,
    setOpenDeleteModal: (open: boolean) => set({ openDeleteModal: open }),
  })
);
