"use client";

import { BaseLabel } from "@/components/atoms/label";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { BaseButton } from "@/components/atoms/button";
import { useState, useEffect } from "react";
import { MultipleCheckboxSelect } from "../../ui/multiple-checkbox-select";
import { useManageSectionQueryStore } from "@/store/admin/manage-section/query";
import { useShallow } from "zustand/react/shallow";
import { useGetListCategoryQuery } from "@/services/query/admin/manage-category";
import { useGetStartingLevelListQuery } from "@/services/query/admin/master";

interface IFilter {
  category_id?: number;
  level_id?: number;
  type: string[];
}

const sectionTypeOptions = [
  { value: "video", label: "Video" },
  { value: "audio", label: "Audio" },
  { value: "document", label: "Document" },
  { value: "test", label: "Test" },
  { value: "quiz", label: "Quiz" },
  { value: "pre-test", label: "Pre-test" },
  { value: "post-test", label: "Post-test" },
];

const ManageSectionFilterInput = () => {
  const { sectionQuery, setSectionQuery } = useManageSectionQueryStore(
    useShallow(({ sectionQuery, setSectionQuery }) => ({
      sectionQuery,
      setSectionQuery,
    }))
  );

  const [filter, setFilter] = useState<IFilter>({
    category_id: sectionQuery.category_id,
    level_id: sectionQuery.level_id,
    type: sectionQuery.type ? sectionQuery.type.split(",") : [],
  });

  // Fetch categories and levels for dropdowns
  const { data: categoriesData } = useGetListCategoryQuery({
    page: 1,
    limit: 100,
  });
  const { data: levelsData } = useGetStartingLevelListQuery();

  useEffect(() => {
    setFilter({
      category_id: sectionQuery.category_id,
      level_id: sectionQuery.level_id,
      type: sectionQuery.type ? sectionQuery.type.split(",") : [],
    });
  }, [sectionQuery]);

  const handleTypeChange = (selectedValues: string[]) => {
    setFilter((prev) => ({
      ...prev,
      type: selectedValues,
    }));
  };

  const handleCategoryChange = (value: string) => {
    const categoryId = value === "all" ? undefined : parseInt(value);
    setFilter((prev) => ({
      ...prev,
      category_id: categoryId,
    }));
  };

  const handleLevelChange = (value: string) => {
    const levelId = value === "all" ? undefined : parseInt(value);
    setFilter((prev) => ({
      ...prev,
      level_id: levelId,
    }));
  };

  const handleApply = () => {
    setSectionQuery({
      category_id: filter.category_id,
      level_id: filter.level_id,
      type: filter.type.length > 0 ? filter.type.join(",") : undefined,
      page: 1, // Reset to first page when applying filters
    });
  };

  const handleReset = () => {
    const resetFilter: IFilter = {
      category_id: undefined,
      level_id: undefined,
      type: [],
    };
    setFilter(resetFilter);
    setSectionQuery({
      category_id: undefined,
      level_id: undefined,
      type: undefined,
      page: 1,
    });
  };

  return (
    <div className="flex flex-col gap-4 w-full bg-white rounded-lg p-3">
      <div className="flex items-center justify-between">
        <span className="font-semibold">Filter</span>
        <div className="flex gap-3">
          <BaseButton
            variant="outline"
            className="text-red-600 border-red-600 hover:text-red-600 h-9"
            onClick={handleReset}
          >
            Reset
          </BaseButton>
          <BaseButton
            className="h-9 bg-orange-500 hover:bg-orange-600"
            onClick={handleApply}
          >
            Apply
          </BaseButton>
        </div>
      </div>
      <BaseSeparator />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Category Filter */}
        <div className="flex flex-col gap-2">
          <BaseLabel htmlFor="category">Category</BaseLabel>
          <BaseSelect
            value={filter.category_id?.toString() || "all"}
            onValueChange={handleCategoryChange}
          >
            <BaseSelectTrigger>
              <BaseSelectValue placeholder="Select Category" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              <BaseSelectItem value="all">All Categories</BaseSelectItem>
              {categoriesData?.data?.map((category) => (
                <BaseSelectItem
                  key={category.id}
                  value={category.id.toString()}
                >
                  {category.category_name}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>

        {/* Level Filter */}
        <div className="flex flex-col gap-2">
          <BaseLabel htmlFor="level">Level</BaseLabel>
          <BaseSelect
            value={filter.level_id?.toString() || "all"}
            onValueChange={handleLevelChange}
          >
            <BaseSelectTrigger>
              <BaseSelectValue placeholder="Select Level" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              <BaseSelectItem value="all">All Levels</BaseSelectItem>
              {levelsData?.data?.map((level) => (
                <BaseSelectItem key={level.id} value={level.id.toString()}>
                  Level {level.level} - {level.name}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>

        {/* Section Type Filter */}
        <div className="flex flex-col gap-2">
          <MultipleCheckboxSelect
            options={sectionTypeOptions}
            selectedValues={filter.type || []}
            onSelectionChange={handleTypeChange}
            label="Section Type"
          />
        </div>
      </div>
    </div>
  );
};

export default ManageSectionFilterInput;
