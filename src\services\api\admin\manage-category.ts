"use server";

import {
  ICategory,
  IGetListCategoryQuery,
  IGetListSubCategoryQuery,
  ISubCategory,
} from "@/interfaces/admin/manage-category/list";
import {
  ICreateCategoryPayload,
  ICreateSubCategoryPayload,
  IUpdateCategoryPayload,
  IUpdateSubCategoryPayload,
} from "@/interfaces/admin/manage-category/new";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetListCategory = async (params?: IGetListCategoryQuery) => {
  try {
    const response = await api.get<IGlobalResponseDto<ICategory[]>>(
      "/cms/admin/category-list",
      { params }
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiGetListSubCategory = async (
  params?: IGetListSubCategoryQuery
) => {
  try {
    const response = await api.get<IGlobalResponseDto<ISubCategory[]>>(
      "/cms/admin/subcategory-list",
      { params }
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiInsertCategory = async (body: ICreateCategoryPayload) => {
  try {
    const response = await api.post<IGlobalResponseDto<any>>(
      "/cms/admin/category-insert",
      body
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiInsertSubCategory = async (body: ICreateSubCategoryPayload) => {
  try {
    const response = await api.post<IGlobalResponseDto<any>>(
      "/cms/admin/subcategory-insert",
      body
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiUpdateCategory = async (payload: IUpdateCategoryPayload) => {
  try {
    const response = await api.post<IGlobalResponseDto<any>>(
      "/cms/admin/category-update/" + payload.id,
      payload.body
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiUpdateSubCategory = async (
  payload: IUpdateSubCategoryPayload
) => {
  try {
    const response = await api.post<IGlobalResponseDto<any>>(
      "/cms/admin/subcategory-update/" + payload.id,
      payload.body
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiDownloadListCategoryFile = async (
  params: IGetListCategoryQuery
) => {
  try {
    const response = await api.get(`/cms/admin/category-export`, {
      params,
      responseType: "arraybuffer",
    });

    let filename = "lemon_category.xlsx";

    const disposition = response.headers["content-disposition"];
    if (disposition && disposition.includes("filename=")) {
      const match = disposition.match(/filename="?([^"]+)"?/);
      if (match?.[1]) {
        filename = match[1];
      }
    }

    const base64File = Buffer.from(response.data).toString("base64");

    return {
      filename,
      file: base64File,
    };
  } catch (error) {
    throw handleAxiosError(error);
  }
};

export const apiDownloadListSubCategoryFile = async (
  params: IGetListSubCategoryQuery
) => {
  try {
    const response = await api.get(`/cms/admin/subcategory-export`, {
      params,
      responseType: "arraybuffer",
    });

    let filename = "lemon_subcategory.xlsx";

    const disposition = response.headers["content-disposition"];
    if (disposition && disposition.includes("filename=")) {
      const match = disposition.match(/filename="?([^"]+)"?/);
      if (match?.[1]) {
        filename = match[1];
      }
    }

    const base64File = Buffer.from(response.data).toString("base64");

    return {
      filename,
      file: base64File,
    };
  } catch (error) {
    throw handleAxiosError(error);
  }
};
