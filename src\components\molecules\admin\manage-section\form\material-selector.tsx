"use client";

import { BaseLabel } from "@/components/atoms/label";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { useGetMaterialRepositoryListQuery } from "@/services/query/admin/master";
import { Controller, useFormContext } from "react-hook-form";
import { cn } from "@/lib/utils";
import { useMemo } from "react";

interface MaterialSelectorProps {
  labelClassName?: string;
  name?: string;
  sectionType: "video" | "audio" | "document";
  label?: string;
  placeholder?: string;
}

const MaterialSelector = ({
  labelClassName,
  name = "materialId",
  sectionType,
  label,
  placeholder,
}: MaterialSelectorProps) => {
  const form = useFormContext();

  // Map section types to material repository types
  const materialType = sectionType === "document" ? "document" : sectionType;

  const { data: materials, isPending: pendingMaterials } =
    useGetMaterialRepositoryListQuery({
      type: materialType,
      order_by: "name",
    });

  const materialOptions = useMemo(
    () =>
      materials?.data?.map((material) => ({
        id: material.id,
        name: material.name,
        type: material.type,
        format: material.file_format,
        link: material.link,
      })) ?? [],
    [materials]
  );

  const materialLabels = {
    video: "Video Material",
    audio: "Audio Material",
    document: "Document Material",
  };

  const materialPlaceholders = {
    video: "Select video material",
    audio: "Select audio material",
    document: "Select document material",
  };

  return (
    <div className="space-y-2">
      <BaseLabel className={cn("text-sm font-medium", labelClassName)}>
        {label || materialLabels[sectionType]}
      </BaseLabel>

      <div className="flex flex-col">
        <Controller
          name={name}
          render={({ field }) => {
            return (
              <BaseSelect
                value={field.value}
                onValueChange={(value) => {
                  field.onChange(value);
                  form.setValue(
                    "materialLink",
                    materialOptions.find((m) => m.id === Number(value))?.link
                  );
                }}
                disabled={pendingMaterials}
              >
                <BaseSelectTrigger
                  className={cn(
                    "w-full",
                    form.formState.errors[name] ? "border-red-500" : ""
                  )}
                >
                  <BaseSelectValue
                    placeholder={
                      pendingMaterials
                        ? "Loading materials..."
                        : placeholder || materialPlaceholders[sectionType]
                    }
                  />
                </BaseSelectTrigger>
                <BaseSelectContent>
                  {materialOptions.length === 0 && !pendingMaterials ? (
                    <BaseSelectItem value="" disabled>
                      No {sectionType} materials available
                    </BaseSelectItem>
                  ) : (
                    materialOptions.map((material) => (
                      <BaseSelectItem
                        key={material.id}
                        value={material.id.toString()}
                      >
                        <div className="flex flex-col">
                          <span className="font-medium">{material.name}</span>
                        </div>
                      </BaseSelectItem>
                    ))
                  )}
                </BaseSelectContent>
              </BaseSelect>
            );
          }}
        />
        {form.formState.errors[name] && (
          <p className="text-red-500 text-sm mt-1">
            {form.formState.errors[name]?.message as string}
          </p>
        )}
      </div>
    </div>
  );
};

export default MaterialSelector;
