import { apiUpdateCertificateStatus } from "@/services/api/admin/manage-certificate";
import { certificateQueryKeys } from "@/services/query/admin/manage-certificate";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useUpdateCertificateStatusMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["update-certificate-status"],
    mutationFn: async ({
      id,
      body,
    }: {
      id: number;
      body: { status: string };
    }) => {
      return await apiUpdateCertificateStatus(id, body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: certificateQueryKeys.all(),
      });
    },
  });
};
