import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseInput } from "@/components/atoms/input";
import { BaseLabel } from "@/components/atoms/label";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { DialogClose } from "@/components/ui/dialog";
import {
  IAssociatedSection,
  IGetListMaterialResponse,
  IInsertLearningMaterialData,
  IMaterial,
} from "@/interfaces/admin/manage-material/list";
import React, { useEffect, useRef, useState } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { X, Trash2, ArrowRight, ArrowLeft } from "lucide-react";
import { useGetDetailLearningMaterialQuery } from "@/services/query/admin/manage-material";
import LevelSelector from "./form/level-selector";
import CategorySelector from "./form/category-selector";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { format } from "date-fns";
import { formatFileSize } from "@/utils/helper/file";
import FileUploader, { FILE_CONFIGS } from "./form/file-uploader";
import { useUpdateLearningMaterialMutation } from "@/services/mutation/admin/manage-material";
import { notifyHotError, notifyHotSuccess } from "../../toast/hot-toast";
import Spinner from "@/components/atoms/spinner";
import PillDropdown from "@/components/atoms/dropdown/pill-dropdown";

// Types
type MaterialType = "video" | "audio" | "document";

interface EditMaterialModalProps {
  isOpen: boolean;
  onClose: () => void;
  material: IGetListMaterialResponse | null;
  type: MaterialType;
}

const MODAL_CONFIGS = {
  video: {
    title: "Edit Video",
    fileLabel: "Video",
    icon: "🎥",
  },
  audio: {
    title: "Edit Audio",
    fileLabel: "Audio",
    icon: "🎵",
  },
  document: {
    title: "Edit Document",
    fileLabel: "Document",
    icon: "📄",
  },
};

const EditMaterialModal = ({
  isOpen,
  onClose,
  material,
  type,
}: EditMaterialModalProps) => {
  const config = MODAL_CONFIGS[type];

  return (
    <BaseDialog open={isOpen} onOpenChange={onClose}>
      <BaseDialogContent className="w-full max-w-[600px] sm:max-w-[808px] max-h-[90vh] overflow-y-auto">
        <BaseDialogHeader>
          <BaseDialogTitle className="flex items-center justify-between">
            <span>{config.title}</span>
          </BaseDialogTitle>
        </BaseDialogHeader>
        <EditMaterialForm material={material} type={type} onClose={onClose} />
      </BaseDialogContent>
    </BaseDialog>
  );
};
// Form Component
interface EditMaterialFormProps {
  material: IGetListMaterialResponse | null;
  type: MaterialType;
  onClose: () => void;
}

export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  status: "uploading" | "success" | "error";
  progress?: number;
  uploadDate?: string;
  file?: File;
  isNew?: boolean;
}

const schema = yup.object({
  id: yup.string().required(),
  name: yup.string().required(),
  categories: yup
    .array(
      yup.object({
        value: yup.string(),
        label: yup.string(),
      })
    )
    .required(),
  level: yup
    .array(
      yup.object({
        value: yup.string(),
        label: yup.string(),
      })
    )
    .required(),
});

const EditMaterialForm = ({
  material,
  type,
  onClose,
}: EditMaterialFormProps) => {
  const form = useForm<yup.InferType<typeof schema>>({
    resolver: yupResolver(schema),
    defaultValues: {
      id: material?.id.toString() || "",
      name: material?.name || "",
      categories: [],
      level: [],
    },
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  const { data: detailData } = useGetDetailLearningMaterialQuery(
    material?.id.toString() || ""
  );

  const { mutateAsync: updateLearningMaterial, isPending: pendingUpdate } =
    useUpdateLearningMaterialMutation();

  const [selectedFile, setSelectedFile] = useState<UploadedFile | null>(null);

  const associatedSections = detailData?.data.associated || [];
  const [currentPage, setCurrentPage] = React.useState(1);
  const sectionsPerPage = 3;

  const config = MODAL_CONFIGS[type];

  const onSubmit = async (data: yup.InferType<typeof schema>) => {
    if (!material?.id) return;
    const filteredCategories = data.categories.filter(
      (cat) => cat.value !== undefined
    );
    const filteredLevel = data.level.filter((lev) => lev.value !== undefined);

    const bodyData: IInsertLearningMaterialData = {
      category_id: filteredCategories.map((cat) => Number(cat.value)),
      level_id: filteredLevel.map((lev) => Number(lev.value)),
      feature: "OnlineLearning",
      file_name: data.name || "",
    };

    const formData = new FormData();
    formData.append("data", JSON.stringify(bodyData));
    formData.append("file_type", type);

    // check if file is new uploaded
    const isNewFile = selectedFile?.isNew;
    if (isNewFile && selectedFile?.file) {
      formData.append("file", selectedFile.file);
    }
    try {
      await updateLearningMaterial({ id: material.id, body: formData });
      onClose();
      notifyHotSuccess("Material updated successfully");
    } catch (error: any) {
      notifyHotError(error.message ?? "Failed to update material");
    }
  };
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    // form.setValue("files", files);

    console.log(files);

    files.forEach((file) => {
      const newFile: UploadedFile = {
        id: Math.random().toString(36).substr(2, 9),
        name: file.name,
        size: file.size,
        type: file.type,
        status: "success",
        progress: 100,
        file,
        isNew: true,
        uploadDate: new Date().toISOString(),
      };

      // directly upload files
      setSelectedFile(newFile);
    });
  };

  const totalSections = associatedSections.length;
  const totalPages = Math.ceil(totalSections / sectionsPerPage);
  // const startIndex = (currentPage - 1) * sectionsPerPage;
  // const endIndex = startIndex + sectionsPerPage;
  // const currentSections = associatedSections.slice(startIndex, endIndex);

  useEffect(() => {
    if (detailData) {
      form.reset({
        id: detailData.data.id.toString(),
        name: detailData.data.name,
        categories:
          detailData.data.categories.map((cat) => {
            return {
              value: cat.id.toString(),
              label: cat.name,
            };
          }) ?? [],
        level:
          detailData.data.levels.map((level) => {
            return {
              value: level.id.toString(),
              label: level.level,
            };
          }) ?? [],
      });
      setSelectedFile({
        id: detailData.data.id.toString(),
        name: detailData.data.name,
        size: detailData.data.filesize,
        type: detailData.data.file_format,
        status: "success",
        progress: 100,
        uploadDate: detailData.data.created_at.toString(),
        file: undefined,
        isNew: false,
      });
    }
  }, [detailData]);

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Video Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">
            {config.fileLabel} Information
          </h3>

          <div className="grid grid-cols-2 gap-4">
            {/* Video ID */}
            <div className="space-y-2">
              <BaseLabel className="text-sm font-medium">
                {config.fileLabel} ID
              </BaseLabel>
              <BaseInput
                value={form.watch("id")}
                readOnly
                disabled
                className="bg-gray-100"
              />
            </div>

            {/* Video Name */}
            <div className="space-y-2">
              <BaseLabel className="text-sm font-medium">
                {config.fileLabel} Name
              </BaseLabel>
              <BaseInput {...form.register("name")} placeholder="Enter name" />
            </div>
          </div>

          <CategorySelector />

          <LevelSelector />
        </div>

        {/* Uploaded File Display */}
        {selectedFile ? (
          <div className="space-y-3">
            <BaseLabel className="text-sm font-medium">
              Uploaded {config.fileLabel}
            </BaseLabel>
            <div className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-purple-100 rounded flex items-center justify-center">
                  <span className="text-purple-600 text-lg">{config.icon}</span>
                </div>
              </div>

              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900">
                  {selectedFile?.name}
                </p>
                <p className="text-xs text-gray-500">
                  {selectedFile?.uploadDate
                    ? format(selectedFile?.uploadDate, "dd MMM yyyy")
                    : "-"}{" "}
                  • {formatFileSize(selectedFile?.size || 0)}
                </p>
              </div>

              <button
                type="button"
                onClick={() => setSelectedFile(null)}
                className="p-1 text-red-500 hover:bg-red-50 rounded"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>
        ) : (
          <FileUploader
            config={FILE_CONFIGS[type]}
            form={form}
            onFileUpload={handleFileUpload}
            ref={fileInputRef}
          />
        )}

        {/* Associated Section Table */}
        <AssociatedSectionTable sections={detailData?.data.associated || []} />

        {/* Pagination for Associated Sections */}
        {totalPages > 1 && (
          <SectionPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4">
          <DialogClose asChild>
            <BaseButton variant="outline" className="px-8">
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            type="submit"
            disabled={pendingUpdate}
            className="px-8 bg-orange-500 hover:bg-orange-600"
          >
            {pendingUpdate ? <Spinner /> : "Save Changes"}
          </BaseButton>
        </div>
      </form>
    </FormProvider>
  );
};

// Associated Section Table Component
interface AssociatedSectionTableProps {
  sections: IAssociatedSection[];
}

const AssociatedSectionTable = ({ sections }: AssociatedSectionTableProps) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Associated Section</h3>

      <div className="border rounded-lg overflow-hidden">
        {/* Table Header */}
        <div className="grid grid-cols-12 gap-4 p-4 bg-gray-50 border-b font-medium text-sm text-gray-700">
          <div className="col-span-1">ID</div>
          <div className="col-span-5">Section</div>
          <div className="col-span-3">Category</div>
          <div className="col-span-3">Level</div>
        </div>

        {/* Table Body */}
        <div className="divide-y">
          {sections.length === 0 ? (
            <div className="p-4 text-center">No associated sections</div>
          ) : (
            sections.map((section) => (
              <div
                key={section.section_id}
                className="grid grid-cols-12 gap-4 p-4 items-center"
              >
                {/* ID */}
                <div className="col-span-1 text-sm text-gray-900">
                  {section.section_id}
                </div>

                {/* Section Name */}
                <div className="col-span-5 text-sm text-gray-900">
                  {section.section_name}
                </div>

                {/* Category Dropdown */}
                <div className="col-span-3">
                  <PillDropdown
                    selected={`${section.categories.length} Category `}
                    options={section.categories.map((category) => ({
                      value: category.section_category_name,
                      label: category.section_category_name,
                    }))}
                  />
                </div>

                {/* Level Dropdown */}
                <div className="col-span-3">
                  <PillDropdown
                    selected={`${section.levels.length} Level`}
                    options={section.levels.map((level) => ({
                      value: level.section_level_name,
                      label: level.section_level_name,
                    }))}
                  />
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

// Section Pagination Component
interface SectionPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const SectionPagination = ({
  currentPage,
  totalPages,
  onPageChange,
}: SectionPaginationProps) => {
  return (
    <div className="flex items-center justify-center gap-1">
      <button
        onClick={() => onPageChange(Math.max(1, currentPage - 1))}
        disabled={currentPage === 1}
        className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
      >
        <ArrowLeft />
      </button>

      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
        const page = i + 1;
        return (
          <button
            key={page}
            onClick={() => onPageChange(page)}
            className={`px-3 py-1 rounded ${
              page === currentPage
                ? "bg-gray-200 text-gray-900"
                : "text-gray-600 hover:bg-gray-100"
            }`}
          >
            {page}
          </button>
        );
      })}

      {totalPages > 5 && (
        <>
          <span className="px-2 text-gray-400">...</span>
          <button
            onClick={() => onPageChange(totalPages)}
            className="px-3 py-1 text-gray-600 hover:bg-gray-100 rounded"
          >
            {totalPages}
          </button>
        </>
      )}

      <button
        onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
        disabled={currentPage === totalPages}
        className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
      >
        <ArrowRight />
      </button>
    </div>
  );
};

export default EditMaterialModal;
