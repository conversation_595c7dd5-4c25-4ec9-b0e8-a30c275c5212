import { IGetQuestionBankListResponse } from '@/interfaces/admin/manage-test/question-bank/list';
import { create } from 'zustand';

interface IQuestionBankTableList {
  selectedQuestionBanksFinal: IGetQuestionBankListResponse[];
  setSelectedQuestionBanksFinal: VoidFunction;
  selectedQuestionBanks: IGetQuestionBankListResponse[];
  setSelectedQuestionBanks: (questions: IGetQuestionBankListResponse[]) => void;
  deleteFinalQuestionBank: (id: number | string) => void;
}

export const useQuestionBankTableListStore = create<IQuestionBankTableList>()(
  (set, get) => ({
    selectedQuestionBanksFinal: [],
    selectedQuestionBanks: [],
    setSelectedQuestionBanks: (question) => {
      if (!question.length) {
        set({ selectedQuestionBanks: [] });
        return;
      }

      if (question.length === 1) {
        const currentData = get().selectedQuestionBanks;
        const alreadySelected = currentData.find(
          (it) => it.id === question[0].id
        );

        if (alreadySelected) {
          set({
            selectedQuestionBanks: currentData.filter(
              (it) => it.id === question[0].id
            ),
          });
        } else {
          set({ selectedQuestionBanks: [...currentData, ...question] });
        }
        return;
      }

      // save all question
      set({ selectedQuestionBanks: question });
    },
    setSelectedQuestionBanksFinal: () => {
      set({ selectedQuestionBanksFinal: get().selectedQuestionBanks });
    },
    deleteFinalQuestionBank: (id) => {
      const currentData = get().selectedQuestionBanksFinal;

      set({
        selectedQuestionBanksFinal: currentData.filter((it) => it.id !== id),
      });
    },
  })
);
