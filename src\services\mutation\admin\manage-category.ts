import {
  IGetListCategoryQuery,
  IGetListSubCategoryQuery,
} from "@/interfaces/admin/manage-category/list";
import {
  ICreateCategoryPayload,
  ICreateSubCategoryPayload,
  IUpdateCategoryPayload,
  IUpdateSubCategoryPayload,
} from "@/interfaces/admin/manage-category/new";
import {
  apiDownloadListCategoryFile,
  apiDownloadListSubCategoryFile,
  apiInsertCategory,
  apiInsertSubCategory,
  apiUpdateCategory,
  apiUpdateSubCategory,
} from "@/services/api/admin/manage-category";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

export const useInsertCategoryMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["insert-category"],
    mutationFn: async (body: ICreateCategoryPayload) => {
      return await apiInsertCategory(body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["category"],
      });
    },
  });
};

export const useInsertSubCategoryMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["insert-subcategory"],
    mutationFn: async (body: ICreateSubCategoryPayload) => {
      return await apiInsertSubCategory(body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["subcategory"],
      });
    },
  });
};

export const useUpdateCategoryMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["update-category"],
    mutationFn: async (body: IUpdateCategoryPayload) => {
      return await apiUpdateCategory(body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["category"],
      });
      queryClient.invalidateQueries({
        queryKey: ["subcategory"],
      });
    },
  });
};

export const useUpdateSubCategoryMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["update-subcategory"],
    mutationFn: async (body: IUpdateSubCategoryPayload) => {
      return await apiUpdateSubCategory(body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["subcategory"],
      });
    },
  });
};

const downloadfile = (result: { filename: string; file: string }) => {
  const byteCharacters = atob(result.file);
  const byteNumbers = new Array(byteCharacters.length)
    .fill(0)
    .map((_, i) => byteCharacters.charCodeAt(i));
  const byteArray = new Uint8Array(byteNumbers);

  const blob = new Blob([byteArray]);
  const url = window.URL.createObjectURL(blob);

  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", result.filename);
  document.body.appendChild(link);
  link.click();
  link.remove();
  window.URL.revokeObjectURL(url);
};

export const useDownloadListCategoryMutation = () => {
  return useMutation({
    mutationKey: ["category-file"],
    mutationFn: async (params: IGetListCategoryQuery) => {
      const result = await apiDownloadListCategoryFile(params);

      downloadfile(result);
    },
    onError: () => {
      toast.error("Failed to download file. Try again!");
    },
  });
};

export const useDownloadListSubCategoryMutation = () => {
  return useMutation({
    mutationKey: ["subcategory-file"],
    mutationFn: async (params: IGetListSubCategoryQuery) => {
      const result = await apiDownloadListSubCategoryFile(params);

      downloadfile(result);
    },
    onError: () => {
      toast.error("Failed to download file. Try again!");
    },
  });
};
