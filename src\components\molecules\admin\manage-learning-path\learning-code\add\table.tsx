"use client";

import React, { useEffect, useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { Check, Trash2 } from "lucide-react";
import { cn } from "@/utils/common";
import { BaseButton } from "@/components/atoms/button";
import { useShallow } from "zustand/react/shallow";
import { IGetJobPositionListResponse } from "@/interfaces/admin/manage-job/list";
import toast from "react-hot-toast";
import ManageLearningPathTableHeader from "./table-header";
import { DataTable } from "@/components/molecules/global/table";
import { FormProvider, useForm } from "react-hook-form";
import AddLearningCodeModal from "./add-modal/new";
import AddLearningCodeFooter from "./footer";
import {
  createLearningCodeFormSchema,
  ICreateLearningCodeForm,
} from "@/interfaces/admin/manage-learning-path/new";
import { yupResolver } from "@hookform/resolvers/yup";
import { useManageLearningPathFilterStore } from "@/store/admin/manage-learning-path/filter";
import { useInsertLearningCodeMutation } from "@/services/mutation/learning-path/new";
import { useRouter, useSearchParams } from "next/navigation";
import { useGetLearningCodeDetailQuery } from "@/services/query/learning-path/detail";
import { useUpdateLearningCodeMutation } from "@/services/mutation/learning-path/update";

const AddLearningCodeTable = () => {
  const {
    currentData,
    selectedJobPositions,
    setSelectedJobPositions,
    setCurrentData,
    selectedJobPagination,
    setSelectedJobPagination,
    selectedJobQuery,
  } = useManageLearningPathFilterStore(
    useShallow(
      ({
        currentData,
        selectedJobPositions,
        setSelectedJobPositions,
        setCurrentData,
        selectedJobPagination,
        setSelectedJobPagination,
        selectedJobQuery,
      }) => ({
        currentData,
        selectedJobPositions,
        setSelectedJobPositions,
        setCurrentData,
        selectedJobPagination,
        setSelectedJobPagination,
        selectedJobQuery,
      })
    )
  );
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = searchParams.get("id");

  const createLearningCode = useInsertLearningCodeMutation();
  const updateLearningCode = useUpdateLearningCodeMutation();
  const learningCodeDetail = useGetLearningCodeDetailQuery({
    id: id ? +id : null,
  });
  const [jobs, setJobs] =
    useState<IGetJobPositionListResponse[]>(selectedJobPositions);

  const columns: ColumnDef<IGetJobPositionListResponse>[] = [
    {
      accessorKey: "job_id",
      header: "Job Position ID",
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.job_id}
          </span>
        );
      },
    },
    { accessorKey: "job_name", header: "Job Position Name" },
    { accessorKey: "job_position_type", header: "Job Type" },
    { accessorKey: "department_name", header: "Department" },
    { accessorKey: "job_function", header: "Job Function" },
    {
      accessorKey: "level",
      header: "Starting Learning Level",
    },
    {
      accessorKey: "is_need_neop",
      header: "Need NEOP?",
      cell({ row }) {
        return (
          <Check
            size={24}
            className={cn(
              row.original.is_need_neop
                ? "bg-orange-400 text-white"
                : "bg-gray-400 text-white",
              "rounded-full p-1"
            )}
            strokeWidth={3}
            absoluteStrokeWidth
          />
        );
      },
    },
    {
      accessorKey: "is_need_welcoming_kit",
      header: "Need Welcoming Kit?",
      cell({ row }) {
        return (
          <Check
            size={24}
            className={cn(
              row.original.is_need_welcoming_kit
                ? "bg-orange-400 text-white"
                : "bg-gray-400 text-white",
              "rounded-full p-1"
            )}
            strokeWidth={3}
            absoluteStrokeWidth
          />
        );
      },
    },
    {
      accessorKey: "starter_module_priority",
      header: "Starter Module Priority",
    },
    { accessorKey: "entity_name", header: "Entity" },

    {
      accessorKey: "last_updated",
      header: "Last Updated",
      cell: (props) => {
        return (
          <span>
            {props.row.original.last_updated
              ? dayjs(props.row.original.last_updated).format(
                  "DD MMM YYYY HH:mm"
                )
              : "-"}
          </span>
        );
      },
    },
    { accessorKey: "updated_by", header: "Updated By" },

    {
      accessorKey: "id",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center justify-start gap-2">
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => {
                handleDelete(row.original.id);
              }}
              type="button"
            >
              <Trash2 size={24} className="text-gray-700" strokeWidth={2.5} />
            </BaseButton>
          </div>
        );
      },
    },
  ];

  const handlePageChange = (page: number) => {
    setSelectedJobPagination({ ...selectedJobPagination, current_page: page });
  };

  const handleDelete = (id: number) => {
    setSelectedJobPositions(
      selectedJobPositions.filter((item) => item.id !== id)
    );
  };

  const form = useForm({
    resolver: yupResolver(createLearningCodeFormSchema),
    mode: "all",
  });

  const handleSubmit = (data: ICreateLearningCodeForm) => {
    const jobIds = jobs.map((item) => item.id);

    if (!currentData) {
      createLearningCode.mutate(
        {
          ...data,
          job_name_id: jobIds,
        },
        {
          onSuccess: (data) => {
            toast.success(data.message);
            setSelectedJobPositions([]);
            router.back();
          },
          onError: (data) => {
            toast.error(data.message);
          },
        }
      );

      return;
    }

    updateLearningCode.mutate(
      {
        form: {
          ...data,
          job_name_id: jobIds,
        },
        params: {
          id: currentData,
        },
      },
      {
        onSuccess: (data) => {
          toast.success(data.message);
          setSelectedJobPositions([]);
          router.back();
        },
        onError: (data) => {
          toast.error(data.message);
        },
      }
    );
  };

  useEffect(() => {
    if (learningCodeDetail.data) {
      const data = learningCodeDetail.data.data;

      form.reset({
        learning_code: data.code ?? undefined,
        learning_code_name: data.name ?? undefined,
        job_name_id: data.related_job.map((item) => item.id),
      });

      setCurrentData(data.ID);

      setSelectedJobPositions(data.related_job);
    }
  }, [learningCodeDetail.data]);

  useEffect(() => {
    setSelectedJobPagination({
      ...selectedJobPagination,
      current_page: 1,
      total_data: selectedJobPositions.length,
      total_page: Math.ceil(selectedJobPositions.length / 10),
    });
  }, [selectedJobPositions.length]);

  const currentPage = selectedJobPagination.current_page ?? 1;
  const startIndex = (currentPage - 1) * 10;
  const endIndex = currentPage * 10;

  useEffect(() => {
    if (!selectedJobQuery) return;

    const {
      search,
      search_by, // "job_name" | "job_function"
      job_position_type, // string
      is_neop, // boolean
      starter_modul_priority, // string (param)
      level, // number
      limit, // optional
    } = selectedJobQuery;

    const toStr = (v: unknown) => (v ?? "").toString().toLowerCase().trim();
    const lim = typeof limit === "number" && limit > 0 ? limit : 10;

    // --- APPLY FILTERS ---
    const filtered = (selectedJobPositions ?? []).filter((item) => {
      // search by specific field
      if (search && search_by) {
        const target = toStr((item as any)[search_by]);
        if (!target.includes(toStr(search))) return false;
      }

      // job_position_type exact match (case-insensitive)
      if (job_position_type) {
        if (toStr(item.job_position_type) !== toStr(job_position_type))
          return false;
      }

      // level exact match
      if (typeof level === "number") {
        if (item.level !== level) return false;
      }

      if (typeof is_neop === "boolean") {
        if (Boolean(item.is_need_neop) !== is_neop) return false;
      }

      if (starter_modul_priority) {
        if (
          toStr(item.starter_module_priority) !== toStr(starter_modul_priority)
        )
          return false;
      }

      return true;
    });

    setJobs(filtered);

    setSelectedJobPagination({
      ...selectedJobPagination,
      current_page: 1,
      total_data: filtered.length,
      total_page: Math.max(1, Math.ceil(filtered.length / lim)),
    });
  }, [JSON.stringify(selectedJobQuery), selectedJobPositions]);

  useEffect(() => {
    setJobs(selectedJobPositions);
    setSelectedJobPagination({
      ...selectedJobPagination,
      current_page: 1,
      total_data: selectedJobPositions.length,
      total_page: Math.max(1, Math.ceil(selectedJobPositions.length / 10)),
    });
  }, [selectedJobPositions.length]);

  return (
    <FormProvider {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className="flex flex-col gap-4"
      >
        <div className="bg-white p-2 rounded-md flex flex-col gap-4">
          <ManageLearningPathTableHeader />

          {jobs.length ? (
            <DataTable
              columns={columns}
              data={jobs.slice(startIndex, endIndex)}
              pagination={selectedJobPagination}
              onPageChange={handlePageChange}
            />
          ) : (
            <div className="flex flex-col justify-center items-center w-full h-[49dvh]  bg-gray-100 text-gray-400 rounded-md">
              <span>No Job Position selected</span>
              <span>Please select the Job Position first</span>
            </div>
          )}
        </div>
        <AddLearningCodeFooter />
        <AddLearningCodeModal />
      </form>
    </FormProvider>
  );
};

export default AddLearningCodeTable;
