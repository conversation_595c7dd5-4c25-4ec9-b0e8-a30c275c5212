import { BaseButton } from "@/components/atoms/button";
import PillDropdown from "@/components/atoms/dropdown/pill-dropdown";
import { IGetSectionListResponse } from "@/interfaces/admin/manage-section/list";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { Pencil, Trash2 } from "lucide-react";

interface Props {
  onEdit: (section: IGetSectionListResponse) => void;
  onDelete: (section: IGetSectionListResponse) => void;
}

export const getSectionColumns = ({
  onEdit,
  onDelete,
}: Props): ColumnDef<IGetSectionListResponse>[] => {
  return [
    {
      accessorKey: "id",
      header: "Section ID",
      cell({ row }) {
        return <div className="text-muted-foreground">{row.original.id}</div>;
      },
    },
    {
      accessorKey: "section_name",
      header: "Section Name",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.section_name}
          </div>
        );
      },
    },
    {
      accessorKey: "section_type",
      header: "Section Type",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.section_type}
          </div>
        );
      },
    },
    {
      accessorKey: "duration",
      header: "Duration",
      cell({ row }) {
        const duration = row.original.duration; // in seconds
        const hours = Math.floor(duration / 3600);
        const minutes = Math.floor((duration % 3600) / 60);
        const seconds = duration % 60;
        const formattedDuration = duration
          ? `${hours} hrs ${minutes} min ${seconds} sec`
          : "-";
        return <div className="text-muted-foreground">{formattedDuration}</div>;
      },
    },
    {
      accessorKey: "categories",
      header: "Categories",
      cell({ row }) {
        const categories = row.original.categories || [];
        return (
          <PillDropdown
            selected={`${categories.length} Sections`}
            options={categories.map((category) => ({
              value: category.name,
              label: category.name,
            }))}
          />
        );
      },
    },
    {
      accessorKey: "levels",
      header: "Levels",
      cell({ row }) {
        const levels = row.original.levels || [];
        return (
          <PillDropdown
            selected={`${levels.length} Sections`}
            options={levels.map((level) => ({
              value: level.id.toString(),
              label: level.level.toString(),
            }))}
          />
        );
      },
    },
    {
      accessorKey: "tech_competency",
      header: "Technical Competencies",
      cell({ row }) {
        const techCompetencies = row.original.tech_competency || [];
        return (
          <PillDropdown
            selected={`${techCompetencies.length} Sections`}
            options={techCompetencies.map((techCompetency) => ({
              value: techCompetency.id.toString(),
              label: techCompetency.name,
            }))}
          />
        );
      },
    },
    {
      accessorKey: "soft_competency",
      header: "Soft Competencies",
      cell({ row }) {
        const softCompetencies = row.original.soft_competency || [];
        return (
          <PillDropdown
            selected={`${softCompetencies.length} Sections`}
            options={softCompetencies.map((softCompetency) => ({
              value: softCompetency.id.toString(),
              label: softCompetency.name,
            }))}
          />
        );
      },
    },

    {
      accessorKey: "modul",
      header: "Associated Modules",
      cell({ row }) {
        const modules = row.original.modul || [];
        return (
          <div className="text-muted-foreground">
            <PillDropdown
              selected={`${modules.length} Sections`}
              options={modules.map((module) => ({
                value: module.id.toString(),
                label: module.name,
              }))}
            />
          </div>
        );
      },
    },

    {
      accessorKey: "created_at",
      header: "Created At",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {dayjs(row.original.created_at).format("DD/MM/YYYY")}
          </div>
        );
      },
    },
    {
      accessorKey: "created_by",
      header: "Created By",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.created_by ?? "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "updated_at",
      header: "Last Updated",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {dayjs(row.original.updated_at).format("DD/MM/YYYY")}
          </div>
        );
      },
    },
    {
      accessorKey: "updated_by",
      header: "Updated By",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.updated_by ?? "-"}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center gap-2">
            <BaseButton
              variant="ghost"
              size="sm"
              onClick={() => onEdit(row.original)}
              className="h-8 w-8 p-0 hover:bg-blue-50"
            >
              <Pencil className="h-4 w-4 text-blue-600" />
            </BaseButton>
            <BaseButton
              variant="ghost"
              size="sm"
              onClick={() => onDelete(row.original)}
              className="h-8 w-8 p-0 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4 text-red-600" />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};
