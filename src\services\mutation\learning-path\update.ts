import { ICreateLearningCodeForm } from "@/interfaces/admin/manage-learning-path/new";
import {
  IUpdateLearningCodeBody,
  IUpdateLearningCodeParams,
  IUpdateLearningLevelBody,
} from "@/interfaces/admin/manage-learning-path/update";
import {
  apiUpdateLearningCode,
  apiUpdateLearningLevel,
} from "@/services/api/learning-path/update";
import { useMutation } from "@tanstack/react-query";

export const useUpdateLearningCodeMutation = () => {
  return useMutation({
    mutationKey: ["learning-code", "update"],
    mutationFn: async ({
      params,
      form,
    }: {
      params: IUpdateLearningCodeParams;
      form: ICreateLearningCodeForm;
    }) => {
      const body: IUpdateLearningCodeBody = {
        job_name_id: form.job_name_id as number[],
        learning_code: form.learning_code,
        learning_code_name: form.learning_code_name,
      };

      return await apiUpdateLearningCode(params, body);
    },
  });
};

export const useUpdateLearningLevelMutation = () => {
  return useMutation({
    mutationKey: ["learning-level", "update"],
    mutationFn: async ({
      params,
      body,
    }: {
      params: IUpdateLearningCodeParams;
      body: IUpdateLearningLevelBody;
    }) => {
      return await apiUpdateLearningLevel(params, body);
    },
  });
};
