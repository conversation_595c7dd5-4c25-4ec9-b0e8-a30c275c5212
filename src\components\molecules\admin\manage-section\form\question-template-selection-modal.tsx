"use client";

import React, { useState, useEffect } from "react";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseButton } from "@/components/atoms/button";
import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import {
  BaseTable,
  BaseTableBody,
  BaseTableCell,
  BaseTableHead,
  BaseTableHeader,
  BaseTableRow,
} from "@/components/atoms/table";
import { Search, Filter, X, Eye } from "lucide-react";
import { useGetQuestionTemplateListQuery } from "@/services/query/admin/manage-test/question-template";
import {
  IGetQuestionTemplateListResponse,
  TQTSearchBy,
} from "@/interfaces/admin/manage-test/question-template/list";
import {
  useGetCategoryListQuery,
  useGetStartingLevelListQuery,
} from "@/services/query/admin/master";

interface QuestionTemplateSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddQuestions: (template: IGetQuestionTemplateListResponse) => void;
}

const QuestionTemplateSelectionModal: React.FC<
  QuestionTemplateSelectionModalProps
> = ({ isOpen, onClose, onAddQuestions }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchBy, setSearchBy] = useState<TQTSearchBy>("template_name");
  const [categoryFilter, setCategoryFilter] = useState<string>("");
  const [levelFilter, setLevelFilter] = useState<string>("");
  const [templateTypeFilter, setTemplateTypeFilter] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTemplate, setSelectedTemplate] =
    useState<IGetQuestionTemplateListResponse | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // API queries
  const { data: templateData, isLoading } = useGetQuestionTemplateListQuery({
    search: searchQuery || undefined,
    search_by: searchBy,
    category_id: categoryFilter ? parseInt(categoryFilter) : undefined,
    level_id: levelFilter ? parseInt(levelFilter) : undefined,
    template_type: templateTypeFilter || undefined,
    feature: "OnlineLearning",
    page: currentPage,
    limit: 10,
  });

  const { data: categoriesData } = useGetCategoryListQuery();

  const { data: levelsData } = useGetStartingLevelListQuery();

  const templates = templateData?.data || [];
  const pagination = templateData?.pagination;
  const categories = categoriesData?.data || [];
  const levels = levelsData?.data || [];

  useEffect(() => {
    if (!isOpen) {
      setSearchQuery("");
      setCategoryFilter("");
      setLevelFilter("");
      setTemplateTypeFilter("");
      setCurrentPage(1);
      setShowFilters(false);
      setSelectedTemplate(null);
      setShowPreview(false);
    }
  }, [isOpen]);

  const handleSelectTemplate = (template: IGetQuestionTemplateListResponse) => {
    setSelectedTemplate(template);
    setShowPreview(true);
  };

  const handleAddTemplate = () => {
    if (selectedTemplate) {
      onAddQuestions(selectedTemplate);
      onClose();
    }
  };

  const handleClearFilters = () => {
    setSearchQuery("");
    setCategoryFilter("");
    setLevelFilter("");
    setTemplateTypeFilter("");
    setCurrentPage(1);
  };

  const searchByOptions = [
    { value: "template_name", label: "Template Name" },
    { value: "template_id", label: "Template ID" },
    { value: "question", label: "Question" },
    { value: "created_by", label: "Created By" },
    { value: "updated_by", label: "Updated By" },
  ];

  const templateTypes = [
    { value: "quiz", label: "Quiz" },
    { value: "pre_test", label: "Pre Test" },
    { value: "post_test", label: "Post Test" },
    { value: "assessment", label: "Assessment" },
  ];

  return (
    <BaseDialog open={isOpen} onOpenChange={onClose}>
      <BaseDialogContent className="sm:max-w-7xl sm:w-[90vw] max-h-[90vh] overflow-hidden flex flex-col">
        <BaseDialogHeader>
          <BaseDialogTitle className="text-lg font-semibold">
            {showPreview
              ? "Question Template Preview"
              : "Select Question Template"}
          </BaseDialogTitle>
        </BaseDialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          {!showPreview ? (
            <>
              {/* Search and Filter Controls */}
              <div className="space-y-3">
                {/* Search Bar */}
                <div className="flex items-center gap-2">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <BaseInput
                      placeholder="Search templates..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <BaseSelect
                    value={searchBy}
                    onValueChange={(value) => setSearchBy(value as TQTSearchBy)}
                  >
                    <BaseSelectTrigger className="w-48">
                      <BaseSelectValue />
                    </BaseSelectTrigger>
                    <BaseSelectContent>
                      {searchByOptions.map((option) => (
                        <BaseSelectItem key={option.value} value={option.value}>
                          {option.label}
                        </BaseSelectItem>
                      ))}
                    </BaseSelectContent>
                  </BaseSelect>
                  <BaseButton
                    type="button"
                    variant="outline"
                    onClick={() => setShowFilters(!showFilters)}
                    className="flex items-center gap-2"
                  >
                    <Filter className="w-4 h-4" />
                    Filters
                  </BaseButton>
                </div>

                {/* Advanced Filters */}
                {showFilters && (
                  <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Advanced Filters</h4>
                      <BaseButton
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={handleClearFilters}
                        className="text-gray-500"
                      >
                        <X className="w-4 h-4 mr-1" />
                        Clear All
                      </BaseButton>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">
                          Category
                        </label>
                        <BaseSelect
                          value={categoryFilter}
                          onValueChange={setCategoryFilter}
                        >
                          <BaseSelectTrigger>
                            <BaseSelectValue placeholder="All Categories" />
                          </BaseSelectTrigger>
                          <BaseSelectContent>
                            <BaseSelectItem value="">
                              All Categories
                            </BaseSelectItem>
                            {categories.map((category) => (
                              <BaseSelectItem
                                key={category.id}
                                value={category.id.toString()}
                              >
                                {category.category_name}
                              </BaseSelectItem>
                            ))}
                          </BaseSelectContent>
                        </BaseSelect>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">
                          Level
                        </label>
                        <BaseSelect
                          value={levelFilter}
                          onValueChange={setLevelFilter}
                        >
                          <BaseSelectTrigger>
                            <BaseSelectValue placeholder="All Levels" />
                          </BaseSelectTrigger>
                          <BaseSelectContent>
                            <BaseSelectItem value="">All Levels</BaseSelectItem>
                            {levels.map((level) => (
                              <BaseSelectItem
                                key={level.id}
                                value={level.id.toString()}
                              >
                                {level.level}
                              </BaseSelectItem>
                            ))}
                          </BaseSelectContent>
                        </BaseSelect>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">
                          Template Type
                        </label>
                        <BaseSelect
                          value={templateTypeFilter}
                          onValueChange={setTemplateTypeFilter}
                        >
                          <BaseSelectTrigger>
                            <BaseSelectValue placeholder="All Types" />
                          </BaseSelectTrigger>
                          <BaseSelectContent>
                            <BaseSelectItem value="">All Types</BaseSelectItem>
                            {templateTypes.map((type) => (
                              <BaseSelectItem
                                key={type.value}
                                value={type.value}
                              >
                                {type.label}
                              </BaseSelectItem>
                            ))}
                          </BaseSelectContent>
                        </BaseSelect>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Results Summary */}
              <div className="text-sm text-gray-600">
                {templates.length} templates found
              </div>

              {/* Templates Table */}
              <div className="flex-1 overflow-auto border rounded-lg">
                {isLoading ? (
                  <div className="flex items-center justify-center h-64">
                    <div className="text-gray-500">Loading templates...</div>
                  </div>
                ) : templates.length === 0 ? (
                  <div className="flex items-center justify-center h-64">
                    <div className="text-gray-500">No templates found</div>
                  </div>
                ) : (
                  <BaseTable>
                    <BaseTableHeader>
                      <BaseTableRow>
                        <BaseTableHead className="w-20">ID</BaseTableHead>
                        <BaseTableHead>Template Name</BaseTableHead>
                        <BaseTableHead className="w-32">Type</BaseTableHead>
                        <BaseTableHead className="w-32">
                          Questions
                        </BaseTableHead>
                        <BaseTableHead className="w-32">Category</BaseTableHead>
                        <BaseTableHead className="w-32">Level</BaseTableHead>
                        <BaseTableHead className="w-24">Action</BaseTableHead>
                      </BaseTableRow>
                    </BaseTableHeader>
                    <BaseTableBody>
                      {templates.map((template) => (
                        <BaseTableRow key={template.id}>
                          <BaseTableCell className="font-medium">
                            {template.id}
                          </BaseTableCell>
                          <BaseTableCell className="max-w-md">
                            <div
                              className="truncate"
                              title={template.question_template_name || ""}
                            >
                              {template.question_template_name}
                            </div>
                          </BaseTableCell>
                          <BaseTableCell>
                            <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">
                              {template.type}
                            </span>
                          </BaseTableCell>
                          <BaseTableCell>
                            <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                              {template.questions.length} questions
                            </span>
                          </BaseTableCell>
                          <BaseTableCell>
                            {template.categories
                              .map((cat) => cat.name)
                              .join(", ")}
                          </BaseTableCell>
                          <BaseTableCell>
                            {template.levels
                              .map((level) => level.name)
                              .join(", ")}
                          </BaseTableCell>
                          <BaseTableCell>
                            <BaseButton
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => handleSelectTemplate(template)}
                              className="flex items-center gap-1"
                            >
                              <Eye className="w-4 h-4" />
                              Preview
                            </BaseButton>
                          </BaseTableCell>
                        </BaseTableRow>
                      ))}
                    </BaseTableBody>
                  </BaseTable>
                )}
              </div>

              {/* Pagination */}
              {/* {pagination && pagination.total_pages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    Page {pagination.current_page} of {pagination.total_pages}
                  </div>
                  <div className="flex gap-2">
                    <BaseButton
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={pagination.current_page <= 1}
                    >
                      Previous
                    </BaseButton>
                    <BaseButton
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => prev + 1)}
                      disabled={pagination.current_page >= pagination.total_pages}
                    >
                      Next
                    </BaseButton>
                  </div>
                </div>
              )} */}
            </>
          ) : (
            /* Template Preview */
            selectedTemplate && (
              <div className="space-y-4">
                {/* Template Info */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-blue-900 mb-2">
                    {selectedTemplate.question_template_name}
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-blue-700 font-medium">Type:</span>
                      <div className="text-blue-800">
                        {selectedTemplate.type}
                      </div>
                    </div>
                    <div>
                      <span className="text-blue-700 font-medium">
                        Questions:
                      </span>
                      <div className="text-blue-800">
                        {selectedTemplate.questions.length}
                      </div>
                    </div>
                    <div>
                      <span className="text-blue-700 font-medium">
                        Categories:
                      </span>
                      <div className="text-blue-800">
                        {selectedTemplate.categories
                          .map((cat) => cat.name)
                          .join(", ")}
                      </div>
                    </div>
                    <div>
                      <span className="text-blue-700 font-medium">Levels:</span>
                      <div className="text-blue-800">
                        {selectedTemplate.levels
                          .map((level) => level.name)
                          .join(", ")}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Questions Preview */}
                <div className="flex-1 overflow-auto border rounded-lg">
                  <BaseTable>
                    <BaseTableHeader>
                      <BaseTableRow>
                        <BaseTableHead className="w-16">No</BaseTableHead>
                        <BaseTableHead className="w-20">ID</BaseTableHead>
                        <BaseTableHead>Question</BaseTableHead>
                      </BaseTableRow>
                    </BaseTableHeader>
                    <BaseTableBody>
                      {selectedTemplate.questions.map((question, index) => (
                        <BaseTableRow key={question.id}>
                          <BaseTableCell className="font-medium">
                            {index + 1}
                          </BaseTableCell>
                          <BaseTableCell>{question.id}</BaseTableCell>
                          <BaseTableCell className="max-w-md">
                            <div
                              className="truncate"
                              title={question.question || ""}
                            >
                              {question.question}
                            </div>
                          </BaseTableCell>
                        </BaseTableRow>
                      ))}
                    </BaseTableBody>
                  </BaseTable>
                </div>
              </div>
            )
          )}
        </div>

        {/* Footer Actions */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="text-sm text-gray-600">
            {showPreview && selectedTemplate && (
              <span>
                {selectedTemplate.questions.length} questions in this template
              </span>
            )}
          </div>
          <div className="flex gap-2">
            {showPreview ? (
              <>
                <BaseButton
                  type="button"
                  variant="outline"
                  onClick={() => setShowPreview(false)}
                >
                  Back to List
                </BaseButton>
                <BaseButton type="button" onClick={handleAddTemplate}>
                  Add This Template
                </BaseButton>
              </>
            ) : (
              <BaseButton type="button" variant="outline" onClick={onClose}>
                Cancel
              </BaseButton>
            )}
          </div>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default QuestionTemplateSelectionModal;
