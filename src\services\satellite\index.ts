import axios from "axios";
import { env } from "../config/env";
import { clearSession, getSession, setSession } from "../session/session";
import { apiRefreshToken } from "../api/login/user";
import { detectWafBlock } from "./waf-detector";
import { logWafBlock } from "./waf-logger";

export const api = axios.create({
  baseURL: env.API.URL,
  withCredentials: true,
  headers: {
    apikey: env.API.KEY,
    "X-Content-Type-Options": "nosniff",
    "X-XSS-Protection": "1; mode=block",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
    "X-Frame-Options": "SAMEORIGIN",
  },
});

api.interceptors.request.use(
  async function (config) {
    const session = await getSession();
    if (session) {
      config.headers["Authorization"] = `Bearer ${session.accessToken}`;
    }
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
  //   { synchronous: true, runWhen: () => /* This function returns true */}
);

api.interceptors.response.use(
  async function onFulfilled(response) {
    // Only perform WAF detection if logging is enabled (performance optimization)
    if (env.WAF_LOGGING) {
      const wafDetection = detectWafBlock(response, null);

      if (wafDetection.isWafBlock) {
        // Log the WAF block to database
        await logWafBlock(
          response.config.method?.toUpperCase() || "UNKNOWN",
          response.config.url || "UNKNOWN",
          response.config.baseURL || "",
          response.config.headers || {},
          response.config.data,
          wafDetection.responseData,
          wafDetection.supportId,
          wafDetection.detectionMethod
        );

        // Throw an error to handle this as a failed request
        const wafError = new Error("Request blocked by WAF");
        (wafError as any).isWafBlock = true;
        (wafError as any).response = response;
        throw wafError;
      }
    }

    return response;
  },
  async function onRejected(error) {
    console.log(error?.response.data.errors);

    // Only perform WAF detection if logging is enabled (performance optimization)
    if (env.WAF_LOGGING) {
      const wafDetection = detectWafBlock(null, error);

      if (wafDetection.isWafBlock) {
        // Log the WAF block to database
        await logWafBlock(
          error.config?.method?.toUpperCase() || "UNKNOWN",
          error.config?.url || "UNKNOWN",
          error.config?.baseURL || "",
          error.config?.headers || {},
          error.config?.data,
          wafDetection.responseData,
          wafDetection.supportId,
          wafDetection.detectionMethod
        );

        // Mark this error as a WAF block for easier handling
        error.isWafBlock = true;
      }
    }

    if (error.response?.status === 401 && !error.isWafBlock) {
      try {
        const res = await apiRefreshToken();
        if (res.status === true) {
          setSession(res.data.token);
          // retry the request
          return api.request(error.config);
        }
      } catch {
        // refresh token failed
        clearSession();
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);
