'use server';

import { api } from '@/services/satellite';
import { handleAxiosError } from '@/utils/common/axios';
import { IGlobalResponseDto } from '@/interfaces/global/response';
import {
  IGetQuestionTemplateListQuery,
  IGetQuestionTemplateListResponse,
} from '@/interfaces/admin/manage-test/question-template/list';
import { IQuestionTemplateDetailResponse } from '@/interfaces/admin/manage-test/question-template/detail';
import {
  IUpdateQuestionTemplateBody,
  IUpdateQuestionTemplateParams,
} from '@/interfaces/admin/manage-test/question-template/update';
import { ICreateQuestionTemplateBody } from '@/interfaces/admin/manage-test/question-template/new';

const BASE = '/cms/admin/learning/question-template';

export const apiGetQuestionTemplateList = async (
  query?: IGetQuestionTemplateListQuery
) => {
  try {
    const res = await api.get<
      IGlobalResponseDto<IGetQuestionTemplateListResponse[]>
    >(`${BASE}/list`, { params: query });
    return res.data;
  } catch (err) {
    throw handleAxiosError(err);
  }
};

export const apiGetQuestionTemplateDetail = async (id: number) => {
  try {
    const res = await api.get<
      IGlobalResponseDto<IQuestionTemplateDetailResponse>
    >(`${BASE}/detail/${id}`);
    return res.data;
  } catch (err) {
    throw handleAxiosError(err);
  }
};

export const apiCreateQuestionTemplate = async (
  body: ICreateQuestionTemplateBody
) => {
  try {
    const res = await api.post<IGlobalResponseDto<any>>(`${BASE}/insert`, body);
    return res.data;
  } catch (err) {
    throw handleAxiosError(err);
  }
};

export const apiUpdateQuestionTemplate = async (
  params: IUpdateQuestionTemplateParams,
  body: IUpdateQuestionTemplateBody
) => {
  try {
    const res = await api.post<IGlobalResponseDto<any>>(
      `${BASE}/update/${params.id}`,
      body
    );
    return res.data;
  } catch (err) {
    throw handleAxiosError(err);
  }
};

export const apiDeleteQuestionTemplate = async (
  params: IUpdateQuestionTemplateParams
) => {
  try {
    const res = await api.post<IGlobalResponseDto<any>>(
      `${BASE}/delete/${params.id}`
    );
    return res.data;
  } catch (err) {
    throw handleAxiosError(err);
  }
};
