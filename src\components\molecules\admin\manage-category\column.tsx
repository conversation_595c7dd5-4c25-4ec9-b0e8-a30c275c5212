import { BaseButton } from "@/components/atoms/button";
import {
  ICategory,
  ISubCategory,
} from "@/interfaces/admin/manage-category/list";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { Pencil, Trash2 } from "lucide-react";

interface Props {
  onEdit: (id: ICategory | ISubCategory) => void;
  onDelete: (id: ICategory | ISubCategory) => void;
}

export const getColumnsManageCategory = ({
  onEdit,
  onDelete,
}: Props): ColumnDef<ICategory>[] => {
  return [
    {
      accessorKey: "id",
      header: "Category ID",
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.id}
          </span>
        );
      },
    },
    { accessorKey: "category_name", header: "Category Name" },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell(props) {
        const createdAt = props.row.original.created_at;
        return (
          <span>
            {createdAt ? dayjs(createdAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "created_by", header: "Created By" },
    {
      accessorKey: "last_updated",
      header: "Last Updated",
      cell(props) {
        const updatedAt = props.row.original.last_updated;

        return (
          <span>
            {updatedAt ? dayjs(updatedAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "updated_by", header: "Updated By" },

    {
      accessorKey: "id",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center justify-start gap-2">
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => onEdit(row.original)}
            >
              <Pencil
                size={24}
                className="fill-gray-700 text-white"
                strokeWidth={1}
              />
            </BaseButton>
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => onDelete(row.original)}
            >
              <Trash2 size={24} className="text-gray-700" strokeWidth={2.5} />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};

export const getColumnsManageSubCategory = ({
  onEdit,
  onDelete,
}: Props): ColumnDef<ISubCategory>[] => {
  return [
    {
      accessorKey: "id",
      header: "Sub Category ID",
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.id}
          </span>
        );
      },
    },
    { accessorKey: "subcategory_name", header: "Sub Category Name" },
    { accessorKey: "category_name", header: "Category Name" },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell(props) {
        const createdAt = props.row.original.created_at;
        return (
          <span>
            {createdAt ? dayjs(createdAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "created_by", header: "Created By" },
    {
      accessorKey: "updated_at",
      header: "Last Updated",
      cell(props) {
        const updatedAt = props.row.original.last_updated;

        return (
          <span>
            {updatedAt ? dayjs(updatedAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "updated_by", header: "Updated By" },

    {
      accessorKey: "id",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center justify-start gap-2">
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => onEdit(row.original)}
            >
              <Pencil
                size={20}
                className="fill-gray-700 text-white"
                strokeWidth={1}
              />
            </BaseButton>
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => onDelete(row.original)}
            >
              <Trash2 size={20} className="text-gray-700" strokeWidth={2.5} />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};
