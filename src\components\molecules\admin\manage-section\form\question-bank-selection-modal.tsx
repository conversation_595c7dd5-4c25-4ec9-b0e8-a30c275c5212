"use client";

import { BaseButton } from "@/components/atoms/button";
import { OrangeCheckbox } from "@/components/atoms/checkbox/orange-checkbox";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import {
  BaseTable,
  BaseTableBody,
  BaseTableCell,
  BaseTableHead,
  BaseTableHeader,
  BaseTableRow,
} from "@/components/atoms/table";
import {
  IGetQuestionBankListResponse,
  TSearchBy,
} from "@/interfaces/admin/manage-test/question-bank/list";
import { useGetQuestionBankListQuery } from "@/services/query/admin/manage-test/question-bank";
import {
  useGetCategoryListQuery,
  useGetStartingLevelListQuery,
} from "@/services/query/admin/master";
import { Filter, Search, X } from "lucide-react";
import React, { useEffect, useState } from "react";
// import { useGetCategoryListQuery } from "@/services/query/admin/master/category";
// import { useGetLevelListQuery } from "@/services/query/admin/master/level";

interface QuestionBankSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddQuestions: (questions: IGetQuestionBankListResponse[]) => void;
  excludeIds?: number[];
}

const QuestionBankSelectionModal: React.FC<QuestionBankSelectionModalProps> = ({
  isOpen,
  onClose,
  onAddQuestions,
  excludeIds = [],
}) => {
  const [selectedQuestions, setSelectedQuestions] = useState<number[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchBy, setSearchBy] = useState<TSearchBy>("question");
  const [categoryFilter, setCategoryFilter] = useState<string>("");
  const [levelFilter, setLevelFilter] = useState<string>("");
  const [questionTypeFilter, setQuestionTypeFilter] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);

  // API queries
  const { data: questionBankData, isLoading } = useGetQuestionBankListQuery({
    search: searchQuery || undefined,
    search_by: searchBy,
    category_id: categoryFilter ? parseInt(categoryFilter) : undefined,
    level_id: levelFilter ? parseInt(levelFilter) : undefined,
    question_type: questionTypeFilter || undefined,
    feature: "OnlineLearning",
    page: currentPage,
    limit: 10,
  });

  const { data: categoriesData } = useGetCategoryListQuery();

  const { data: levelsData } = useGetStartingLevelListQuery();

  const questions = questionBankData?.data || [];
  const pagination = questionBankData?.pagination;
  const categories = categoriesData?.data || [];
  const levels = levelsData?.data || [];

  // Filter out excluded questions
  const availableQuestions = questions.filter(
    (q) => !excludeIds.includes(q.id)
  );

  useEffect(() => {
    if (!isOpen) {
      setSelectedQuestions([]);
      setSearchQuery("");
      setCategoryFilter("");
      setLevelFilter("");
      setQuestionTypeFilter("");
      setCurrentPage(1);
      setShowFilters(false);
    }
  }, [isOpen]);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedQuestions(availableQuestions.map((q) => q.id));
    } else {
      setSelectedQuestions([]);
    }
  };

  const handleSelectQuestion = (questionId: number, checked: boolean) => {
    if (checked) {
      setSelectedQuestions((prev) => [...prev, questionId]);
    } else {
      setSelectedQuestions((prev) => prev.filter((id) => id !== questionId));
    }
  };

  const handleAddQuestions = () => {
    const questionsToAdd = availableQuestions.filter((q) =>
      selectedQuestions.includes(q.id)
    );
    onAddQuestions(questionsToAdd);
    onClose();
  };

  const handleClearFilters = () => {
    setSearchQuery("");
    setCategoryFilter("");
    setLevelFilter("");
    setQuestionTypeFilter("");
    setCurrentPage(1);
  };

  const isAllSelected =
    availableQuestions.length > 0 &&
    selectedQuestions.length === availableQuestions.length;

  const searchByOptions = [
    { value: "question", label: "Question" },
    { value: "question_id", label: "Question ID" },
    { value: "option_a", label: "Option A" },
    { value: "option_b", label: "Option B" },
    { value: "option_c", label: "Option C" },
    { value: "option_d", label: "Option D" },
    { value: "correct_answer", label: "Correct Answer" },
    { value: "created_by", label: "Created By" },
    { value: "updated_by", label: "Updated By" },
  ];

  const questionTypes = [
    { value: "multiple_choice", label: "Multiple Choice" },
    { value: "true_false", label: "True/False" },
    { value: "essay", label: "Essay" },
  ];

  return (
    <BaseDialog open={isOpen} onOpenChange={onClose}>
      <BaseDialogContent className="sm:max-w-7xl sm:w-[90vw] max-h-[90vh] overflow-hidden flex flex-col">
        <BaseDialogHeader>
          <BaseDialogTitle className="text-lg font-semibold">
            Select Questions from Question Bank
          </BaseDialogTitle>
        </BaseDialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          {/* Search and Filter Controls */}
          <div className="space-y-3">
            {/* Search Bar */}
            <div className="flex items-center gap-2">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <BaseInput
                  placeholder="Search questions..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <BaseSelect
                value={searchBy}
                onValueChange={(value) => setSearchBy(value as TSearchBy)}
              >
                <BaseSelectTrigger className="w-48">
                  <BaseSelectValue />
                </BaseSelectTrigger>
                <BaseSelectContent>
                  {searchByOptions.map((option) => (
                    <BaseSelectItem key={option.value} value={option.value}>
                      {option.label}
                    </BaseSelectItem>
                  ))}
                </BaseSelectContent>
              </BaseSelect>
              <BaseButton
                type="button"
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="w-4 h-4" />
                Filters
              </BaseButton>
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Advanced Filters</h4>
                  <BaseButton
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleClearFilters}
                    className="text-gray-500"
                  >
                    <X className="w-4 h-4 mr-1" />
                    Clear All
                  </BaseButton>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Category
                    </label>
                    <BaseSelect
                      value={categoryFilter}
                      onValueChange={setCategoryFilter}
                    >
                      <BaseSelectTrigger>
                        <BaseSelectValue placeholder="All Categories" />
                      </BaseSelectTrigger>
                      <BaseSelectContent>
                        <BaseSelectItem value="">All Categories</BaseSelectItem>
                        {categories.map((category) => (
                          <BaseSelectItem
                            key={category.id}
                            value={category.id.toString()}
                          >
                            {category.category_name}
                          </BaseSelectItem>
                        ))}
                      </BaseSelectContent>
                    </BaseSelect>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Level
                    </label>
                    <BaseSelect
                      value={levelFilter}
                      onValueChange={setLevelFilter}
                    >
                      <BaseSelectTrigger>
                        <BaseSelectValue placeholder="All Levels" />
                      </BaseSelectTrigger>
                      <BaseSelectContent>
                        <BaseSelectItem value="">All Levels</BaseSelectItem>
                        {levels.map((level) => (
                          <BaseSelectItem
                            key={level.id}
                            value={level.id.toString()}
                          >
                            {level.level}
                          </BaseSelectItem>
                        ))}
                      </BaseSelectContent>
                    </BaseSelect>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Question Type
                    </label>
                    <BaseSelect
                      value={questionTypeFilter}
                      onValueChange={setQuestionTypeFilter}
                    >
                      <BaseSelectTrigger>
                        <BaseSelectValue placeholder="All Types" />
                      </BaseSelectTrigger>
                      <BaseSelectContent>
                        <BaseSelectItem value="">All Types</BaseSelectItem>
                        {questionTypes.map((type) => (
                          <BaseSelectItem key={type.value} value={type.value}>
                            {type.label}
                          </BaseSelectItem>
                        ))}
                      </BaseSelectContent>
                    </BaseSelect>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Results Summary */}
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>
              {availableQuestions.length} questions available
              {excludeIds.length > 0 &&
                ` (${excludeIds.length} already selected)`}
            </span>
            <span>{selectedQuestions.length} selected</span>
          </div>

          {/* Questions Table */}
          <div className="flex-1 overflow-auto border rounded-lg">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-gray-500">Loading questions...</div>
              </div>
            ) : availableQuestions.length === 0 ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-gray-500">No questions found</div>
              </div>
            ) : (
              <BaseTable>
                <BaseTableHeader>
                  <BaseTableRow>
                    <BaseTableHead className="w-12">
                      <OrangeCheckbox
                        checked={isAllSelected}
                        onCheckedChange={handleSelectAll}
                      />
                    </BaseTableHead>
                    <BaseTableHead className="w-20">ID</BaseTableHead>
                    <BaseTableHead>Question</BaseTableHead>
                    <BaseTableHead className="w-32">Type</BaseTableHead>
                    <BaseTableHead className="w-32">Category</BaseTableHead>
                    <BaseTableHead className="w-32">Level</BaseTableHead>
                  </BaseTableRow>
                </BaseTableHeader>
                <BaseTableBody>
                  {availableQuestions.map((question) => (
                    <BaseTableRow key={question.id}>
                      <BaseTableCell>
                        <OrangeCheckbox
                          checked={selectedQuestions.includes(question.id)}
                          onCheckedChange={(checked) =>
                            handleSelectQuestion(
                              question.id,
                              checked as boolean
                            )
                          }
                        />
                      </BaseTableCell>
                      <BaseTableCell className="font-medium">
                        {question.id}
                      </BaseTableCell>
                      <BaseTableCell className="max-w-md">
                        <div
                          className="truncate"
                          title={question.question || ""}
                        >
                          {question.question}
                        </div>
                      </BaseTableCell>
                      <BaseTableCell>
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                          {question.type}
                        </span>
                      </BaseTableCell>
                      <BaseTableCell>
                        {question.categories.map((cat) => cat.name).join(", ")}
                      </BaseTableCell>
                      <BaseTableCell>
                        {question.levels.map((level) => level.level).join(", ")}
                      </BaseTableCell>
                    </BaseTableRow>
                  ))}
                </BaseTableBody>
              </BaseTable>
            )}
          </div>

          {/* Pagination */}
          {/* {pagination && pagination.total_pages > 1 && (
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Page {pagination.current_page} of {pagination.total_pages}
              </div>
              <div className="flex gap-2">
                <BaseButton
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(1, prev - 1))
                  }
                  disabled={pagination.current_page <= 1}
                >
                  Previous
                </BaseButton>
                <BaseButton
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage((prev) => prev + 1)}
                  disabled={pagination.current_page >= pagination.total_pages}
                >
                  Next
                </BaseButton>
              </div>
            </div>
          )} */}
        </div>

        {/* Footer Actions */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="text-sm text-gray-600">
            {selectedQuestions.length} question(s) selected
          </div>
          <div className="flex gap-2">
            <BaseButton type="button" variant="outline" onClick={onClose}>
              Cancel
            </BaseButton>
            <BaseButton
              type="button"
              onClick={handleAddQuestions}
              disabled={selectedQuestions.length === 0}
            >
              Add Selected Questions ({selectedQuestions.length})
            </BaseButton>
          </div>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default QuestionBankSelectionModal;
