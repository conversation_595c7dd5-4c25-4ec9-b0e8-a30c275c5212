import { IGetCategoryListQuery } from "@/interfaces/admin/master/category";
import { IGetImageRepositoryListQuery } from "@/interfaces/admin/master/image-category";
import { IGetStartingLevelListQuery } from "@/interfaces/admin/master/starting-level";
import { IGetMaterialRepositoryListQuery } from "@/interfaces/admin/master/material-repository";
import { IGetCompetencyListQuery } from "@/interfaces/admin/master/competency";
import {
  apiGetCategoryList,
  apiGetImageRepository,
  apiGetStartingLevelList,
} from "@/services/api/master/starting-level";
import { apiGetMaterialRepositoryList } from "@/services/api/master/material-repository";
import { apiGetCompetencyList } from "@/services/api/master/competency";
import { useQuery } from "@tanstack/react-query";

export const useGetStartingLevelListQuery = (
  params?: IGetStartingLevelListQuery,
  enabled = true
) => {
  return useQuery({
    queryKey: ["starting-level", params],
    queryFn: async () => {
      return await apiGetStartingLevelList(params);
    },
    enabled,
  });
};

export const useGetCategoryListQuery = (
  params?: IGetCategoryListQuery,
  enabled = true
) => {
  return useQuery({
    queryKey: ["category", params],
    queryFn: async () => {
      return await apiGetCategoryList(params);
    },
    enabled,
  });
};

export const useGetMaterialRepositoryListQuery = (
  params?: IGetMaterialRepositoryListQuery,
  enabled = true
) => {
  return useQuery({
    queryKey: ["material-repository", params],
    queryFn: async () => {
      return await apiGetMaterialRepositoryList(params);
    },
    enabled,
  });
};

export const useGetCompetencyListQuery = (
  params?: IGetCompetencyListQuery,
  enabled = true
) => {
  return useQuery({
    queryKey: ["competency", params],
    queryFn: async () => {
      return await apiGetCompetencyList(params);
    },
    enabled,
  });
};

export const useGetMasterImageRepository = (
  params?: IGetImageRepositoryListQuery,
  enabled = true
) => {
  return useQuery({
    queryKey: ["image-repository", params],
    queryFn: async () => {
      return await apiGetImageRepository(params);
    },
    enabled,
  });
};
