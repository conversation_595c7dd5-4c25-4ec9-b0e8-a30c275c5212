import {
  IGetQuestionBankListQuery,
  TFeature,
  TSearchBy,
} from '@/interfaces/admin/manage-test/question-bank/list';
import { create } from 'zustand';

interface IQuestionBankFilterStore {
  openFilter: boolean;
  setOpenFilter: (open: boolean) => void;
  query: IGetQuestionBankListQuery;
  draft: IGetQuestionBankListQuery;
  setDraft: (q: Partial<IGetQuestionBankListQuery>) => void;
  applyDraft: () => void;
  resetDraft: () => void;
  initDraftFromQuery: () => void;
  setQuery: (q: Partial<IGetQuestionBankListQuery>) => void;
  resetQuery: () => void;
  setSearch: (val?: string | number) => void;
  setSearchBy: (val: TSearchBy) => void;
  setFeature: (val: TFeature) => void;
}

const DEFAULT_QUERY: IGetQuestionBankListQuery = {
  page: 1,
  limit: 10,
  feature: 'OnlineLearning',
  search_by: 'question',
};

export const useQuestionBankFilterStore = create<IQuestionBankFilterStore>()(
  (set, get) => ({
    openFilter: false,
    setOpenFilter: (open) => set({ openFilter: open }),

    query: { ...DEFAULT_QUERY },
    draft: { ...DEFAULT_QUERY },

    setDraft: (q) => set({ draft: { ...(get().draft as any), ...q } }),

    applyDraft: () => set({ query: { ...(get().draft as any) } }),

    resetDraft: () => set({ draft: { ...DEFAULT_QUERY } }),

    initDraftFromQuery: () => set({ draft: { ...(get().query as any) } }),

    setQuery: (q) => set({ query: { ...(get().query as any), ...q } }),
    resetQuery: () => set({ query: { ...DEFAULT_QUERY } }),

    setSearch: (val) =>
      set((state) => {
        const nextSearchBy = state.draft.search_by ?? state.query.search_by;
        return {
          draft: { ...state.draft, page: 1, search: val },
          query: {
            ...state.query,
            page: 1,
            search: val,
            search_by: nextSearchBy,
          },
        };
      }),

    setSearchBy: (val) =>
      set((state) => ({
        draft: { ...state.draft, page: 1, search_by: val },
      })),

    setFeature: (val) =>
      set({ draft: { ...(get().draft as any), feature: val } }),
  })
);
