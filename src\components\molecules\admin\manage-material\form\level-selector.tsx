"use client";
import { BaseLabel } from "@/components/atoms/label";
import MultipleSelector from "@/components/atoms/multiselect";
import { useGetStartingLevelListQuery } from "@/services/query/admin/master";
import { Controller, useFormContext } from "react-hook-form";
import { cn } from "@/lib/utils";

interface LevelSelectorProps {
  labelClassName?: string;
  showLevel?: boolean;
}

const LevelSelector = ({
  labelClassName,
  showLevel = false,
}: LevelSelectorProps) => {
  const form = useFormContext();
  const { data: level, isPending: pendingLevel } =
    useGetStartingLevelListQuery();

  const levelOptions =
    level?.data?.map((level) => {
      if (showLevel) {
        return {
          label: level.level?.toString() ?? "",
          value: level.id.toString(),
        };
      }
      return {
        label: level.name ?? "",
        value: level.id.toString(),
      };
    }) ?? [];

  return (
    <div className="space-y-3 flex flex-col">
      <BaseLabel className={cn("text-sm font-medium", labelClassName)}>
        Level
      </BaseLabel>

      <div className="flex flex-col">
        <Controller
          name="level"
          render={({ field }) => {
            return (
              <MultipleSelector
                isPending={pendingLevel}
                value={field.value}
                options={levelOptions ?? []}
                onChange={field.onChange}
                placeholder="Select Level"
                badgeClassName="bg-base-gray-20 text-comp-content-primary"
                loadingIndicator={
                  <p className="py-2 text-center text-sm leading-10 text-muted-foreground">
                    loading...
                  </p>
                }
                emptyIndicator={
                  <p className="w-full text-center text-lg leading-10 text-muted-foreground">
                    no results found.
                  </p>
                }
              />
            );
          }}
        />
        {form.formState.errors.level && (
          <p className="text-red-500 text-sm mt-1">
            {form.formState.errors.level?.message as string}
          </p>
        )}
      </div>
    </div>
  );
};

export default LevelSelector;
