'use client';

import { BaseInput } from '@/components/atoms/input';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/atoms/select';
import { BaseSeparator } from '@/components/atoms/separator';
import { Search } from 'lucide-react';
import React from 'react';
import { useShallow } from 'zustand/react/shallow';
import lodash from 'lodash';
import { TSearchBy } from '@/interfaces/admin/manage-test/question-bank/list';
import { useQuestionBankFilterStore } from '@/store/admin/manage-test/question-bank/filter';

export const SEARCH_BY_OPTIONS: { label: string; value: TSearchBy }[] = [
  { label: 'Question ID', value: 'question_id' },
  { label: 'Question', value: 'question' },
  { label: 'Option A', value: 'option_a' },
  { label: 'Option B', value: 'option_b' },
  { label: 'Option C', value: 'option_c' },
  { label: 'Option D', value: 'option_d' },
  { label: 'Correct Answer', value: 'correct_answer' },
  { label: 'Created By', value: 'created_by' },
  { label: 'Updated By', value: 'updated_by' },
];

const QuestionBankTableHeaderSearch = () => {
  const { draft, setSearch, setSearchBy } = useQuestionBankFilterStore(
    useShallow(({ draft, setSearch, setSearchBy }) => ({
      draft,
      setSearch,
      setSearchBy,
    }))
  );

  return (
    <div className="text-[#3C3C3C] font-semibold rounded-lg flex items-center gap-1 relative w-[40%] bg-white px-3">
      <div>
        <BaseSelect
          value={draft.search_by ?? 'question'}
          onValueChange={(v) => setSearchBy(v as any)}
        >
          <BaseSelectTrigger className="w-44 border-none px-1">
            <BaseSelectValue placeholder="Search By" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {SEARCH_BY_OPTIONS.map((opt) => (
              <BaseSelectItem
                key={opt.value}
                value={opt.value}
              >
                {opt.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>
      <BaseSeparator orientation="vertical" />
      <BaseInput
        className="border-none h-12 focus-visible:border-none focus-visible:ring-0"
        onChange={lodash.debounce((e: React.ChangeEvent<HTMLInputElement>) => {
          const raw = e?.target?.value;
          setSearch(raw);
        }, 800)}
      />
      <Search size={24} />
      <BaseSeparator
        orientation="vertical"
        className="bg-red-500 w-1"
      />
    </div>
  );
};

export default QuestionBankTableHeaderSearch;
