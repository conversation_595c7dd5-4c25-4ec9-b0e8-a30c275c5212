'use client';

import { BaseButton } from '@/components/atoms/button';
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from '@/components/atoms/dialog';
import { BaseInput } from '@/components/atoms/input';
import { BaseLabel } from '@/components/atoms/label';
import MultipleSelectorComponent from '@/components/atoms/multiple-selector';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/atoms/select';
import { BaseSeparator } from '@/components/atoms/separator';
import { DialogClose } from '@/components/ui/dialog';
import { IGetImageRepositoryListResponse } from '@/interfaces/admin/manage-test/image-repository/list';
import { useImageRepositoryModal } from '@/store/admin/manage-test/image-repository/modal';
import { yupResolver } from '@hookform/resolvers/yup';
import React, { useEffect, useState } from 'react';
import { useForm, useFormContext, Path, FormProvider } from 'react-hook-form';
import { useShallow } from 'zustand/react/shallow';
import { Option } from '@/components/atoms/multiselect';
import {
  createImageRepositoryBodySchema,
  ICreateImageRepositoryBody,
} from '@/interfaces/admin/manage-test/image-repository/form';
import FileUploader from '../common/file-uploader';
import { Info } from 'lucide-react';
import {
  useCreateImageRepositoryMutation,
  useUpdateImageRepositoryMutation,
} from '@/services/mutation/admin/manage-test/image-repository';
import { buildCreateImageRepositoryPayload } from '@/store/admin/manage-test/image-repository/mapper';
import { useGetImageRepositoryDetailQuery } from '@/services/query/admin/manage-test/image-repository';
import {
  useGetCategoryListQuery,
  useGetStartingLevelListQuery,
} from '@/services/query/admin/master';
import { useGetFileQuery } from '@/services/query/file/get';
import { bufferToFile } from '@/utils/common/file';

const ImageRepositoryNewModal = () => {
  const {
    openedImageRepository,
    openAddModal,
    setOpenAddModal,
    setOpenedImageRepository,
  } = useImageRepositoryModal(
    useShallow(
      ({
        openedImageRepository,
        openAddModal,
        setOpenAddModal,
        setOpenedImageRepository,
      }) => ({
        openedImageRepository,
        openAddModal,
        setOpenAddModal,
        setOpenedImageRepository,
      })
    )
  );

  const handleOpenChangeModal = (state: boolean) => {
    if (!state) {
      setOpenedImageRepository(null);
    }

    setOpenAddModal(state);
  };

  return (
    <BaseDialog
      open={openAddModal}
      onOpenChange={handleOpenChangeModal}
    >
      <BaseDialogContent className="sm:max-w-[800px] overflow-y-auto max-h-[95%]">
        <BaseDialogHeader>
          <BaseDialogTitle className="flex flex-col gap-4">
            <span>{openedImageRepository?.id ? 'Edit' : 'Add New'} Image</span>
            <BaseSeparator />
          </BaseDialogTitle>
        </BaseDialogHeader>

        <span className="font-medium">Image Information</span>

        <NewImageRepositoryForm
          isOpen={openAddModal}
          data={openedImageRepository}
          onCloseModal={() => handleOpenChangeModal(false)}
        />
      </BaseDialogContent>
    </BaseDialog>
  );
};

interface IFormProps {
  isOpen: boolean;
  data: IGetImageRepositoryListResponse | null;
  onCloseModal: VoidFunction;
}

const NewImageRepositoryForm = ({ data, isOpen, onCloseModal }: IFormProps) => {
  const form = useForm({
    resolver: yupResolver(createImageRepositoryBodySchema),
    defaultValues: {
      category: [],
      level: [],
    },
  });

  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const createIR = useCreateImageRepositoryMutation();
  const updateIR = useUpdateImageRepositoryMutation();

  const detailQuery = useGetImageRepositoryDetailQuery(data?.id, !!data?.id);
  const masterStartingLearningLevels = useGetStartingLevelListQuery({
    order_by: 'name',
    order: 'asc',
  });
  const masterCategory = useGetCategoryListQuery();

  const detailPath = detailQuery.data?.data?.link || '';

  const fileQuery = useGetFileQuery({ path: detailPath });

  useEffect(() => {
    if (!isOpen) return;

    const guessMimeFromName = (name: string) => {
      const n = name?.toLowerCase?.() ?? '';
      if (n.endsWith('.png')) return 'image/png';
      if (n.endsWith('.jpg') || n.endsWith('.jpeg')) return 'image/jpeg';
      if (n.endsWith('.webp')) return 'image/webp';
      if (n.endsWith('.svg')) return 'image/svg+xml';
      return 'image/png';
    };

    if (fileQuery?.data && detailPath) {
      const mime = guessMimeFromName(detailPath);
      const file = bufferToFile(fileQuery.data, detailPath, mime);
      const url = URL.createObjectURL(file);

      setPreviewUrl((prev) => {
        if (prev?.startsWith('blob:')) URL.revokeObjectURL(prev);
        return url;
      });
    }

    return () => {
      setPreviewUrl((prev) => {
        if (prev?.startsWith('blob:')) URL.revokeObjectURL(prev);
        return null;
      });
    };
  }, [fileQuery?.data, detailPath, isOpen]);

  const levelOptions: Option[] =
    masterStartingLearningLevels?.data?.data?.map?.((it: any) => ({
      value: String(it?.id ?? ''),
      label: String(it?.name ?? ''),
    })) ?? [];

  const categoryOptions: Option[] =
    masterCategory?.data?.data?.map?.((it: any) => ({
      value: String(it?.id ?? ''),
      label: String(it?.category_name ?? ''),
    })) ?? [];

  useEffect(() => {
    if (isOpen && detailQuery.data) {
      const d = detailQuery.data.data;

      form.reset({
        id: String(d.id),
        image_name: d.image_name ?? '',
        category:
          d.categories?.map((c) => ({
            value: String(c.id),
            label: String(c.name),
          })) ?? [],
        level:
          d.levels?.map((l) => ({
            value: String(l.id),
            label: String(l.name),
          })) ?? [],
      });
    }

    if (!isOpen) {
      form.reset({ category: [], level: [], image_name: '' });
      setPreviewUrl((prev) => {
        if (prev?.startsWith('blob:')) URL.revokeObjectURL(prev);
        return null;
      });
    }
  }, [isOpen, detailQuery.data]);

  const onSubmit = (body: ICreateImageRepositoryBody) => {
    const payload = buildCreateImageRepositoryPayload(body);

    console.log('payload:', payload);

    if (!data) {
      createIR.mutate(payload, { onSuccess: onCloseModal });
    } else {
      updateIR.mutate(
        { params: { id: data.id }, ...payload },
        { onSuccess: onCloseModal }
      );
    }
  };

  const renderImageSection = () => {
    if (previewUrl) {
      return (
        <img
          src={previewUrl}
          alt="Preview"
          className="w-full max-h-80 object-contain"
        />
      );
    }

    if (isOpen && detailPath && fileQuery.isLoading) {
      return (
        <div className="py-8 text-center text-sm text-gray-500">
          Loading image…
        </div>
      );
    }

    if (fileQuery.error) {
      return (
        <div className="py-8 text-center text-sm text-red-500">
          Failed to load image
        </div>
      );
    }

    return (
      <FileUploader
        title="Upload Image"
        supportedText="Supported file types: PNG, JPG, JPEG, SVG max. size 2 MB."
        acceptedTypes="image/*"
        onFileUpload={(img) =>
          form.setValue('image', img!, { shouldValidate: true })
        }
        banner={
          <div className="border-[#F8A644] border bg-[#FEF4E9] h-[52px] px-4 rounded-[8px] w-full flex items-center gap-2">
            <Info
              size={20}
              fill="#F8A644"
              stroke="white"
            />
            <span className="text-sm text-[#767676]">
              Please adjust your image aspect ratio to 4:3 with minimum
              resolutions in 640 x 480 pixel for better quality.
            </span>
          </div>
        }
      />
    );
  };

  return (
    <FormProvider {...form}>
      <form
        className="flex flex-col gap-4"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <InputString<ICreateImageRepositoryBody>
          label="Image Name"
          id="image_name"
          placeholder="Input image name"
        />

        <MultipleSelectorComponent
          title="Category"
          placeholder="Select Category"
          selectAllTitle="Select All Category"
          options={categoryOptions}
          value={(form.watch('category') as Option[]) ?? []}
          onSelect={(vals) => {
            form.setValue('category', vals, {
              shouldValidate: true,
              shouldDirty: true,
            });
          }}
          onSelectAll={() => {
            const current = (form.getValues('category') as Option[]) ?? [];
            const all = categoryOptions;
            const isAllSelected =
              current.length === all.length &&
              all.every((a) => current.some((c) => c.value === a.value));

            form.setValue('category', isAllSelected ? [] : all, {
              shouldValidate: true,
              shouldDirty: true,
            });
          }}
        />

        <MultipleSelectorComponent
          title="Level"
          placeholder="Select Level"
          options={levelOptions}
          value={(form.watch('level') as Option[]) ?? []}
          onSelect={(vals) => {
            form.setValue('level', vals, {
              shouldValidate: true,
              shouldDirty: true,
            });
          }}
        />

        {renderImageSection()}

        <BaseSeparator className="mt-4 -mb-2" />
        <div className="flex justify-end gap-3 -mb-3">
          <DialogClose asChild>
            <BaseButton
              className="h-11 w-32"
              variant={'outline'}
            >
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            className="h-11 w-32"
            type="submit"
            disabled={createIR.isPending || updateIR.isPending}
          >
            {data ? 'Save Changes' : 'Add Image'}
          </BaseButton>
        </div>
      </form>
    </FormProvider>
  );
};

const InputString = <T extends ICreateImageRepositoryBody>({
  label,
  id,
  placeholder,
  optional = false,
  readonly = false,
}: {
  label: string;
  id: keyof T;
  placeholder?: string;
  optional?: boolean;
  readonly?: boolean;
}) => {
  const form = useFormContext<T>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel
        className="text-sm"
        htmlFor={id as string}
      >
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseInput
        id={id as string}
        placeholder={placeholder}
        {...form.register(id as Path<T>)}
        className="h-11 disabled:bg-gray-100"
        readOnly={readonly}
        disabled={readonly}
      />
    </div>
  );
};

const InputSelect = <T extends ICreateImageRepositoryBody>({
  label,
  id,
  placeholder,
  options,
  value,
  optional = false,
}: {
  label: string;
  id: keyof T;
  placeholder?: string;
  value?: string;
  options: { value: string; label: string }[];
  optional?: boolean;
}) => {
  const form = useFormContext<T>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel
        className="text-sm"
        htmlFor={id as string}
      >
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseSelect
        {...form.register(id as Path<T>)}
        value={value}
        onValueChange={(val) => {
          if (!val) return;
          form.setValue(id as Path<T>, val as any, {
            shouldValidate: true,
          });
        }}
      >
        <BaseSelectTrigger
          className="w-full min-h-11"
          id={id as string}
        >
          <BaseSelectValue placeholder={placeholder} />
        </BaseSelectTrigger>
        <BaseSelectContent>
          {options.map((option) => (
            <BaseSelectItem
              key={option.value}
              value={option.value}
            >
              {option.label}
            </BaseSelectItem>
          ))}
        </BaseSelectContent>
      </BaseSelect>
    </div>
  );
};

export default ImageRepositoryNewModal;
