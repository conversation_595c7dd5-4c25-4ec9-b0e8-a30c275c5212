"use client";

import React from "react";
import { DataTable } from "../../global/table";
import {
  Section,
  IGetSectionListResponse,
} from "@/interfaces/admin/manage-section/list";
import { getSectionColumns } from "./column";
import ManageSectionTableHeader from "./table-header";
import ConfirmationModal from "../../modal/confirmation-modal";
import { useManageSectionQueryStore } from "@/store/admin/manage-section/query";
import { useGetSectionListQuery } from "@/services/query/admin/manage-section";
import { useShallow } from "zustand/react/shallow";
import { Skeleton } from "@/components/ui/skeleton";

interface IModalData {
  isOpen: boolean;
  data: IGetSectionListResponse | null;
}

const ManageSectionTable = () => {
  const { sectionQuery, setSectionQuery } = useManageSectionQueryStore(
    useShallow(({ sectionQuery, setSectionQuery }) => ({
      sectionQuery,
      setSectionQuery,
    }))
  );

  // Fetch sections data
  const {
    data: sectionsData,
    isLoading,
    error,
  } = useGetSectionListQuery(sectionQuery);

  const [deleteModal, setDeleteModal] = React.useState<IModalData>({
    isOpen: false,
    data: null,
  });

  const handlePageChange = (page: number) => {
    setSectionQuery({ page });
  };

  const columns = React.useMemo(
    () =>
      getSectionColumns({
        onEdit: (section) => {
          console.log("Edit section:", section);
          // TODO: Implement edit functionality
        },
        onDelete: (section) => {
          setDeleteModal({ isOpen: true, data: section });
        },
      }),
    []
  );

  const handleDeleteConfirm = () => {
    console.log("Delete section:", deleteModal.data);
    // TODO: Implement delete functionality
    setDeleteModal({ isOpen: false, data: null });
  };

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4 h-full">
        <ManageSectionTableHeader />
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4 h-full">
        <ManageSectionTableHeader />
        <div className="bg-white rounded-lg p-8 text-center">
          <p className="text-red-600">
            Error loading sections: {error.message}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 h-full">
      <ConfirmationModal
        isOpen={deleteModal.isOpen}
        onOpenChange={(open) => setDeleteModal({ isOpen: open, data: null })}
        onConfirm={handleDeleteConfirm}
        title="Delete Section?"
        description={`Are you sure you want to delete "${deleteModal.data?.section_name}"? This action cannot be undone.`}
      />
      <ManageSectionTableHeader />
      <DataTable
        columns={columns}
        data={sectionsData?.data || []}
        pagination={sectionsData?.pagination}
        onPageChange={handlePageChange}
      />
    </div>
  );
};

export default ManageSectionTable;
