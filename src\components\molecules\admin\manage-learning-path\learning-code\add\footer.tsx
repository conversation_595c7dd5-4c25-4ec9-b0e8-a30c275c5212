"use client";

import { BaseButton } from "@/components/atoms/button";
import { ICreateLearningCodeForm } from "@/interfaces/admin/manage-learning-path/new";
import { useManageLearningPathFilterStore } from "@/store/admin/manage-learning-path/filter";
import { useRouter } from "next/navigation";
import { useFormContext } from "react-hook-form";
import { useShallow } from "zustand/react/shallow";

const AddLearningCodeFooter = () => {
  const { currentData } = useManageLearningPathFilterStore(
    useShallow(({ currentData }) => ({
      currentData,
    }))
  );

  const router = useRouter();

  const form = useFormContext<ICreateLearningCodeForm>();

  return (
    <div className="flex justify-end gap-4 w-full bg-white px-3 py-3.5 rounded-lg mt-44">
      <BaseButton
        variant={"outline"}
        onClick={() => router.back()}
        className="h-11 px-8"
      >
        Cancel
      </BaseButton>
      <BaseButton
        className="h-11 px-8"
        disabled={
          Object.keys(form.formState.errors).length > 0 ||
          !form.formState.isValid
        }
        type="submit"
      >
        {currentData ? "Update" : "Create"} Learning Code
      </BaseButton>
    </div>
  );
};

export default AddLearningCodeFooter;
