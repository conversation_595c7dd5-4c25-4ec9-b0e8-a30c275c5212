import { BaseButton } from "@/components/atoms/button";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { ICertificate } from "@/interfaces/admin/manage-certificate/list";
import { BaseSwitch } from "@/components/atoms/switch";
import IconEye from "@/assets/icons/IconEye";
import { capitalizeFirstLetter } from "@/utils/helper/text";

interface Props {
  onView: (certificate: ICertificate) => void;
  onToggleActive: (certificate: ICertificate) => void;
}

export const getCertificateColumns = ({
  onView,
  onToggleActive,
}: Props): ColumnDef<ICertificate>[] => {
  return [
    {
      accessorKey: "user_id",
      header: "User ID",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.user_id || "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "npk",
      header: "NPK",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">{row.original.npk || "-"}</div>
        );
      },
    },
    {
      accessorKey: "name",
      header: "Name",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.name || "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "module_name",
      header: "Module Name",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.module_name || "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "module_type",
      header: "Module Type",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.module_type || "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "level",
      header: "Level",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.level || "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "attempt",
      header: "Attempt",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.attempt || "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "issued_date",
      header: "Issued Date",
      cell({ row }) {
        return (
          <div className="text-sm text-muted-foreground w-[100px] text-wrap">
            {row.original.issued_date
              ? dayjs(row.original.issued_date).format("DD MMM YYYY HH:mm:ss")
              : "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "expired_date",
      header: "Expired Date",
      cell({ row }) {
        return (
          <div className="text-sm text-muted-foreground w-[100px] text-wrap">
            {row.original.expired_date
              ? dayjs(row.original.expired_date).format("DD MMM YYYY HH:mm:ss")
              : "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {capitalizeFirstLetter(row.original.status || "") || "-"}
          </div>
        );
      },
    },

    {
      accessorKey: "active",
      header: "Active?",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            <BaseSwitch
              checked={row.original.status === "active"}
              onCheckedChange={() => onToggleActive(row.original)}
            />
          </div>
        );
      },
    },

    {
      id: "actions",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center justify-end gap-1.5">
            <BaseButton
              variant="ghost"
              size="icon"
              className="h-8 w-8 p-0 hover:bg-gray-100"
              onClick={() => onView(row.original)}
              title="Download"
            >
              <IconEye />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};
