"use client";

import React from "react";
import TechnicalCompetencySelector from "../form/technical-competency-selector";
import SoftCompetencySelector from "../form/soft-competency-selector";

const RelatedCompetencies = () => {
  return (
    <div className="bg-white">
      <h2 className="text-sm font-medium text-comp-content-primary mb-3">
        Related Competencies
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        {/* Technical Competencies */}
        <TechnicalCompetencySelector
          labelClassName="block text-xs font-medium text-comp-content-primary mb-1"
          name="technicalCompetencies"
        />

        {/* Soft Competencies */}
        <SoftCompetencySelector
          labelClassName="block text-xs font-medium text-comp-content-primary mb-1"
          name="softCompetencies"
        />
      </div>
    </div>
  );
};

export default RelatedCompetencies;
