import { IGetListCertificateQuery } from "@/interfaces/admin/manage-certificate/list";
import { create } from "zustand";

interface IManageCertificateQuery {
  certificateQuery: IGetListCertificateQuery;
  setCertificateQuery: (query: Partial<IGetListCertificateQuery>) => void;
  resetQuery: () => void;
}

const initialQuery: IGetListCertificateQuery = {
  page: 1,
  limit: 10,
  search: "",
  search_by: undefined,
  module_type: [],
  status: undefined,
  start_issue_date: undefined,
  end_issue_date: undefined,
  start_expired_date: undefined,
  end_expired_date: undefined,
};

export const useManageCertificateQueryStore = create<IManageCertificateQuery>()(
  (set, get) => ({
    certificateQuery: initialQuery,
    setCertificateQuery: (query) =>
      set({
        certificateQuery: { ...get().certificateQuery, ...query },
      }),
    resetQuery: () => set({ certificateQuery: initialQuery }),
  })
);
