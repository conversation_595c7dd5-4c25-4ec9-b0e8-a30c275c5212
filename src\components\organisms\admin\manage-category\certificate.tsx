"use client";
import CertificateDetail from "@/components/molecules/profile-dashboard/riwayat-belajar/certificate-detail";
import { Skeleton } from "@/components/ui/skeleton";
import { useGetDetailCertificateQuery } from "@/services/query/admin/manage-certificate";
import { useGetLearningMaterialUrlQuery } from "@/services/query/admin/manage-material";
import { useGetFileQuery } from "@/services/query/file/get";
import { bufferToFile } from "@/utils/common/file";
import dayjs from "dayjs";
import { notFound, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

type Props = {
  id: string;
};

const guessMimeFromName = (name: string) => {
  const n = name.toLowerCase();
  if (n.endsWith(".png")) return "image/png";
  if (n.endsWith(".jpg") || n.endsWith(".jpeg")) return "image/jpeg";
  if (n.endsWith(".webp")) return "image/webp";
  if (n.endsWith(".pdf")) return "application/pdf";
  return "image/png";
};

const CertificateView = ({ id }: Props) => {
  const router = useRouter();
  const { data, isPending } = useGetDetailCertificateQuery(id);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const { data: image, isPending: imagePending } = useGetFileQuery({
    path: data?.data.user_avatar ?? "",
  });
  const { data: pdf, isPending: pdfPending } = useGetLearningMaterialUrlQuery({
    url: data?.data.link ?? "",
  });

  const avatarPath = data?.data.user_avatar;

  useEffect(() => {
    if (!avatarPath) {
      setAvatarPreview(null);
      return;
    }
    if (image) {
      const mime = guessMimeFromName(avatarPath);
      const file = bufferToFile(image, avatarPath, mime);
      const url = URL.createObjectURL(file);
      setAvatarPreview((prev) => {
        if (prev?.startsWith("blob:")) URL.revokeObjectURL(prev);
        return url;
      });
    }
    return () => {
      setAvatarPreview((prev) => {
        if (prev?.startsWith("blob:")) URL.revokeObjectURL(prev);
        return null;
      });
    };
  }, [avatarPath, image]);

  if (isPending)
    return (
      <div className="flex gap-4 h-[calc(100vh-100px)] w-full">
        <Skeleton className="h-full w-full flex-1" />
        <Skeleton className="h-full w-full lg:max-w-[265px]" />
      </div>
    );

  if (!data) return notFound();

  return (
    <CertificateDetail
      key={id}
      title={data.data.module_name}
      recipientName={data.data.user_name}
      recipientEmail={data.data.user_email}
      issuedDate={dayjs(data.data.issued_date).format("DD MMMM YYYY")}
      expiredDate={
        data.data.expired_date
          ? dayjs(data.data.expired_date).format("DD MMMM YYYY")
          : null
      }
      pdf={pdf?.data.url ?? null}
      avatar={avatarPreview ?? ""}
      description={data.data.module_description}
      skills={data.data.skill_name.split(",")}
      onBack={() => router.push("/admin/manage-certificate")}
    />
  );
};

export default CertificateView;
