import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";

import { Search } from "lucide-react";
import { useShallow } from "zustand/react/shallow";
import lodash from "lodash";
import { useManageLearningPathTabStore } from "@/store/admin/manage-learning-path/tab";
import {
  IGetLearningCodeListQuery,
  IGetLearningLevelListQuery,
} from "@/interfaces/admin/manage-learning-path/list";
import { useManageLearningPathFilterStore } from "@/store/admin/manage-learning-path/filter";
import { useMemo } from "react";

const ManageLearningPathTableHeaderSearch = () => {
  const activeTab = useManageLearningPathTabStore((state) => state.activeTab);
  const {
    learningCodeQuery,
    learningLevelQuery,
    setLearningCodeQuery,
    setLearningLevelQuery,
  } = useManageLearningPathFilterStore(
    useShallow(
      ({
        learningCodeQuery,
        learningLevelQuery,
        setLearningCodeQuery,
        setLearningLevelQuery,
      }) => ({
        learningCodeQuery,
        learningLevelQuery,
        setLearningCodeQuery,
        setLearningLevelQuery,
      })
    )
  );

  const searchByOption = useMemo(() => {
    if (activeTab === "learning-code") {
      return [
        { value: "code", label: "Code" },
        { value: "name", label: "Name" },
        { value: "modul_name", label: "Module Name" },
        { value: "created_by", label: "Created By" },
        { value: "updated_by", label: "Updated By" },
      ];
    }

    return [
      { value: "level", label: "Level" },
      { value: "name", label: "Name" },
      { value: "modul_name", label: "Module Name" },
      { value: "created_by", label: "Created By" },
      { value: "updated_by", label: "Updated By" },
    ];
  }, [activeTab]);

  const handleQueryChange = (
    query: Partial<IGetLearningCodeListQuery | IGetLearningLevelListQuery>
  ) => {
    if (activeTab === "learning-code") {
      setLearningCodeQuery({
        ...learningCodeQuery,
        ...(query as Partial<IGetLearningCodeListQuery>),
      });
      return;
    }

    setLearningLevelQuery({
      ...learningLevelQuery,
      ...(query as Partial<IGetLearningLevelListQuery>),
    });
  };

  return (
    <div className="text-[#3C3C3C] font-semibold rounded-lg flex items-center gap-1 relative w-[40%] bg-white px-3">
      <div>
        <BaseSelect
          value={
            activeTab === "learning-code"
              ? learningCodeQuery.search_by
              : learningLevelQuery.search_by
          }
          onValueChange={(value) =>
            handleQueryChange({ search_by: value as any })
          }
        >
          <BaseSelectTrigger className="w-44 border-none px-1">
            <BaseSelectValue placeholder="Search By" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {searchByOption.map((it) => (
              <BaseSelectItem
                value={it.value}
                key={`search-by-${activeTab}-${it.value}`}
              >
                {it.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>
      <BaseSeparator orientation="vertical" />
      <BaseInput
        className="border-none h-12 focus-visible:border-none focus-visible:ring-0"
        onChange={lodash.debounce(
          (e) => handleQueryChange({ page: 1, search: e?.target?.value }),
          800
        )}
      />
      <Search size={24} />
      <BaseSeparator orientation="vertical" className="bg-red-500 w-1" />
    </div>
  );
};

export default ManageLearningPathTableHeaderSearch;
