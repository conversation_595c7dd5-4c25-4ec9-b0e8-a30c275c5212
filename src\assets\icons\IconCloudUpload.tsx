import * as React from "react";
import { SVGProps } from "react";
const IconCloudUpload = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={49}
    height={48}
    viewBox="0 0 49 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_10046_51954)">
      <g clipPath="url(#clip1_10046_51954)">
        <path
          d="M23.225 17.925C23.93 17.2275 25.07 17.2275 25.7075 17.925L31.7075 23.9249C32.4725 24.6299 32.4725 25.77 31.7075 26.4074C31.07 27.1725 29.93 27.1725 29.225 26.4074L26.3 23.5424V33.5999C26.3 34.5975 25.4975 35.4 24.5 35.4C23.5025 35.4 22.7 34.5975 22.7 33.5999V23.5424L19.7075 26.4074C19.07 27.1725 17.93 27.1725 17.225 26.4074C16.5275 25.77 16.5275 24.6299 17.225 23.9249L23.225 17.925ZM11.3 40.7999C5.33525 40.7999 0.5 35.9625 0.5 30C0.5 25.2974 3.50525 21.2325 7.7 19.8149V19.8C7.7 12.84 13.34 7.19995 20.3 7.19995C24.1175 7.19995 27.53 8.89495 29.8475 11.5732C30.965 11.076 32.195 10.8 33.5 10.8C38.4725 10.8 42.5 14.8275 42.5 19.8C42.5 20.295 42.4625 20.775 42.3875 21.2475C45.9875 22.8225 48.5 26.4224 48.5 30.5999C48.5 36.0299 44.255 40.4699 38.9 40.7849V40.7999H11.3ZM20.3 10.8C15.3275 10.8 11.3 14.8275 11.3 19.8V22.3575L8.8325 23.2125C6.098 24.2024 4.1 26.8724 4.1 30C4.1 33.9749 7.3235 37.1999 11.3 37.1999H38.495L38.69 37.1849C42.155 36.9899 44.9 34.1099 44.9 30.5999C44.9 27.9 43.28 25.5674 40.94 24.5474L38.3825 23.4299L38.8325 20.6775C38.8775 20.3925 38.9 20.1 38.9 19.8C38.9 16.815 36.485 14.4 33.5 14.4C32.7125 14.4 31.97 14.565 31.31 14.865L28.865 15.9525L27.1175 13.9275C25.46 12.006 23.0225 10.8 20.2325 10.8H20.3Z"
          fill="#C0C0C0"
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_10046_51954">
        <rect width={48} height={48} fill="white" transform="translate(0.5)" />
      </clipPath>
      <clipPath id="clip1_10046_51954">
        <rect
          width={48}
          height={38.4}
          fill="white"
          transform="translate(0.5 4.80005)"
        />
      </clipPath>
    </defs>
  </svg>
);
export default IconCloudUpload;
