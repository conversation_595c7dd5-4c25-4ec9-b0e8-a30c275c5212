import * as yup from "yup";

export const createLearningLevelFormSchema = yup.object({
  level: yup.string().required(),
  status: yup
    .mixed<"active" | "inactive">()
    .oneOf(["active", "inactive"])
    .required(),
  level_name: yup.string().required(),
});

export interface ICreateLearningLevelForm
  extends yup.InferType<typeof createLearningLevelFormSchema> {}

// Create learning code
export interface ICreateLearningCodeBody {
  learning_code: string;
  learning_code_name: string;
  job_name_id: number[];
}

// Create learning level
export interface ICreateLearningLevelBody {
  name: string;
  level: number;
  status: "active" | "inactive";
}

export const createLearningCodeFormSchema = yup.object({
  learning_code: yup.string().required(),
  learning_code_name: yup.string().max(100).required(),
  job_name_id: yup.array(yup.number()).required(),
});

export interface ICreateLearningCodeForm
  extends yup.InferType<typeof createLearningCodeFormSchema> {}
