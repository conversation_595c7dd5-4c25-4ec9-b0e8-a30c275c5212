'use server';

import {
  IGetQuestionBankDetailParams,
  IGetQuestionBankDetailResponse,
} from '@/interfaces/admin/manage-test/question-bank/detail';
import {
  IGetQuestionBankListQuery,
  IGetQuestionBankListResponse,
} from '@/interfaces/admin/manage-test/question-bank/list';
import { ICreateQuestionBankBodyData } from '@/interfaces/admin/manage-test/question-bank/new';
import {
  IUpdateQuestionBankBodyData,
  IUpdateQuestionBankParams,
} from '@/interfaces/admin/manage-test/question-bank/update';
import { IGlobalResponseDto } from '@/interfaces/global/response';
import { api } from '@/services/satellite';
import { handleAxiosError } from '@/utils/common/axios';

const BASE = '/cms/admin/learning/question-bank';

export const apiGetQuestionBankList = async (
  query?: IGetQuestionBankListQuery
) => {
  try {
    const res = await api.get<
      IGlobalResponseDto<IGetQuestionBankListResponse[]>
    >(`${BASE}/list`, { params: query });
    return res.data;
  } catch (err) {
    throw handleAxiosError(err);
  }
};

export const apiGetQuestionBankDetail = async (
  params: IGetQuestionBankDetailParams
) => {
  try {
    const res = await api.get<
      IGlobalResponseDto<IGetQuestionBankDetailResponse>
    >(`${BASE}/detail/${params.id}`);
    return res.data;
  } catch (err) {
    throw handleAxiosError(err);
  }
};

export const apiExportQuestionBankList = async (
  query?: IGetQuestionBankListQuery
) => {
  try {
    const response = await api.get(`${BASE}/export`, {
      params: query,
      responseType: 'arraybuffer',
    });

    let filename = 'question-bank-list.xlsx';

    const disposition = response.headers['content-disposition'];
    if (disposition?.includes('filename=')) {
      const match = disposition.match(/filename="?([^"]+)"?/);
      if (match?.[1]) filename = match[1];
    }

    const base64File = Buffer.from(response.data).toString('base64');
    return { filename, file: base64File };
  } catch (err) {
    throw handleAxiosError(err);
  }
};

export const apiCreateQuestionBank = async (
  body: ICreateQuestionBankBodyData,
  file?: File | null
) => {
  try {
    const formData = new FormData();
    formData.append('data', JSON.stringify(body));
    if (file) formData.append('file', file);

    const res = await api.post<IGlobalResponseDto<any>>(
      `${BASE}/insert`,
      formData,
      { headers: { 'Content-Type': 'multipart/form-data' } }
    );
    return res.data;
  } catch (err) {
    throw handleAxiosError(err);
  }
};

export const apiUpdateQuestionBank = async (
  params: IUpdateQuestionBankParams,
  body: IUpdateQuestionBankBodyData,
  file?: File | null
) => {
  try {
    const formData = new FormData();
    formData.append('data', JSON.stringify(body));
    if (file) formData.append('file', file);

    const res = await api.post<IGlobalResponseDto<any>>(
      `${BASE}/update/${params.id}`,
      formData,
      { headers: { 'Content-Type': 'multipart/form-data' } }
    );
    return res.data;
  } catch (err) {
    throw handleAxiosError(err);
  }
};

export const apiDeleteQuestionBank = async (
  params: IUpdateQuestionBankParams
) => {
  try {
    const res = await api.post<IGlobalResponseDto<any>>(
      `${BASE}/delete/${params.id}`
    );
    return res.data;
  } catch (err) {
    throw handleAxiosError(err);
  }
};
