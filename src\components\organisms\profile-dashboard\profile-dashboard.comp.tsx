"use client";

import React, { useMemo, useState } from "react";
import { BaseButton } from "@/components/atoms/button";
import {
  BaseAvatar,
  BaseAvatarFallback,
  BaseAvatarImage,
} from "@/components/atoms/avatar";
import OnlineLearning from "@/components/molecules/profile-dashboard/riwayat-belajar/online-learning";
import InClassTraining from "@/components/molecules/profile-dashboard/riwayat-belajar/in-class-training";
import TrainingHistory, {
  InClassItem,
} from "@/components/molecules/profile-dashboard/riwayat-belajar/training-history";
import HasilCoconut from "@/components/molecules/profile-dashboard/hasil-coconut/hasil-coconut";
import ToolbarFilter from "@/components/molecules/profile-dashboard/riwayat-belajar/toolbar-filter";
import CertificateDetail from "@/components/molecules/profile-dashboard/riwayat-belajar/certificate-detail";
import HasilSoftCompetencies from "@/components/molecules/profile-dashboard/hasil-soft-competencies/hasil-soft-competencies";
import DashboardTrainer from "@/components/molecules/profile-dashboard/dashboard-trainer/dashboard-trainer";
import { cn } from "@/lib/utils";
import { useSearchParams } from "next/navigation";
import { generateDummyAccPedia } from "@/components/molecules/profile-dashboard/acc-pedia/acc-pedia-table";
import AccPediaWrapper from "@/components/molecules/profile-dashboard/acc-pedia/acc-pedia-wrapper";
import InClassTrainingFacultyMember from "@/components/molecules/profile-dashboard/faculty-member/in-class-training-facmen";
import ModuleFacultyMember from "@/components/molecules/profile-dashboard/faculty-member/module-facmem";
import ReviewTugas from "@/components/molecules/profile-dashboard/beri-tugas/review-tugas";
import BeriTugas from "@/components/molecules/profile-dashboard/beri-tugas/beri-tugas";

type TMenuMenu =
  | "dashboard-trainer"
  | "riwayat-belajar"
  | "hasil-coconut"
  | "hasil-soft-competencies"
  | "beri-tugas-faculty"
  | "beri-tugas-manager"
  | "acc-pedia"
  | "faculty-member";

type TCompRole = "trainer" | "learner" | "practicioner" | "expert" | "manager";

type MenuItem = {
  title: string;
  menu: TMenuMenu;
  role: TCompRole[];
};

const items: MenuItem[] = [
  {
    title: "Dashboard Trainer",
    menu: "dashboard-trainer",
    role: ["trainer"],
  },
  {
    title: "Riwayat Belajar",
    menu: "riwayat-belajar",
    role: ["learner", "trainer", "practicioner", "expert", "manager"],
  },
  {
    title: "Hasil COCONUT",
    menu: "hasil-coconut",
    role: ["learner", "trainer", "practicioner", "expert", "manager"],
  },
  {
    title: "Hasil Soft Competencies",
    menu: "hasil-soft-competencies",
    role: ["learner", "trainer", "practicioner", "expert", "manager"],
  },
  {
    title: "Beri Tugas (Faculty Member)",
    menu: "beri-tugas-faculty",
    role: ["expert"],
  },
  {
    title: "Beri Tugas (Manager)",
    menu: "beri-tugas-manager",
    role: ["manager"],
  },
  { title: "ACC Pedia", menu: "acc-pedia", role: ["practicioner", "expert"] },
  {
    title: "Faculty Member",
    menu: "faculty-member",
    role: ["practicioner", "expert"],
  },
];

type TMenuRiwayatBelajar =
  | "online-learning"
  | "in-class-training"
  | "training-history";

type TMenuItemRiwayat = {
  title: string;
  menu: TMenuRiwayatBelajar;
};

const itemMenuRiwayat: TMenuItemRiwayat[] = [
  { title: "Online Learning", menu: "online-learning" },
  { title: "In-Class Training", menu: "in-class-training" },
  { title: "Training History", menu: "training-history" },
];

type TMenuFacultyMember =
  | "faculty-member-module"
  | "faculty-member-in-class-training";

type TMenuItemFacultyMember = {
  title: string;
  menu: TMenuFacultyMember;
};
const itemMenuFacultyMember: TMenuItemFacultyMember[] = [
  { title: "Module", menu: "faculty-member-module" },
  { title: "In-Class Training", menu: "faculty-member-in-class-training" },
];

type TMenuBeriTugas = "beri-tugas" | "review-tugas";

type TMenuItemBeriTugas = {
  title: string;
  menu: TMenuBeriTugas;
};
const itemMenuBeriTugas: TMenuItemBeriTugas[] = [
  { title: "Beri Tugas", menu: "beri-tugas" },
  { title: "Review Tugas", menu: "review-tugas" },
];

type TUser = {
  npk: string;
  name: string;
  position: string;
  entity: string;
  email: string;
  secEmail: string;
  phone: string;
  password: string;
  role: TCompRole;
};

const dummyLearner: TUser = {
  npk: "123456",
  name: "Katon Nugroho",
  position: "Learning Development Analyst",
  entity: "Head Office",
  email: "<EMAIL>",
  secEmail: "<EMAIL>",
  phone: "+6282188888888",
  password: "oldPass123",
  role: "learner",
};

const dummyTrainer: TUser = {
  npk: "889900",
  name: "Dewi Zunuvi",
  position: "Corporate Trainer",
  entity: "Head Office",
  email: "<EMAIL>",
  secEmail: "<EMAIL>",
  phone: "+6282212345678",
  password: "oldPass123",
  role: "trainer",
};

const dummyPracticioner: TUser = {
  npk: "889900",
  name: "Akhmad Nur Hidayat",
  position: "Practicioner Welcome",
  entity: "Head Office",
  email: "<EMAIL>",
  secEmail: "<EMAIL>",
  phone: "+6282212312515",
  password: "oldPass123",
  role: "practicioner",
};

const dummyExpert: TUser = {
  npk: "777001",
  name: "Ayu Maharani",
  position: "Subject Matter Expert",
  entity: "Head Office",
  email: "<EMAIL>",
  secEmail: "<EMAIL>",
  phone: "+6282299900001",
  password: "oldPass123",
  role: "expert",
};

const dummyManager: TUser = {
  npk: "777002",
  name: "Raka Pratama",
  position: "Learning Manager",
  entity: "Head Office",
  email: "<EMAIL>",
  secEmail: "<EMAIL>",
  phone: "+6282299900002",
  password: "oldPass123",
  role: "manager",
};

type CertificateData = {
  title: string;
  recipientName: string;
  recipientEmail: string;
  issuedDate: string;
  expiredDate: string;
  description: string;
  skills: string[];
};

function toRole(v: string | null): TCompRole | null {
  if (!v) return null;
  const s = v.toLowerCase();
  return s === "trainer" ||
    s === "learner" ||
    s === "practicioner" ||
    s === "expert" ||
    s === "manager"
    ? (s as TCompRole)
    : null;
}

const ProfileDashboardComp: React.FC = () => {
  const searchParams = useSearchParams();
  const selectedRole = toRole(searchParams.get("role"));

  const user = useMemo<TUser>(() => {
    switch (selectedRole) {
      case "trainer":
        return dummyTrainer;
      case "practicioner":
        return dummyPracticioner;
      case "expert":
        return dummyExpert;
      case "manager":
        return dummyManager;
      case "learner":
      default:
        return dummyLearner;
    }
  }, [selectedRole]);

  const [menuRiwayatBelajar, setMenuRiwayatBelajar] =
    useState<TMenuRiwayatBelajar>("online-learning");
  const [menuFacultyMember, setMenuFacultyMember] =
    useState<TMenuFacultyMember>("faculty-member-module");
  const [menuBeriTugas, setMenuBeriTugas] =
    useState<TMenuBeriTugas>("beri-tugas");
  const [selectedCertificate, setSelectedCertificate] =
    useState<CertificateData | null>(null);

  const visibleItems = useMemo(
    () => items.filter((i) => i.role.includes(user.role)),
    [user.role]
  );

  const [menu, setMenu] = useState<TMenuMenu>(() => {
    return visibleItems.length > 0 ? visibleItems[0].menu : "riwayat-belajar";
  });

  const prettyRole = useMemo(() => {
    return user.role.charAt(0).toUpperCase() + user.role.slice(1);
  }, [user.role]);

  const handleSelectHistoryItem = (it: InClassItem) => {
    setSelectedCertificate({
      title: it.title,
      recipientName: user.name,
      recipientEmail: user.email,
      issuedDate: it.dateStart,
      expiredDate: it.dateEnd,
      description:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque sit amet sapien fringilla, mattis ligula consectetur.",
      skills: [
        "Public Speaking",
        "Interpersonal Communication",
        "Stakeholder Management",
        "Presentation",
      ],
    });
  };

  const clearSelectedCertificate = () => setSelectedCertificate(null);

  return (
    <div className="flex flex-col gap-6 md:gap-8">
      <h4 className="text-[#3C3C3C] font-bold text-lg md:text-[26px] leading-[30px]">
        Profile Dashboard
      </h4>

      {selectedCertificate ? (
        <CertificateDetail
          {...selectedCertificate}
          onBack={clearSelectedCertificate}
          // Change to real data
          avatar={""}
          pdf={null}
        />
      ) : (
        <div className="flex flex-col lg:flex-row gap-5">
          <div className="w-full lg:min-w-[200px] lg:max-w-[200px] h-fit rounded-xl border border-[#DEDEDE] bg-white overflow-hidden">
            <div className="flex gap-3 flex-col py-5 items-center border-b border-[#DEDEDE]">
              <BaseAvatar className="w-20 h-20">
                <BaseAvatarImage src="https://github.com/shadcn.png" />
                <BaseAvatarFallback>
                  {user.name
                    .split(" ")
                    .map((s) => s[0])
                    .slice(0, 2)
                    .join("")
                    .toUpperCase()}
                </BaseAvatarFallback>
              </BaseAvatar>

              <p className="text-xs text-[#FFA841] font-semibold">
                {prettyRole}
              </p>
              <div className="flex flex-col items-center">
                <p className="text-sm text-[#3C3C3C] font-medium">
                  {user.name}
                </p>
                <p className="text-xs text-[#767676]">{user.email}</p>
              </div>
            </div>

            <ul className="py-2">
              {visibleItems.map((item) => {
                const isActive = menu === item.menu;

                return (
                  <li key={item.menu}>
                    <button
                      type="button"
                      onClick={() => setMenu(item.menu)}
                      aria-current={isActive ? "page" : undefined}
                      className={cn(
                        "relative flex w-full items-center gap-3 px-4 py-3 text-left transition-colors focus:outline-none cursor-pointer",
                        isActive ? "bg-[#F5F5F5]" : "bg-white"
                      )}
                    >
                      <span
                        aria-hidden="true"
                        className={cn(
                          "absolute left-0 top-0 h-full w-[2px] rounded-r",
                          isActive ? "bg-[#F7941E]" : "bg-transparent"
                        )}
                      />
                      <span className="text-sm text-[#3C3C3C]">
                        {item.title}
                      </span>
                    </button>
                  </li>
                );
              })}
            </ul>
          </div>

          <div className="p-4 md:p-5 border border-[#DEDEDE] rounded-md flex-1 min-w-0">
            {menu === "dashboard-trainer" && <DashboardTrainer />}

            {menu === "riwayat-belajar" && (
              <div className="flex flex-col gap-4">
                <p className="text-xl font-bold text-[#3C3C3C] py-3">
                  Riwayat Belajar
                </p>

                <div className="w-full overflow-x-auto scrollbar-hide">
                  <div className="flex flex-nowrap gap-4 xl:gap-8 min-w-max">
                    {itemMenuRiwayat.map((item) => (
                      <BaseButton
                        key={item.menu}
                        type="button"
                        onClick={() => setMenuRiwayatBelajar(item.menu)}
                        className={cn(
                          "text-xs font-medium py-2 px-4 rounded-[8px] cursor-pointer border",
                          menuRiwayatBelajar === item.menu
                            ? "bg-[#F7941E] text-white border-none"
                            : "bg-white text-[#3C3C3C] border-none shadow-none hover:bg-[#FFF3E6]"
                        )}
                      >
                        {item.title}
                      </BaseButton>
                    ))}
                  </div>
                </div>

                <div className="-mx-4 md:mx-0 border-t border-[#DEDEDE]" />

                <ToolbarFilter />

                {menuRiwayatBelajar === "online-learning" && <OnlineLearning />}
                {menuRiwayatBelajar === "in-class-training" && (
                  <InClassTraining />
                )}
                {menuRiwayatBelajar === "training-history" && (
                  <TrainingHistory onSelect={handleSelectHistoryItem} />
                )}
              </div>
            )}

            {menu === "hasil-coconut" && (
              <div className="flex flex-col gap-3">
                <p className="text-xl font-bold text-[#3C3C3C] py-[10px]">
                  Hasil COCONUT
                </p>
                <HasilCoconut />
              </div>
            )}

            {menu === "hasil-soft-competencies" && (
              <div className="flex flex-col gap-3">
                <p className="text-xl font-bold text-[#3C3C3C] py-[10px]">
                  Hasil Soft Competencies
                </p>
                <HasilSoftCompetencies />
              </div>
            )}

            {(menu === "beri-tugas-faculty" ||
              menu === "beri-tugas-manager") && (
              <div className="flex flex-col gap-4">
                <p className="text-xl font-bold text-[#3C3C3C] py-3">
                  Beri Tugas
                </p>

                <div className="w-full overflow-x-auto scrollbar-hide">
                  <div className="flex flex-nowrap gap-4 xl:gap-8 min-w-max">
                    {itemMenuBeriTugas.map((item) => (
                      <BaseButton
                        key={item.menu}
                        type="button"
                        onClick={() => setMenuBeriTugas(item.menu)}
                        className={cn(
                          "text-xs font-medium py-2 px-4 rounded-[8px] cursor-pointer border",
                          menuBeriTugas === item.menu
                            ? "bg-[#F7941E] text-white border-none"
                            : "bg-white text-[#3C3C3C] border-none shadow-none hover:bg-[#FFF3E6]"
                        )}
                      >
                        {item.title}
                      </BaseButton>
                    ))}
                  </div>
                </div>

                <div className="-mx-4 md:mx-0 border-t border-[#DEDEDE]" />

                {menuBeriTugas === "beri-tugas" && <BeriTugas />}

                {menuBeriTugas === "review-tugas" && <ReviewTugas />}
              </div>
            )}

            {menu === "acc-pedia" && (
              <AccPediaWrapper rows={generateDummyAccPedia(53)} />
            )}

            {menu === "faculty-member" && (
              <div className="flex flex-col gap-4">
                <p className="text-xl font-bold text-[#3C3C3C] py-3">
                  Faculty Member
                </p>

                <div className="w-full overflow-x-auto scrollbar-hide">
                  <div className="flex flex-nowrap gap-4 xl:gap-8 min-w-max">
                    {itemMenuFacultyMember.map((item) => (
                      <BaseButton
                        key={item.menu}
                        type="button"
                        onClick={() => setMenuFacultyMember(item.menu)}
                        className={cn(
                          "text-xs font-medium py-2 px-4 rounded-[8px] cursor-pointer border",
                          menuFacultyMember === item.menu
                            ? "bg-[#F7941E] text-white border-none"
                            : "bg-white text-[#3C3C3C] border-none shadow-none hover:bg-[#FFF3E6]"
                        )}
                      >
                        {item.title}
                      </BaseButton>
                    ))}
                  </div>
                </div>

                <div className="-mx-4 md:mx-0 border-t border-[#DEDEDE]" />

                <ToolbarFilter />

                {menuFacultyMember === "faculty-member-module" && (
                  <ModuleFacultyMember />
                )}

                {menuFacultyMember === "faculty-member-in-class-training" && (
                  <InClassTrainingFacultyMember />
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileDashboardComp;
