"use server";

import {
  IGetMaterialRepositoryListQuery,
  IGetMaterialRepositoryListResponse,
} from "@/interfaces/admin/master/material-repository";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite/";
import { handleAxiosError } from "@/utils/common/axios";

// Dummy data for material repository
const DUMMY_MATERIAL_REPOSITORY_DATA: IGlobalResponseDto<
  IGetMaterialRepositoryListResponse[]
> = {
  status: true,
  message: "success get data material",
  data: [
    {
      id: 101,
      type: "video",
      file_format: "mp4",
      name: "Leadership Training Part 1",
      link: "materials/video/1758518097-Noviantodev.mp4",
    },
    {
      id: 102,
      type: "video",
      file_format: "mp4",
      name: "Communication Skills Workshop",
      link: "materials/video/1758518097-Noviantodev.mp4",
    },
    {
      id: 103,
      type: "audio",
      file_format: "mp3",
      name: "Teamwork Fundamentals",
      link: "materials/audio/1758467843-Cinematic Horror Sound Effect Terror Transition.mp3",
    },
    {
      id: 104,
      type: "document",
      file_format: "pdf",
      name: "Project Management Guide",
      link: "materials/document/1758468015-MENU_SEDARI.pdf",
    },
    {
      id: 105,
      type: "video",
      file_format: "mp4",
      name: "Technical Skills Assessment",
      link: "https://lemon/materials/technical_assessment.mp4",
    },
    {
      id: 106,
      type: "audio",
      file_format: "mp3",
      name: "Leadership Podcast Series",
      link: "https://lemon/materials/leadership_podcast.mp3",
    },
    {
      id: 107,
      type: "document",
      file_format: "pdf",
      name: "Employee Handbook",
      link: "https://lemon/materials/employee_handbook.pdf",
    },
    {
      id: 108,
      type: "video",
      file_format: "mp4",
      name: "Safety Training Module",
      link: "https://lemon/materials/safety_training.mp4",
    },
    {
      id: 109,
      type: "audio",
      file_format: "wav",
      name: "Customer Service Training",
      link: "https://lemon/materials/customer_service.wav",
    },
    {
      id: 110,
      type: "document",
      file_format: "docx",
      name: "Performance Review Template",
      link: "https://lemon/materials/performance_review.docx",
    },
  ],
};

export const apiGetMaterialRepositoryList = async (
  query?: IGetMaterialRepositoryListQuery
) => {
  try {
    // TODO: Uncomment when API is ready
    // const response = await api.get<
    //   IGlobalResponseDto<IGetMaterialRepositoryListResponse[]>
    // >("/cms/admin/master/material-repository", { params: query });
    // return response.data;

    // Return dummy data for now
    let filteredData = [...DUMMY_MATERIAL_REPOSITORY_DATA.data];

    // Apply filters
    if (query?.type) {
      filteredData = filteredData.filter((item) => item.type === query.type);
    }

    if (query?.search) {
      const searchLower = query.search.toLowerCase();
      filteredData = filteredData.filter((item) =>
        item.name.toLowerCase().includes(searchLower)
      );
    }

    // Apply sorting
    if (query?.order_by) {
      filteredData.sort((a, b) => {
        if (query.order_by === "name") {
          return a.name.localeCompare(b.name);
        }
        return a.id - b.id; // default to id
      });
    }

    return {
      ...DUMMY_MATERIAL_REPOSITORY_DATA,
      data: filteredData,
    };
  } catch (error) {
    throw handleAxiosError(error);
  }
};
