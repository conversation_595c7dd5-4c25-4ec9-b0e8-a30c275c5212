import { create } from "zustand";

interface IManageLearningPathModal {
  openAddLearningLevelModal: boolean;
  setOpenAddLearningLevelModal: (open: boolean) => void;
  openEditModal: boolean;
  setOpenEditModal: (open: boolean) => void;
  openDeleteModal: boolean;
  setOpenDeleteModal: (open: boolean) => void;
  openSetActiveModal: boolean;
  setOpenSetActiveModal: (open: boolean) => void;
}

export const useManageLearningPathModalStore =
  create<IManageLearningPathModal>()((set) => ({
    openedManageLearningPath: null,
    openAddLearningLevelModal: false,
    setOpenAddLearningLevelModal: (open: boolean) =>
      set({ openAddLearningLevelModal: open }),
    openEditModal: false,
    setOpenEditModal: (open: boolean) => set({ openEditModal: open }),
    openDeleteModal: false,
    setOpenDeleteModal: (open: boolean) => set({ openDeleteModal: open }),
    openSetActiveModal: false,
    setOpenSetActiveModal: (open: boolean) => set({ openSetActiveModal: open }),
  }));
