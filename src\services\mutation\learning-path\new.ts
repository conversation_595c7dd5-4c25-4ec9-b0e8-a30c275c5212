import {
  ICreateLearningCodeBody,
  ICreateLearningCodeForm,
  ICreateLearningLevelBody,
} from "@/interfaces/admin/manage-learning-path/new";
import {
  apiCreateLearningCode,
  apiCreateLearningLevel,
} from "@/services/api/learning-path/new";
import { useMutation } from "@tanstack/react-query";

export const useInsertLearningCodeMutation = () => {
  return useMutation({
    mutationKey: ["learning-code", "insert"],
    mutationFn: async (body: ICreateLearningCodeForm) => {
      const data: ICreateLearningCodeBody = {
        job_name_id: body.job_name_id as number[],
        learning_code: body.learning_code,
        learning_code_name: body.learning_code_name,
      };

      return await apiCreateLearningCode(data);
    },
  });
};

export const useInsertLearningLevelMutation = () => {
  return useMutation({
    mutationKey: ["learning-level", "insert"],
    mutationFn: async (body: ICreateLearningLevelBody) => {
      return await apiCreateLearningLevel(body);
    },
  });
};
