'use client';

import React, { useEffect } from 'react';
import { BaseButton } from '@/components/atoms/button';
import { BaseSeparator } from '@/components/atoms/separator';
import MultipleSelectorComponent from '@/components/atoms/multiple-selector';
import { Option } from '@/components/atoms/multiselect';
import { InputSelect } from '../common/select';
import { cn } from '@/lib/utils';
import {
  useGetCategoryListQuery,
  useGetStartingLevelListQuery,
} from '@/services/query/admin/master';
import { useQuestionBankFilterStore } from '@/store/admin/manage-test/question-bank/filter';
import { useShallow } from 'zustand/react/shallow';

export const QUESTION_TYPE_OPTIONS: Option[] = [
  { value: '<PERSON><PERSON><PERSON> Ganda', label: '<PERSON><PERSON><PERSON> Ganda' },
  { value: 'Benar Salah', label: 'Benar Salah' },
  { value: 'Isian', label: 'Isian' },
];

export const WITH_IMAGE_OPTIONS: Option[] = [
  { value: 'notnull', label: 'Yes' },
  { value: 'null', label: 'No' },
];

interface Props {
  className?: string;
}

const QuestionBankTableHeaderFilter = ({ className }: Readonly<Props>) => {
  const {
    resetQuery,
    draft,
    setDraft,
    resetDraft,
    applyDraft,
    initDraftFromQuery,
  } = useQuestionBankFilterStore(
    useShallow(
      ({
        resetQuery,
        draft,
        setDraft,
        resetDraft,
        applyDraft,
        initDraftFromQuery,
      }) => ({
        resetQuery,
        draft,
        setDraft,
        resetDraft,
        applyDraft,
        initDraftFromQuery,
      })
    )
  );

  useEffect(() => {
    initDraftFromQuery();
  }, [initDraftFromQuery]);

  const startingLearningLevels = useGetStartingLevelListQuery({
    order_by: 'name',
    order: 'asc',
  });
  const masterCategory = useGetCategoryListQuery();

  const levelOptions: Option[] =
    startingLearningLevels?.data?.data?.map?.((it: any) => ({
      value: String(it?.id ?? ''),
      label: String(it?.name ?? ''),
    })) ?? [];

  const categoryOptions: Option[] =
    masterCategory?.data?.data?.map?.((it: any) => ({
      value: String(it?.id ?? ''),
      label: String(it?.category_name ?? ''),
    })) ?? [];

  const findCategoryLabel = (value: string) =>
    categoryOptions.find((o) => o.value === value)?.label ?? value;

  const selectedCategoryValue: Option[] = draft.category_id
    ? [
        {
          value: String(draft.category_id),
          label: findCategoryLabel(String(draft.category_id)),
        },
      ]
    : [];
  const selectedLevel = draft.level_id ? String(draft.level_id) : '';

  const handleApply = () => {
    applyDraft();
  };

  return (
    <div className={cn('bg-white rounded-[8px]', className)}>
      <div className="flex items-center justify-between p-4">
        <span className="font-semibold text-comp-content-primary">Filter</span>

        <div className="flex items-center gap-2">
          <BaseButton
            className="px-5 w-[95px] text-destructive border border-destructive hover:text-destructive hover:border-destructive h-[40px] text-xs font-medium"
            variant="outline"
            onClick={() => {
              resetDraft();
              resetQuery();
            }}
          >
            Reset
          </BaseButton>

          <BaseButton
            className="px-5 w-[95px] h-[40px] text-xs font-medium"
            onClick={handleApply}
          >
            Apply
          </BaseButton>
        </div>
      </div>

      <BaseSeparator />

      <div className="p-4 space-y-4">
        <MultipleSelectorComponent
          onSelectAll={() => {}}
          options={categoryOptions}
          value={selectedCategoryValue}
          onSelect={(opts) => {
            const first = opts?.[0];
            setDraft({
              page: 1,
              category_id: first ? Number(first.value) : undefined,
            });
          }}
          title="Category"
          placeholder="Select Category"
        />

        <div className="flex items-center gap-3">
          <InputSelect
            label="Level"
            placeholder="Select Level"
            value={selectedLevel}
            options={levelOptions}
            onChange={(v: string) =>
              setDraft({ page: 1, level_id: v ? Number(v) : undefined })
            }
          />

          <InputSelect
            label="Question Type"
            placeholder="Select Question Type"
            value={draft.question_type ?? ''}
            options={QUESTION_TYPE_OPTIONS}
            onChange={(v: string) =>
              setDraft({ page: 1, question_type: v || undefined })
            }
          />

          <InputSelect
            label="With Image?"
            placeholder="Select option"
            value={draft.is_image ?? ''}
            options={WITH_IMAGE_OPTIONS}
            onChange={(v: string) =>
              setDraft({ page: 1, is_image: (v as any) || undefined })
            }
          />
        </div>
      </div>
    </div>
  );
};

export default QuestionBankTableHeaderFilter;
