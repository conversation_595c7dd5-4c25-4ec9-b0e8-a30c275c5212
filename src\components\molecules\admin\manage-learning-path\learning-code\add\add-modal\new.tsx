import { BaseButton } from "@/components/atoms/button";

import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";

import { BaseSeparator } from "@/components/atoms/separator";
import { DialogClose } from "@/components/ui/dialog";
import {
  ICategory,
  ISubCategory,
} from "@/interfaces/admin/manage-category/list";

import { ICreateLearningCodeForm } from "@/interfaces/admin/manage-learning-path/new";

import { useManageLearningPathModalStore } from "@/store/admin/manage-learning-path/modal";

import React from "react";
import { useFormContext } from "react-hook-form";
import { useShallow } from "zustand/react/shallow";
import AddLearningCodeTable from "./table";
import { useManageLearningPathFilterStore } from "@/store/admin/manage-learning-path/filter";
import { useGetLearningCodeDetailQuery } from "@/services/query/learning-path/detail";

const AddLearningCodeModal = () => {
  const { setOpenAddLearningLevelModal, openAddLearningLevelModal } =
    useManageLearningPathModalStore(
      useShallow(
        ({ openAddLearningLevelModal, setOpenAddLearningLevelModal }) => ({
          setOpenAddLearningLevelModal,
          openAddLearningLevelModal,
        })
      )
    );

  const { setSelectedJobPositions, currentData } =
    useManageLearningPathFilterStore(
      useShallow(({ setSelectedJobPositions, currentData }) => ({
        setSelectedJobPositions,
        currentData,
      }))
    );

  const title = "Add Related Job Position";

  const form = useFormContext<ICreateLearningCodeForm>();

  const handleOpenChangeModal = (state: boolean) => {
    handleCancel();

    setOpenAddLearningLevelModal(state);
  };

  const learningCodeDetail = useGetLearningCodeDetailQuery({
    id: currentData,
  });

  const handleCancel = () => {
    if (learningCodeDetail.data) {
      const data = learningCodeDetail.data.data;

      setSelectedJobPositions(data.related_job);
      form.setValue(
        "job_name_id",
        data.related_job.map((item) => item.id),
        { shouldDirty: true, shouldTouch: true, shouldValidate: true }
      );
    } else {
      setSelectedJobPositions([]);
      form.setValue("job_name_id", [], {
        shouldDirty: true,
        shouldTouch: true,
      });
    }
  };

  const handleSubmit = () => {
    setOpenAddLearningLevelModal(false);
  };

  return (
    <BaseDialog
      open={openAddLearningLevelModal}
      onOpenChange={handleOpenChangeModal}
    >
      <BaseDialogContent className="min-w-4/5">
        <BaseDialogHeader>
          <BaseDialogTitle className="flex flex-col gap-4">
            <span>{title}</span>
            <BaseSeparator />
          </BaseDialogTitle>
        </BaseDialogHeader>
        <div className="overflow-x-auto">
          <AddLearningCodeTable />
        </div>

        <BaseSeparator className="mt-4 -mb-2" />
        <div className="flex justify-end gap-3 -mb-3">
          <DialogClose asChild>
            <BaseButton className="h-11 w-32" variant={"outline"}>
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            className="h-11"
            disabled={
              form.getValues("job_name_id")
                ? form.getValues("job_name_id").length === 0
                : true
            }
            onClick={handleSubmit}
          >
            Add Job Position ({form.getValues("job_name_id")?.length ?? 0})
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

interface IFormProps {
  isOpen: boolean;
  data: ICategory | ISubCategory | null;
  onCloseModal: VoidFunction;
}

export default AddLearningCodeModal;
