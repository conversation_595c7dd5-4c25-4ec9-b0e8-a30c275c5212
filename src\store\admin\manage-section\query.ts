import { IGetSectionListQuery } from "@/interfaces/admin/manage-section/list";
import { create } from "zustand";

interface IManageSectionQuery {
  sectionQuery: IGetSectionListQuery;
  setSectionQuery: (query: Partial<IGetSectionListQuery>) => void;
}

export const useManageSectionQueryStore = create<IManageSectionQuery>()(
  (set, get) => ({
    sectionQuery: {
      page: 1,
      limit: 10,
      search: "",
      search_by: "",
    },
    setSectionQuery: (query) =>
      set({
        sectionQuery: {
          ...get().sectionQuery,
          ...query,
        },
      }),
  })
);
