"use server";

import {
  ICreateLearningCodeBody,
  ICreateLearningLevelBody,
} from "@/interfaces/admin/manage-learning-path/new";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiCreateLearningCode = async (body: ICreateLearningCodeBody) => {
  try {
    const response = await api.post<IGlobalResponseDto>(
      "/cms/admin/learning/code/insert",
      body
    );

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};

export const apiCreateLearningLevel = async (
  body: ICreateLearningLevelBody
) => {
  try {
    const response = await api.post<IGlobalResponseDto>(
      "/cms/admin/learning/level/insert",
      body
    );

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
