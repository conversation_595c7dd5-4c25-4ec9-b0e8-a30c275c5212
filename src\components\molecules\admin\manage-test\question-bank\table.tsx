'use client';

import React from 'react';
import { DataTable } from '../../../global/table';
import { getColumnsQuestionBank } from './column';
import { useShallow } from 'zustand/react/shallow';
import { useQuestionBankModal } from '@/store/admin/manage-test/question-bank/modal';
import { useQuestionBankTableListStore } from '@/store/admin/manage-test/question-bank/list';
import { useQuestionBankFilterStore } from '@/store/admin/manage-test/question-bank/filter';
import { useGetQuestionBankListQuery } from '@/services/query/admin/manage-test/question-bank';

interface Props {
  isSelectable?: boolean;
}

const QuestionBankTable = ({ isSelectable }: Readonly<Props>) => {
  const { setOpenAddModal, setOpenDeleteModal, setOpenedQuestionBank } =
    useQuestionBankModal(
      useShallow(
        ({ setOpenAddModal, setOpenedQuestionBank, setOpenDeleteModal }) => ({
          setOpenAddModal,
          setOpenedQuestionBank,
          setOpenDeleteModal,
        })
      )
    );

  const { setSelectedQuestionBanks } = useQuestionBankTableListStore(
    useShallow(({ setSelectedQuestionBanks }) => ({ setSelectedQuestionBanks }))
  );

  const { query, setQuery } = useQuestionBankFilterStore(
    useShallow(({ query, setQuery }) => ({ query, setQuery }))
  );

  const { data } = useGetQuestionBankListQuery(query);
  const rows = data?.data ?? [];

  const pagination = data?.pagination;

  const columns = React.useMemo(
    () =>
      getColumnsQuestionBank({
        isSelectable,
        onSelect: (qs) => setSelectedQuestionBanks(qs),
        onEdit: (row) => {
          setOpenedQuestionBank(row);
          setOpenAddModal(true);
        },
        onDelete: (row) => {
          setOpenedQuestionBank(row);
          setOpenDeleteModal(true);
        },
      }),
    [
      isSelectable,
      setSelectedQuestionBanks,
      setOpenedQuestionBank,
      setOpenAddModal,
      setOpenDeleteModal,
    ]
  );

  return (
    <DataTable
      columns={columns}
      data={rows}
      pagination={pagination}
      onPageChange={(page) => setQuery({ page })}
    />
  );
};

export default QuestionBankTable;
