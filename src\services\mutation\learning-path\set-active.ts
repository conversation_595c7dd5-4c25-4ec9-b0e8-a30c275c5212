import {
  IUpdateLearningCodeBody,
  IUpdateLearningCodeParams,
} from "@/interfaces/admin/manage-learning-path/update";
import { apiUpdateLearningCode } from "@/services/api/learning-path/update";
import { useMutation } from "@tanstack/react-query";

export const useSetActiveLearningCodeMutation = () => {
  return useMutation({
    mutationKey: ["learning-code", "update"],
    mutationFn: async ({
      params,
      status,
    }: {
      params: IUpdateLearningCodeParams;
      status: "active" | "inactive";
    }) => {
      const body: IUpdateLearningCodeBody = {
        status,
      };

      return await apiUpdateLearningCode(params, body);
    },
  });
};
