import {
  IGetLearningCodeListQuery,
  IGetLearningLevelListQuery,
} from "@/interfaces/admin/manage-learning-path/list";
import {
  apiGetLearningCodeList,
  apiGetLearningLevelList,
} from "@/services/api/learning-path/list";
import { useQuery } from "@tanstack/react-query";

export const useGetLearningCodeListQuery = (
  query: IGetLearningCodeListQuery,
  enabled: boolean = false
) => {
  return useQuery({
    queryKey: ["learning-code", "list", query],
    enabled,
    queryFn: async () => {
      return await apiGetLearningCodeList(query);
    },
  });
};

export const useGetLearningLevelListQuery = (
  query: IGetLearningLevelListQuery,
  enabled: boolean = false
) => {
  return useQuery({
    queryKey: ["learning-level", "list", query],
    enabled,
    queryFn: async () => {
      return await apiGetLearningLevelList(query);
    },
  });
};
