"use client";

import React, { useState, useMemo } from "react";
import { Control, FieldErrors } from "react-hook-form";
import { AddSectionFormData } from "@/components/organisms/admin/manage-section/add-new-section";
import { BaseButton } from "@/components/atoms/button";
import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { Search, RefreshCw, Plus, ChevronDown } from "lucide-react";
import { QuestionTable } from "./question-table";
import { DataTable } from "@/components/molecules/global/table";
import { IGlobalPaginationDto } from "@/interfaces/global/pagination";
import { IGetQuestionBankListResponse } from "@/interfaces/admin/manage-test/question-bank/list";
import QuestionTemplateModal from "./question-template-modal";
import QuestionBankModal from "./question-bank-modal";
import Pagination from "../common/pagination";

interface QuizTableProps {
  control: Control<AddSectionFormData>;
  errors: FieldErrors<AddSectionFormData>;
}

// Convert quiz data to match IGetQuestionBankListResponse format
// const convertToQuestionBankFormat = (
//   quizData: any[]
// ): IGetQuestionBankListResponse[] => {
//   return quizData.map((quiz, index) => ({
//     id: parseInt(quiz.id) || index + 1,
//     question: quiz.question || null,
//     type: quiz.questionType || null,
//     option_a: quiz.optionA || null,
//     option_b: quiz.optionB || null,
//     option_c: quiz.optionC || null,
//     option_d: quiz.optionD || null,
//     correct_answer: quiz.keyAnswer || null,
//     correct_answer_percentage: null,
//     feature: null,
//     levels: quiz.level ? [{ id: 1, level: quiz.level }] : [],
//     categories: quiz.category ? [{ id: 1, name: quiz.category }] : [],
//     associated: [],
//     image_id: null,
//     created_at: new Date().toISOString(),
//     created_by: "System",
//     updated_by: "System",
//     last_updated: new Date().toISOString(),
//   }));
// };

const QuizTable = ({ control: _control, errors: _errors }: QuizTableProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [showAddDropdown, setShowAddDropdown] = useState(false);
  const [quizQuestions, setQuizQuestions] = useState<
    IGetQuestionBankListResponse[]
  >([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const [showQuestionBankModal, setShowQuestionBankModal] = useState(false);
  const [showQuestionTemplateModal, setShowQuestionTemplateModal] =
    useState(false);

  // Client-side pagination logic
  const filteredQuestions = useMemo(() => {
    return quizQuestions.filter((question) => {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch =
        !searchTerm ||
        question.question?.toLowerCase().includes(searchLower) ||
        question.option_a?.toLowerCase().includes(searchLower) ||
        question.option_b?.toLowerCase().includes(searchLower) ||
        question.option_c?.toLowerCase().includes(searchLower) ||
        question.option_d?.toLowerCase().includes(searchLower) ||
        question.correct_answer?.toLowerCase().includes(searchLower) ||
        question.id.toString().includes(searchLower);

      const matchesCategory =
        !selectedCategory ||
        (selectedCategory === "category" && question.categories?.length > 0) ||
        (selectedCategory === "level" && question.levels?.length > 0) ||
        (selectedCategory === "type" && question.type);

      return matchesSearch && matchesCategory;
    });
  }, [quizQuestions, searchTerm, selectedCategory]);

  const paginatedQuestions = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredQuestions.slice(startIndex, endIndex);
  }, [filteredQuestions, currentPage, pageSize]);

  const handleAddFromBank = () => {
    setShowQuestionBankModal(true);
    setShowAddDropdown(false);
  };

  const handleAddFromTemplate = () => {
    setShowQuestionTemplateModal(true);
    setShowAddDropdown(false);
  };

  const handleAddQuestionsFromBank = (questions: any[]) => {
    console.log("questions", questions);
    // const convertedQuestions = convertToQuestionBankFormat(questions);
    setQuizQuestions((prev) => [...prev, ...questions]);
    setCurrentPage(1); // Reset to first page when adding new questions
  };

  const handleAddQuestionsFromTemplate = (questions: any[]) => {
    setQuizQuestions((prev) => [...prev, ...questions]);
    setCurrentPage(1); // Reset to first page when adding new questions
  };

  const handleRemoveQuestion = (question: IGetQuestionBankListResponse) => {
    setQuizQuestions((prev) => prev.filter((q) => q.id !== question.id));
    // Adjust current page if necessary
    const newTotalPages = Math.ceil((filteredQuestions.length - 1) / pageSize);
    if (currentPage > newTotalPages && newTotalPages > 0) {
      setCurrentPage(newTotalPages);
    }
  };

  const handleReset = () => {
    setSearchTerm("");
    setSelectedCategory("");
    setCurrentPage(1);
  };

  console.log("paginatedQuestions", paginatedQuestions);
  console.log("filteredQuestions", filteredQuestions);
  console.log("quizQuestions", quizQuestions);
  return (
    <div className="space-y-4">
      {/* Filter Section */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center">
          <div className="flex-1">
            <BaseSelect
              value={selectedCategory}
              onValueChange={setSelectedCategory}
            >
              <BaseSelectTrigger className="w-[200px]">
                <BaseSelectValue placeholder="Search by" />
              </BaseSelectTrigger>
              <BaseSelectContent>
                <BaseSelectItem value="category">Category</BaseSelectItem>
                <BaseSelectItem value="level">Level</BaseSelectItem>
                <BaseSelectItem value="type">Question Type</BaseSelectItem>
              </BaseSelectContent>
            </BaseSelect>
          </div>
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <BaseInput
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="flex items-center gap-3">
          <BaseButton
            type="button"
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            onClick={handleReset}
          >
            <RefreshCw className="w-4 h-4" />
            Reset
          </BaseButton>
          <div className="relative">
            <BaseButton
              type="button"
              className="bg-orange-500 hover:bg-orange-600 flex items-center gap-2"
              onClick={() => setShowAddDropdown(!showAddDropdown)}
            >
              <Plus className="w-4 h-4" />
              Add New Question
              <ChevronDown className="w-4 h-4" />
            </BaseButton>
            {showAddDropdown && (
              <div className="absolute right-0 top-full mt-2 w-56 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                <div className="p-2">
                  <button
                    onClick={handleAddFromBank}
                    className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded"
                  >
                    Add from Question Bank
                  </button>
                  <button
                    onClick={handleAddFromTemplate}
                    className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded"
                  >
                    Add from Question Template
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Table */}
      {paginatedQuestions.length === 0 ? (
        <div className="border rounded-lg overflow-hidden">
          <div className="text-center py-8 text-gray-500">
            No quiz questions added yet. Click "Add New Question" to get
            started.
          </div>
        </div>
      ) : (
        <div className="flex flex-col gap-2">
          <QuestionTable
            questions={paginatedQuestions}
            currentPage={currentPage}
            withCheckbox={false}
            onDeleteQuestion={handleRemoveQuestion}
          />
          <Pagination
            totalItems={quizQuestions.length}
            currentPage={currentPage}
            pageSize={pageSize}
            pageSizeOptions={[10, 20, 50]}
            onPageChange={setCurrentPage}
            onPageSizeChange={setPageSize}
          />
        </div>
      )}

      {/* Modals */}
      <QuestionBankModal
        isOpen={showQuestionBankModal}
        onClose={() => setShowQuestionBankModal(false)}
        onAddQuestions={handleAddQuestionsFromBank}
      />

      <QuestionTemplateModal
        isOpen={showQuestionTemplateModal}
        onClose={() => setShowQuestionTemplateModal(false)}
        onAddQuestions={handleAddQuestionsFromTemplate}
      />
    </div>
  );
};

export default QuizTable;
