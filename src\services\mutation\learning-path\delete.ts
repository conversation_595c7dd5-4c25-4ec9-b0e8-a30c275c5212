import {
  IUpdateLearningCodeBody,
  IUpdateLearningCodeParams,
} from "@/interfaces/admin/manage-learning-path/update";
import { apiUpdateLearningCode } from "@/services/api/learning-path/update";
import { useMutation } from "@tanstack/react-query";

export const useDeleteLearningCodeMutation = () => {
  return useMutation({
    mutationKey: ["learning-code", "update"],
    mutationFn: async ({ params }: { params: IUpdateLearningCodeParams }) => {
      const body: IUpdateLearningCodeBody = {
        is_deleted: true,
      };

      return await apiUpdateLearningCode(params, body);
    },
  });
};
