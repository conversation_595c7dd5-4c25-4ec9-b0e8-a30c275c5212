"use client";

import { BaseButton } from "@/components/atoms/button";
import { cn } from "@/utils/common";
import { Settings2 } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import { useManageJobFilterStore } from "@/store/admin/manage-job/filter";

const ManageJobTableHeaderFilter = () => {
  const { openFilter, setOpenFilter } = useManageJobFilterStore(
    useShallow((state) => ({
      openFilter: state.openFilter,
      setOpenFilter: state.setOpenFilter,
    }))
  );

  return (
    <div className="flex justify-end gap-3">
      <BaseButton
        variant={"outline"}
        className={cn("h-12 px-8", openFilter && "bg-gray-200")}
        onClick={() => setOpenFilter(!openFilter)}
        ref={null}
      >
        <div className="flex items-center gap-2">
          <Settings2 />
          Filter
        </div>
      </BaseButton>
    </div>
  );
};

export default ManageJobTableHeaderFilter;
