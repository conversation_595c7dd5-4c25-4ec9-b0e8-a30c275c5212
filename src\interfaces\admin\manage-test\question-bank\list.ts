export type TFeature = 'OnlineLearning' | 'InClassTraining';
export type TIsImage = 'null' | 'notnull';
export type TSearchBy =
  | 'question_id'
  | 'question'
  | 'option_a'
  | 'option_b'
  | 'option_c'
  | 'option_d'
  | 'correct_answer'
  | 'created_by'
  | 'updated_by';

export interface IGetQuestionBankListQuery {
  category_id?: number;
  level_id?: number;
  question_type?: string;
  feature?: TFeature;
  search?: string | number;
  is_image?: TIsImage;
  search_by?: TSearchBy;
  page?: number;
  limit?: number;
}

export interface IGetQuestionBankListResponse {
  id: number;
  question: string | null;
  type: string | null;
  option_a: string | null;
  option_b: string | null;
  option_c: string | null;
  option_d: string | null;
  correct_answer: string | null;
  correct_answer_percentage: number | null;
  feature: string | null;
  levels: { id: number | null; level: string | null }[];
  categories: { id: number | null; name: string | null }[];
  associated: { section_id: number | null; section_name: string | null }[];
  image_id: number | null;
  created_at: string | null;
  created_by: string | null;
  updated_by: string | null;
  last_updated: string | null;
}
