'use client';

import {
  BaseTabs,
  BaseTabsList,
  BaseTabsTrigger,
} from '@/components/atoms/tabs';
import { usePathname, useRouter } from 'next/navigation';

const ManageTestTitle = () => {
  const router = useRouter();
  const pathname = usePathname();

  // /admin/{menu}/manage-test/{menu}
  const mainUrlManageTest = pathname.split('/').slice(0, 4).join('/');

  return (
    <div className="flex justify-between gap-4">
      <div className="bg-white text-[#3C3C3C] w-full p-3 font-semibold rounded-lg">
        Manage Test
      </div>
      <div className="text-[#3C3C3C] w-full flex items-center gap-2">
        <BaseTabs
          defaultValue={pathname}
          onValueChange={(val) => router.push(val)}
        >
          <BaseTabsList className="w-full h-12">
            <BaseTabsTrigger
              value={`${mainUrlManageTest}/question-bank`}
              className="cursor-pointer"
            >
              Question Bank
            </BaseTabsTrigger>
            <BaseTabsTrigger
              value={`${mainUrlManageTest}/question-template`}
              className="cursor-pointer"
            >
              Question Template
            </BaseTabsTrigger>
            <BaseTabsTrigger
              value={`${mainUrlManageTest}/image-repository`}
              className="cursor-pointer"
            >
              Image Repository
            </BaseTabsTrigger>
          </BaseTabsList>
        </BaseTabs>
      </div>
    </div>
  );
};

export default ManageTestTitle;
