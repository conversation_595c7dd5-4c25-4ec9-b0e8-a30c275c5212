"use server";

import {
  IUpdateLearningCodeBody,
  IUpdateLearningCodeParams,
  IUpdateLearningLevelBody,
  IUpdateLearningLevelParams,
} from "@/interfaces/admin/manage-learning-path/update";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiUpdateLearningCode = async (
  params: IUpdateLearningCodeParams,
  body: IUpdateLearningCodeBody
) => {
  try {
    const response = await api.post<IGlobalResponseDto>(
      `/cms/admin/learning/code/update/${params.id}`,
      body
    );

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};

export const apiUpdateLearningLevel = async (
  params: IUpdateLearningLevelParams,
  body: IUpdateLearningLevelBody
) => {
  try {
    const response = await api.post<IGlobalResponseDto>(
      `/cms/admin/learning/level/update/${params.id}`,
      body
    );

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
