import { useQuery } from '@tanstack/react-query';
import {
  apiGetImageRepositoryList,
  apiGetImageRepositoryDetail,
} from '@/services/api/admin/manage-test/image-repository';
import { IGetImageRepositoryListQuery } from '@/interfaces/admin/manage-test/image-repository/list';

export const useGetImageRepositoryListQuery = (
  params?: IGetImageRepositoryListQuery,
  enabled = true
) => {
  return useQuery({
    queryKey: ['image-repo', 'list', params],
    queryFn: () => apiGetImageRepositoryList(params),
    enabled,
  });
};

export const useGetImageRepositoryDetailQuery = (
  id?: number,
  enabled = !!id
) => {
  return useQuery({
    queryKey: ['image-repo', 'detail', id],
    queryFn: () => apiGetImageRepositoryDetail({ id: id as number }),
    enabled: enabled && !!id,
  });
};
