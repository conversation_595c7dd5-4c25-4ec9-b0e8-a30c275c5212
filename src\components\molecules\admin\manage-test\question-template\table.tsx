'use client';

import React from 'react';
import { DataTable } from '../../../global/table';
import { getColumnsQuestionTemplate } from './column';
import { useShallow } from 'zustand/react/shallow';
import { useQuestionTemplateModal } from '@/store/admin/manage-test/question-template/modal';
import { useQuestionTemplateFilterStore } from '@/store/admin/manage-test/question-template/filter';
import { useGetQuestionTemplateListQuery } from '@/services/query/admin/manage-test/question-template';

const QuestionTemplateTable = () => {
  const { setOpenAddModal, setOpenDeleteModal, setOpenedQuestionTemplate } =
    useQuestionTemplateModal(
      useShallow(
        ({
          setOpenAddModal,
          setOpenedQuestionTemplate,
          setOpenDeleteModal,
        }) => ({
          setOpenAddModal,
          setOpenedQuestionTemplate,
          setOpenDeleteModal,
        })
      )
    );

  const { query, setQuery } = useQuestionTemplateFilterStore(
    useShallow(({ query, setQuery }) => ({ query, setQuery }))
  );

  const { data } = useGetQuestionTemplateListQuery(query);
  const rows = data?.data ?? [];

  const pagination = data?.pagination;

  const columns = React.useMemo(
    () =>
      getColumnsQuestionTemplate({
        onEdit: (row) => {
          setOpenedQuestionTemplate(row);
          setOpenAddModal(true);
        },
        onDelete: (row) => {
          setOpenedQuestionTemplate(row);
          setOpenDeleteModal(true);
        },
      }),
    [setOpenedQuestionTemplate, setOpenAddModal, setOpenDeleteModal]
  );

  return (
    <DataTable
      columns={columns}
      data={rows}
      pagination={pagination}
      onPageChange={(page) => setQuery({ page })}
    />
  );
};

export default QuestionTemplateTable;
