'use client';

import React from 'react';
import { BaseButton } from '@/components/atoms/button';
import {
  Plus,
  RefreshCw,
  CloudDownload,
  CloudUpload,
  Settings2,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useQuestionBankModal } from '@/store/admin/manage-test/question-bank/modal';
import { useShallow } from 'zustand/react/shallow';
import { useQuestionBankFilterStore } from '@/store/admin/manage-test/question-bank/filter';
import { useExportQuestionBankMutation } from '@/services/mutation/admin/manage-test/question-bank';
import { useQueryClient } from '@tanstack/react-query';

interface Props {
  onOpenFilter: React.Dispatch<React.SetStateAction<boolean>>;
}

const QuestionBankTableHeaderAction = ({ onOpenFilter }: Readonly<Props>) => {
  const { setOpenAddModal } = useQuestionBankModal(
    useShallow(({ setOpenAddModal }) => ({
      setOpenAddModal,
    }))
  );

  const { query } = useQuestionBankFilterStore(
    useShallow(({ query }) => ({ query }))
  );

  const downloadFile = useExportQuestionBankMutation();

  const qc = useQueryClient();

  const handleRefresh = () => {
    qc.invalidateQueries({ queryKey: ['question-bank', 'list'] });
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-end gap-3">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <BaseButton
              className="h-12 w-12"
              variant="outline"
            >
              <CloudDownload className="h-6 w-6" />
            </BaseButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-48">
            <DropdownMenuItem
              textValue="download_csv"
              disabled={downloadFile.isPending}
              onSelect={() => downloadFile.mutate(query)}
            >
              Download as CSV
            </DropdownMenuItem>
            <DropdownMenuItem
              textValue="download_csv_tempalte"
              onSelect={() => {}}
            >
              Download CSV Template
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <BaseButton
              className="h-12 w-12"
              variant="outline"
            >
              <CloudUpload className="h-6 w-6" />
            </BaseButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-48">
            <DropdownMenuItem
              textValue="upload_csv"
              onSelect={() => {}}
            >
              Upload via CSV
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <BaseButton
          className="h-12 w-12"
          onClick={handleRefresh}
          variant="outline"
        >
          <RefreshCw className="h-6 w-6" />
        </BaseButton>

        <BaseButton
          className="h-12 px-5"
          variant="outline"
          onClick={() => onOpenFilter((prev) => !prev)}
        >
          <div className="flex items-center gap-1 rounded-[8px] py-[13px] px-4">
            <Settings2 className="h-4 w-4" />
            <span className="text-sm font-medium">Filter</span>
          </div>
        </BaseButton>

        <BaseButton
          className="h-12 px-5"
          onClick={() => setOpenAddModal(true)}
        >
          <div className="flex items-center gap-2">
            <Plus />
            Add New Question
          </div>
        </BaseButton>
      </div>
    </div>
  );
};

export default QuestionBankTableHeaderAction;
