"use client";

import { BaseButton } from "@/components/atoms/button";
import { DownloadCloud, Plus } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import { useManageCategoryTabStore } from "@/store/admin/manage-category/tab";
import ManageCategoryNewModal from "./new-modal";
import { useManageCategoryModal } from "@/store/admin/manage-category/modal";
import {
  useDownloadListCategoryMutation,
  useDownloadListSubCategoryMutation,
} from "@/services/mutation/admin/manage-category";
import {
  BaseDropdownMenu,
  BaseDropdownMenuContent,
  BaseDropdownMenuItem,
  BaseDropdownMenuTrigger,
} from "@/components/atoms/dropdown";
import { useManageCategoryQueryStore } from "@/store/admin/manage-category/query";

const ManageCategoryTableHeaderFilter = () => {
  const { activeTab } = useManageCategoryTabStore(
    useShallow((state) => ({ activeTab: state.activeTab }))
  );

  const { setOpenAddModal } = useManageCategoryModal(
    useShallow(({ setOpenAddModal }) => ({
      setOpenAddModal,
    }))
  );

  const { categoryQuery, subCategoryQuery } = useManageCategoryQueryStore(
    useShallow(({ categoryQuery, subCategoryQuery }) => ({
      categoryQuery,
      subCategoryQuery,
    }))
  );

  const downloadCategory = useDownloadListCategoryMutation();
  const downloadSubCategory = useDownloadListSubCategoryMutation();

  const handleDownloadFile = () => {
    if (activeTab === "category") {
      downloadCategory.mutate(categoryQuery);
      return;
    }
    downloadSubCategory.mutate(subCategoryQuery);
  };

  return (
    <div className="flex items-center justify-end gap-3">
      <BaseDropdownMenu>
        <BaseDropdownMenuTrigger asChild>
          <BaseButton variant={"outline"} size={"icon"} className="size-12">
            <DownloadCloud />
          </BaseButton>
        </BaseDropdownMenuTrigger>
        <BaseDropdownMenuContent>
          <BaseDropdownMenuItem
            className="px-4"
            disabled={
              downloadCategory.isPending || downloadSubCategory.isPending
            }
            onClick={() => handleDownloadFile()}
          >
            Download Excel Data
          </BaseDropdownMenuItem>
        </BaseDropdownMenuContent>
      </BaseDropdownMenu>

      <BaseButton className="h-12 px-5" onClick={() => setOpenAddModal(true)}>
        <div className="flex items-center gap-2">
          <Plus />
          {activeTab === "category" ? "Add Category" : "Add Sub Category"}
        </div>
      </BaseButton>
      <ManageCategoryNewModal />
    </div>
  );
};

export default ManageCategoryTableHeaderFilter;
