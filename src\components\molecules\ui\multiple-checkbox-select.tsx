"use client";
import { useState } from "react";
import { BaseButton } from "@/components/atoms/button";
import { BaseLabel } from "@/components/atoms/label";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { OrangeCheckbox } from "@/components/atoms/checkbox/orange-checkbox";
import { cn } from "@/lib/utils";

interface Option {
  value: string;
  label: string;
}

interface MultipleCheckboxSelectProps {
  options: Option[];
  selectedValues: string[];
  onSelectionChange: (selectedValues: string[]) => void;
  placeholder?: string;
  label?: string;
  className?: string;
  resetSelection?: () => void;
}

export const MultipleCheckboxSelect = ({
  options,
  selectedValues = [],
  onSelectionChange,
  placeholder = "Select options",
  label,
  className,
  resetSelection,
}: MultipleCheckboxSelectProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleCheckboxChange = (value: string, checked: boolean) => {
    const newSelection = checked
      ? [...selectedValues, value]
      : selectedValues.filter((item) => item !== value);
    onSelectionChange(newSelection);
  };

  const handleReset = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (resetSelection) {
      resetSelection();
    } else {
      onSelectionChange([]);
    }
  };

  return (
    <div className={cn("flex flex-col gap-2", className)}>
      {label && <BaseLabel className="text-sm font-medium">{label}</BaseLabel>}
      <BaseSelect open={isOpen} onOpenChange={setIsOpen}>
        <BaseSelectTrigger className="w-full">
          <BaseSelectValue
            placeholder={
              selectedValues.length > 0
                ? `${selectedValues.length} selected`
                : placeholder
            }
          />
        </BaseSelectTrigger>
        <BaseSelectContent>
          <div className="p-2 space-y-2">
            {options.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <OrangeCheckbox
                  id={option.value}
                  checked={selectedValues.includes(option.value)}
                  onCheckedChange={(checked) =>
                    handleCheckboxChange(option.value, checked as boolean)
                  }
                />
                <BaseLabel
                  htmlFor={option.value}
                  className="text-sm font-normal cursor-pointer"
                >
                  {option.label}
                </BaseLabel>
              </div>
            ))}
            <div className="flex gap-2 justify-between pt-2 border-t">
              <BaseButton
                type="button"
                variant="outline"
                size="sm"
                onClick={handleReset}
                className="text-[#F7941E] border-[#F7941E] hover:text-[#FFA733]"
              >
                Reset
              </BaseButton>
              <BaseButton
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="hover:bg-[#FFA733] border-[#F7941E] text-[#F7941E] w-24"
              >
                Ok
              </BaseButton>
            </div>
          </div>
        </BaseSelectContent>
      </BaseSelect>
    </div>
  );
};
