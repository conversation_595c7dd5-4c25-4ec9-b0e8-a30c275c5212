"use client";

import { BaseLabel } from "@/components/atoms/label";
import MultipleSelector from "@/components/atoms/multiselect";
import { useGetCompetencyListQuery } from "@/services/query/admin/master";
import { Controller, useFormContext } from "react-hook-form";
import { cn } from "@/lib/utils";
import { useMemo } from "react";

interface SoftCompetencySelectorProps {
  labelClassName?: string;
  name?: string;
}

const SoftCompetencySelector = ({
  labelClassName,
  name = "softCompetencies",
}: SoftCompetencySelectorProps) => {
  const form = useFormContext();

  const { data: competencies, isPending: pendingCompetencies } =
    useGetCompetencyListQuery({
      type: "behavioral",
      order_by: "name",
    });

  const competencyOptions = useMemo(
    () =>
      competencies?.data?.map((competency) => ({
        label: competency.name,
        value: competency.id.toString(),
      })) ?? [],
    [competencies]
  );

  return (
    <div className="space-y-2">
      <BaseLabel className={cn("text-sm font-medium", labelClassName)}>
        Soft Competencies (Optional)
      </BaseLabel>

      <div className="flex flex-col">
        <Controller
          name={name}
          render={({ field }) => {
            return (
              <MultipleSelector
                isPending={pendingCompetencies}
                value={field.value || []}
                options={competencyOptions}
                onChange={field.onChange}
                placeholder="Select soft competencies"
                badgeClassName="bg-blue-100 text-blue-800"
                loadingIndicator={
                  <p className="py-2 text-center text-sm leading-10 text-muted-foreground">
                    Loading soft competencies...
                  </p>
                }
                emptyIndicator={
                  <p className="w-full text-center text-lg leading-10 text-muted-foreground">
                    No soft competencies found.
                  </p>
                }
              />
            );
          }}
        />
        {form.formState.errors[name] && (
          <p className="text-red-500 text-sm mt-1">
            {form.formState.errors[name]?.message as string}
          </p>
        )}
      </div>
    </div>
  );
};

export default SoftCompetencySelector;
