"use server";

import {
  IDetailCertificate,
  IGetListCertificateQuery,
  IGetListCertificateResponse,
} from "@/interfaces/admin/manage-certificate/list";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetListCertificates = async (
  params?: IGetListCertificateQuery
) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetListCertificateResponse[]>
    >("/cms/admin/learning/certificate/list", { params });

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiUpdateCertificateStatus = async (
  id: number,
  body: { status: string }
) => {
  try {
    const response = await api.post<IGlobalResponseDto<null>>(
      `/cms/admin/learning/certificate/${id}/status`,
      body
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiGetDetailCertificate = async (id: string) => {
  try {
    const response = await api.get<IGlobalResponseDto<IDetailCertificate>>(
      `/cms/admin/learning/certificate/${id}`
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};
