'use client';

import React from 'react';
import { DataTable } from '../../../global/table';
import { getColumnsImageRepository } from './column';
import { useShallow } from 'zustand/react/shallow';
import { useImageRepositoryModal } from '@/store/admin/manage-test/image-repository/modal';
import { useGetImageRepositoryListQuery } from '@/services/query/admin/manage-test/image-repository';
import { useImageRepositoryFilterStore } from '@/store/admin/manage-test/image-repository/filter';

const ImageRepositoryTable = () => {
  const { setOpenAddModal, setOpenDeleteModal, setOpenedImageRepository } =
    useImageRepositoryModal(
      useShallow(
        ({
          setOpenAddModal,
          setOpenedImageRepository,
          setOpenDeleteModal,
        }) => ({
          setOpenAddModal,
          setOpenedImageRepository,
          setOpenDeleteModal,
        })
      )
    );

  const { query, setQuery } = useImageRepositoryFilterStore(
    useShallow(({ query, setQuery }) => ({ query, setQuery }))
  );

  const { data } = useGetImageRepositoryListQuery(query);
  const rows = data?.data ?? [];

  console.log('rows:', rows);

  const pagination = data?.pagination;

  

  const columns = React.useMemo(
    () =>
      getColumnsImageRepository({
        onDownload: (row) => {
          const url = row.link;
          if (!url) return;
          const a = document.createElement('a');
          a.href = url;
          a.download = row.image_name ?? `image-${row.id}`;
          document.body.appendChild(a);
          a.click();
          a.remove();
        },
        onEdit: (row) => {
          setOpenedImageRepository(row);
          setOpenAddModal(true);
        },
        onDelete: (row) => {
          setOpenedImageRepository(row);
          setOpenDeleteModal(true);
        },
      }),
    [setOpenedImageRepository, setOpenAddModal, setOpenDeleteModal]
  );

  return (
    <DataTable
      columns={columns}
      data={rows}
      pagination={pagination}
      onPageChange={(page) => setQuery({ page })}
    />
  );
};

export default ImageRepositoryTable;
