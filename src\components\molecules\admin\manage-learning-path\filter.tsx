"use client";

import { BaseButton } from "@/components/atoms/button";
import { Plus } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import ManageLearningLevelNewModal from "./new-learning-level-modal";
import { useManageLearningPathTabStore } from "@/store/admin/manage-learning-path/tab";
import { useManageLearningPathModalStore } from "@/store/admin/manage-learning-path/modal";
import { useRouter } from "next/navigation";

const ManageCategoryTableHeaderFilter = () => {
  const { activeTab } = useManageLearningPathTabStore(
    useShallow((state) => ({ activeTab: state.activeTab }))
  );

  const { setOpenAddLearningLevelModal } = useManageLearningPathModalStore(
    useShallow(({ setOpenAddLearningLevelModal }) => ({
      setOpenAddLearningLevelModal,
    }))
  );

  const router = useRouter();

  return (
    <div className="flex justify-end gap-3">
      <BaseButton
        className="h-12 px-5"
        onClick={() => {
          if (activeTab === "learning-level") {
            setOpenAddLearningLevelModal(true);
          } else {
            router.push("/admin/manage-learning-path/learning-code/add");
          }
        }}
      >
        <div className="flex items-center gap-2">
          <Plus />
          {activeTab === "learning-code"
            ? "Add Learning Code"
            : "Add Learning Level"}
        </div>
      </BaseButton>
      <ManageLearningLevelNewModal />
    </div>
  );
};

export default ManageCategoryTableHeaderFilter;
