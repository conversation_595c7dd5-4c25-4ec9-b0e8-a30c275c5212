"use client";

import { BaseLabel } from "@/components/atoms/label";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { cn } from "@/utils/common";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { BaseButton } from "@/components/atoms/button";
import { DateRange } from "react-day-picker";
import { MODULE_TYPE_OPTIONS, STATUS_OPTIONS } from "@/utils/data/certificate";
import { useManageCertificateFilterStore } from "@/store/admin/manage-certificate/filter";
import { useManageCertificateQueryStore } from "@/store/admin/manage-certificate/query";
import { useShallow } from "zustand/react/shallow";
import { MultipleCheckboxSelect } from "@/components/molecules/ui/multiple-checkbox-select";
import { ICertificateFilter } from "@/interfaces/admin/manage-certificate/list";

const ManageCertificateFilterInput = () => {
  const { filter, setFilter, resetFilter } = useManageCertificateFilterStore(
    useShallow((state) => ({
      filter: state.filter,
      setFilter: state.setFilter,
      resetFilter: state.resetFilter,
    }))
  );

  const { setCertificateQuery } = useManageCertificateQueryStore(
    useShallow((state) => ({
      setCertificateQuery: state.setCertificateQuery,
    }))
  );

  const handleFilterChange = (key: keyof ICertificateFilter, value: any) => {
    setFilter({ [key]: value });
  };

  const handleDateRangeChange = (
    key: "issuedDateRange" | "expiredDateRange",
    value: DateRange | undefined
  ) => {
    if (key === "issuedDateRange") {
      setFilter({
        start_issue_date: value?.from as any,
        end_issue_date: value?.to as any,
      });
    } else {
      setFilter({
        start_expired_date: value?.from as any,
        end_expired_date: value?.to as any,
      });
    }
  };

  const handleModuleTypeChange = (selectedTypes: string[]) => {
    setFilter({ module_type: selectedTypes });
  };

  const handleApply = () => {
    // Convert filter to query parameters
    const queryParams: any = {
      page: 1, // Reset to first page when applying filters
    };

    if (filter.module_type && filter.module_type.length > 0) {
      queryParams.module_type = filter.module_type;
    }

    if (filter.status) {
      queryParams.status = filter.status;
    }

    if (filter.start_issue_date) {
      queryParams.start_issue_date = format(
        filter.start_issue_date,
        "yyyy-MM-dd"
      );
    }

    if (filter.end_issue_date) {
      queryParams.end_issue_date = format(filter.end_issue_date, "yyyy-MM-dd");
    }

    if (filter.start_expired_date) {
      queryParams.start_expired_date = format(
        filter.start_expired_date,
        "yyyy-MM-dd"
      );
    }

    if (filter.end_expired_date) {
      queryParams.end_expired_date = format(
        filter.end_expired_date,
        "yyyy-MM-dd"
      );
    }

    setCertificateQuery(queryParams);
  };

  const handleReset = () => {
    resetFilter();
    setCertificateQuery({
      page: 1,
      module_type: [],
      status: undefined,
      start_issue_date: undefined,
      end_issue_date: undefined,
      start_expired_date: undefined,
      end_expired_date: undefined,
    });
  };

  return (
    <div className="flex flex-col gap-4 w-full bg-white rounded-lg p-3">
      <div className="flex items-center justify-between">
        <span className="font-semibold">Filter</span>
        <div className="flex gap-3">
          <BaseButton
            variant="outline"
            className="text-red-600 border-red-600 hover:text-red-600 h-9"
            onClick={handleReset}
          >
            Reset
          </BaseButton>
          <BaseButton className="h-9" onClick={handleApply}>
            Apply
          </BaseButton>
        </div>
      </div>
      <BaseSeparator />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Module Type Filter */}
        <MultipleCheckboxSelect
          options={MODULE_TYPE_OPTIONS}
          selectedValues={filter.module_type || []}
          onSelectionChange={(selected) => handleModuleTypeChange(selected)}
          label="Module Type (Multiple)"
        />
        {/* Status Filter */}
        <div className="flex flex-col gap-2">
          <BaseLabel className="text-sm font-medium">Status</BaseLabel>
          <BaseSelect
            value={filter.status || ""}
            onValueChange={(value) => handleFilterChange("status", value)}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select status" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              {STATUS_OPTIONS.map((option) => (
                <BaseSelectItem key={option.value} value={option.value}>
                  {option.label}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>

        {/* Date Range Filter */}
        <div className="flex flex-col gap-2">
          <BaseLabel className="text-sm font-medium">
            Date Range (Issued Date)
          </BaseLabel>
          <div className="flex gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <BaseButton
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !filter.start_issue_date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {filter.start_issue_date || filter.end_issue_date ? (
                    <div className="flex items-center gap-2">
                      {filter.start_issue_date &&
                        format(filter.start_issue_date, "dd MMM yyyy")}
                      <span>-</span>
                      {filter.end_issue_date &&
                        format(filter.end_issue_date, "dd MMM yyyy")}
                    </div>
                  ) : (
                    <span>Issued Date</span>
                  )}
                </BaseButton>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  captionLayout="dropdown"
                  mode="range"
                  selected={{
                    from: filter.start_issue_date
                      ? new Date(filter.start_issue_date)
                      : undefined,
                    to: filter.end_issue_date
                      ? new Date(filter.end_issue_date)
                      : undefined,
                  }}
                  onSelect={(date) =>
                    handleDateRangeChange("issuedDateRange", date)
                  }
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <BaseLabel className="text-sm font-medium">
            Date Range (Expired Date)
          </BaseLabel>
          <div className="flex gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <BaseButton
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !filter.start_expired_date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {filter.start_expired_date || filter.end_expired_date ? (
                    <div className="flex items-center gap-2">
                      {filter.start_expired_date &&
                        format(filter.start_expired_date, "dd MMM yyyy")}
                      <span>-</span>
                      {filter.end_expired_date &&
                        format(filter.end_expired_date, "dd MMM yyyy")}
                    </div>
                  ) : (
                    <span>Expired Date</span>
                  )}
                </BaseButton>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="range"
                  selected={{
                    from: filter.start_expired_date
                      ? new Date(filter.start_expired_date)
                      : undefined,
                    to: filter.end_expired_date
                      ? new Date(filter.end_expired_date)
                      : undefined,
                  }}
                  onSelect={(date) =>
                    handleDateRangeChange("expiredDateRange", date)
                  }
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManageCertificateFilterInput;
