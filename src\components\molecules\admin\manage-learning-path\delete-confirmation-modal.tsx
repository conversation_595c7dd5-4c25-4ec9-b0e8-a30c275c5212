import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { DialogClose } from "@/components/ui/dialog";
import { useDeleteLearningCodeMutation } from "@/services/mutation/learning-path/delete";

import { useUpdateLearningLevelMutation } from "@/services/mutation/learning-path/update";
import {
  useGetLearningCodeListQuery,
  useGetLearningLevelListQuery,
} from "@/services/query/learning-path/list";
import { useManageLearningPathFilterStore } from "@/store/admin/manage-learning-path/filter";
import { useManageLearningPathModalStore } from "@/store/admin/manage-learning-path/modal";
import { useManageLearningPathTabStore } from "@/store/admin/manage-learning-path/tab";
import { Trash2 } from "lucide-react";
import React from "react";
import toast from "react-hot-toast";
import { useShallow } from "zustand/react/shallow";

const ManageLearningPathDeleteConfirmationModal = () => {
  const activeTab = useManageLearningPathTabStore((state) => state.activeTab);

  const { openDeleteModal, setOpenDeleteModal } =
    useManageLearningPathModalStore(
      useShallow(({ openDeleteModal, setOpenDeleteModal }) => ({
        openDeleteModal,
        setOpenDeleteModal,
      }))
    );

  const { currentData, learningCodeQuery, learningLevelQuery, setCurrentData } =
    useManageLearningPathFilterStore(
      useShallow(
        ({
          currentData,
          learningCodeQuery,
          learningLevelQuery,
          setCurrentData,
        }) => ({
          currentData,
          learningCodeQuery,
          learningLevelQuery,
          setCurrentData,
        })
      )
    );

  const learningCodes = useGetLearningCodeListQuery(
    learningCodeQuery,
    activeTab === "learning-code"
  );
  const learningLevels = useGetLearningLevelListQuery(
    learningLevelQuery,
    activeTab === "learning-level"
  );
  const deleteLearningCode = useDeleteLearningCodeMutation();
  const updateLearningLevel = useUpdateLearningLevelMutation();

  const handleDeleteData = () => {
    if (currentData) {
      if (activeTab === "learning-code") {
        deleteLearningCode.mutate(
          {
            params: {
              id: currentData,
            },
          },
          {
            onSuccess: (data) => {
              handleOpenChange(false);
              learningCodes.refetch();
              toast.success(data.message);
            },
            onError: (data) => {
              toast.error(data.message);
            },
          }
        );
      } else {
        updateLearningLevel.mutate(
          {
            params: {
              id: currentData,
            },
            body: {
              is_deleted: true,
            },
          },
          {
            onSuccess: (data) => {
              handleOpenChange(false);
              learningLevels.refetch();
              toast.success(data.message);
            },
            onError: (data) => {
              toast.error(data.message);
            },
          }
        );
      }
    }
  };

  const handleOpenChange = (state: boolean) => {
    if (!state) {
      setCurrentData(null);
    }

    setOpenDeleteModal(state);
  };

  return (
    <BaseDialog open={openDeleteModal} onOpenChange={handleOpenChange}>
      <BaseDialogContent className="h-fit min-w-4/12" showCloseButton={false}>
        <BaseDialogHeader>
          <div className="bg-red-200 w-fit p-2 rounded-full border-8 border-red-100 bg">
            <Trash2 className="text-red-400" size={28} />
          </div>
          <BaseDialogTitle className="flex flex-col gap-4 text-xl mt-3">
            Hapus{" "}
            {activeTab === "learning-code" ? "Learning Code" : "Learning Level"}
            ?
          </BaseDialogTitle>
          <BaseDialogDescription className="text-gray-500 text-sm -mt-1">
            {activeTab === "learning-code" ? "Learning Code" : "Learning Level"}{" "}
            yang dipilih akan dihapus secara permanen. Tindakan ini tidak bisa
            dibatalkan.
          </BaseDialogDescription>
        </BaseDialogHeader>
        <div className="flex justify-end gap-3 mt-4">
          <DialogClose asChild>
            <BaseButton className="w-34 h-11" variant={"outline"}>
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            className="w-34 h-11"
            variant={"destructive"}
            onClick={() => handleDeleteData()}
            disabled={
              deleteLearningCode.isPending || updateLearningLevel.isPending
            }
          >
            Hapus
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default ManageLearningPathDeleteConfirmationModal;
