// update learning code
export interface IUpdateLearningCodeParams {
  id: number;
}

export interface IUpdateLearningCodeBody {
  learning_code?: string;
  learning_code_name?: string;
  job_name_id?: number[];
  status?: "active" | "inactive";
  is_deleted?: boolean;
}

// update learning level
export interface IUpdateLearningLevelParams {
  id: number;
}

export interface IUpdateLearningLevelBody {
  name?: string;
  level?: number;
  status?: "active" | "inactive";
  is_deleted?: boolean;
}
