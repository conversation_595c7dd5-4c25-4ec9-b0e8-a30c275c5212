import { NextRequest, NextResponse } from 'next/server';
import { WAF_TEST_RESPONSES, createMockWafResponse, createMockWafError } from './test-utils';

/**
 * Test WAF detection with various response patterns
 */
async function testWafDetection() {
  const { detectWafBlock } = await import('@/services/satellite/waf-detector');
  
  console.log('🧪 Testing WAF Detection Patterns...\n');

  const testCases = [
    { name: 'Standard WAF Response (200)', type: 'standard', isResponse: true },
    { name: 'Minimal WAF Response (200)', type: 'minimal', isResponse: true },
    { name: 'Alt Support ID WAF (200)', type: 'altSupportId', isResponse: true },
    { name: 'Consultation WAF (200)', type: 'consultation', isResponse: true },
    { name: 'Normal Error (200)', type: 'normalError', isResponse: true },
    { name: 'Standard WAF Error (403)', type: 'standard', isResponse: false },
    { name: 'Minimal WAF Error (403)', type: 'minimal', isResponse: false },
  ] as const;

  for (const testCase of testCases) {
    console.log(`\n📋 Testing: ${testCase.name}`);
    console.log('─'.repeat(50));

    try {
      let detection;
      
      if (testCase.isResponse) {
        const mockResponse = createMockWafResponse(testCase.type);
        detection = detectWafBlock(mockResponse, null);
      } else {
        const mockError = createMockWafError(testCase.type);
        detection = detectWafBlock(null, mockError);
      }

      console.log('Detection Result:', {
        isWafBlock: detection.isWafBlock,
        detectionMethod: detection.detectionMethod,
        supportId: detection.supportId || 'N/A',
        responseLength: detection.responseData.length
      });

      if (detection.isWafBlock) {
        console.log('✅ WAF Block Detected!');
      } else {
        console.log('❌ No WAF Block Detected');
      }

    } catch (error) {
      console.error('❌ Test Error:', error);
    }
  }

  console.log('\n🏁 WAF Detection Tests Completed!\n');
}

/**
 * Test database connection and waf_logs table
 */
async function testDatabaseConnection() {
  console.log('🧪 Testing Database Connection...\n');

  try {
    const { Pool } = await import('pg');
    
    const pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
      max: 1,
      idleTimeoutMillis: 5000,
      connectionTimeoutMillis: 5000,
    });

    console.log('📡 Connecting to database...');
    const client = await pool.connect();
    
    // Set the schema search path
    await client.query('SET search_path TO dev, public;');
    
    console.log('✅ Database connection successful!');
    
    // Check if waf_logs table exists
    console.log('🔍 Checking waf_logs table...');
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'dev' 
        AND table_name = 'waf_logs'
      );
    `);
    
    const tableExists = tableCheck.rows[0].exists;
    console.log(`📋 waf_logs table exists: ${tableExists}`);
    
    if (tableExists) {
      // Get table structure
      const structure = await client.query(`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'waf_logs' 
        ORDER BY ordinal_position;
      `);
      
      console.log('📊 Table structure:');
      structure.rows.forEach(row => {
        console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
      });
      
      // Get row count
      const countResult = await client.query('SELECT COUNT(*) as count FROM waf_logs');
      console.log(`📈 Current row count: ${countResult.rows[0].count}`);
    }
    
    client.release();
    await pool.end();
    
  } catch (error) {
    console.error('❌ Database Test Error:', error);
    throw error;
  }

  console.log('\n🏁 Database Connection Test Completed!\n');
}

/**
 * Test WAF logging functionality with simulated data
 */
async function testWafLogging() {
  const { detectWafBlock } = await import('@/services/satellite/waf-detector');
  const { logWafBlock, getWafLogs } = await import('@/services/satellite/waf-logger');
  
  console.log('🧪 Testing WAF Logging Functionality...\n');

  try {
    // Get initial count
    console.log('📊 Getting initial WAF logs count...');
    const initialLogs = await getWafLogs(1, 0);
    const initialCount = initialLogs.length;
    console.log(`📈 Initial logs count: ${initialCount}`);

    // Test with standard WAF response
    const mockResponse = createMockWafResponse('standard');
    const detection = detectWafBlock(mockResponse, null);

    if (detection.isWafBlock) {
      console.log('📝 Logging WAF block to database...');
      console.log('📋 Log data:', {
        method: mockResponse.config.method?.toUpperCase() || 'GET',
        url: mockResponse.config.url || '/test',
        baseURL: mockResponse.config.baseURL || '',
        supportId: detection.supportId,
        detectionMethod: detection.detectionMethod,
        responseLength: detection.responseData.length
      });
      
      await logWafBlock(
        mockResponse.config.method?.toUpperCase() || 'GET',
        mockResponse.config.url || '/test',
        mockResponse.config.baseURL || '',
        mockResponse.config.headers || {},
        mockResponse.config.data,
        detection.responseData,
        detection.supportId,
        detection.detectionMethod
      );

      console.log('✅ WAF block logged successfully!');
      
      // Verify the log was inserted
      console.log('🔍 Verifying log insertion...');
      const newLogs = await getWafLogs(1, 0);
      const newCount = newLogs.length;
      console.log(`📈 New logs count: ${newCount}`);
      
      if (newCount > initialCount || (newCount > 0 && newLogs[0])) {
        console.log('✅ Log insertion verified!');
        if (newLogs[0]) {
          console.log('📋 Latest log entry:', {
            id: newLogs[0].id,
            route_name: newLogs[0].route_name,
            method: newLogs[0].method,
            created_at: newLogs[0].created_at
          });
        }
      } else {
        console.log('❌ Log insertion could not be verified');
      }
      
    } else {
      console.log('❌ No WAF block detected for logging test');
    }

  } catch (error) {
    console.error('❌ Logging Test Error:', error);
    console.error('❌ Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
  }

  console.log('\n🏁 WAF Logging Test Completed!\n');
}

/**
 * Run comprehensive WAF testing suite
 */
async function runWafTests() {
  console.log('🚀 Starting Comprehensive WAF Testing Suite...\n');
  console.log('='.repeat(60));
  
  await testDatabaseConnection();
  await testWafDetection();
  await testWafLogging();
  
  console.log('='.repeat(60));
  console.log('🎉 All WAF Tests Completed!');
}

/**
 * Inject a test WAF response into the axios interceptor for manual testing
 */
async function injectTestWafResponse(responseType: keyof typeof WAF_TEST_RESPONSES = 'standard') {
  const { detectWafBlock } = await import('@/services/satellite/waf-detector');
  
  const mockResponse = createMockWafResponse(responseType);
  const detection = detectWafBlock(mockResponse, null);
  
  console.log('🧪 Injected Test WAF Response:', {
    type: responseType,
    isWafBlock: detection.isWafBlock,
    detectionMethod: detection.detectionMethod,
    supportId: detection.supportId
  });
  
  return detection;
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const responseType = searchParams.get('type') as keyof typeof WAF_TEST_RESPONSES || 'standard';
  const forceWaf = searchParams.get('waf') === 'true';
  
  if (forceWaf) {
    // Simulate WAF block response
    const wafResponse = WAF_TEST_RESPONSES[responseType] || WAF_TEST_RESPONSES.standard;
    
    // Import WAF detection and logging functions
    const { detectWafBlock } = await import('@/services/satellite/waf-detector');
    const { logWafBlock } = await import('@/services/satellite/waf-logger');
    
    // Create a mock response object for WAF detection
    const mockResponse = {
      status: 200,
      data: wafResponse,
      config: {
        method: 'GET',
        url: request.url,
        baseURL: '',
        headers: Object.fromEntries(request.headers.entries()),
        data: null
      }
    };
    
    // Detect WAF block
    const detection = detectWafBlock(mockResponse, null);
    
    if (detection.isWafBlock) {
      // Log the WAF block to database
      await logWafBlock(
        'GET',
        request.url,
        '',
        Object.fromEntries(request.headers.entries()),
        null,
        detection.responseData,
        detection.supportId,
        detection.detectionMethod
      );
      
      console.log('🚫 WAF Block logged from API Interceptor Test (GET):', {
        url: request.url,
        detectionMethod: detection.detectionMethod,
        supportId: detection.supportId
      });
    }
    
    return new NextResponse(wafResponse, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
      },
    });
  }
  
  // Normal API response
  return NextResponse.json({
    message: 'Normal API response',
    timestamp: new Date().toISOString(),
    requestedType: responseType,
  });
}

export async function POST(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const responseType = searchParams.get('type') as keyof typeof WAF_TEST_RESPONSES || 'standard';
  const forceWaf = searchParams.get('waf') === 'true';
  
  try {
    const body = await request.json();
    
    if (forceWaf) {
      // Simulate WAF block with 403 status
      const wafResponse = WAF_TEST_RESPONSES[responseType] || WAF_TEST_RESPONSES.standard;
      
      // Import WAF detection and logging functions
      const { detectWafBlock } = await import('@/services/satellite/waf-detector');
      const { logWafBlock } = await import('@/services/satellite/waf-logger');
      
      // Create a mock error object for WAF detection
      const mockError = {
        response: {
          status: 403,
          data: wafResponse,
          config: {
            method: 'POST',
            url: request.url,
            baseURL: '',
            headers: Object.fromEntries(request.headers.entries()),
            data: body
          }
        }
      };
      
      // Detect WAF block
      const detection = detectWafBlock(null, mockError);
      
      if (detection.isWafBlock) {
        // Log the WAF block to database
        await logWafBlock(
          'POST',
          request.url,
          '',
          Object.fromEntries(request.headers.entries()),
          body,
          detection.responseData,
          detection.supportId,
          detection.detectionMethod
        );
        
        console.log('🚫 WAF Block logged from API Interceptor Test (POST):', {
          url: request.url,
          detectionMethod: detection.detectionMethod,
          supportId: detection.supportId
        });
      }
      
      return new NextResponse(wafResponse, {
        status: 403,
        headers: {
          'Content-Type': 'text/html',
        },
      });
    }
    
    // Normal API response
    return NextResponse.json({
      message: 'POST request successful',
      receivedData: body,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid JSON body' },
      { status: 400 }
    );
  }
}

export async function PUT(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const testType = searchParams.get('test');
  
  try {
    let result;
    
    switch (testType) {
      case 'database':
        await testDatabaseConnection();
        result = { message: 'Database connection test completed', type: 'database' };
        break;
        
      case 'detection':
        await testWafDetection();
        result = { message: 'WAF detection test completed', type: 'detection' };
        break;
        
      case 'logging':
        await testWafLogging();
        result = { message: 'WAF logging test completed', type: 'logging' };
        break;
        
      case 'full':
        await runWafTests();
        result = { message: 'Full WAF test suite completed', type: 'full' };
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid test type. Use: database, detection, logging, or full' },
          { status: 400 }
        );
    }
    
    return NextResponse.json(result);
    
  } catch (error) {
    console.error('Test execution error:', error);
    return NextResponse.json(
      { error: 'Test execution failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}