"use server";

import {
  IGetCompetencyListQuery,
  IGetCompetencyListResponse,
} from "@/interfaces/admin/master/competency";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite/";
import { handleAxiosError } from "@/utils/common/axios";

// Dummy data for competency
const DUMMY_COMPETENCY_DATA: IGlobalResponseDto<IGetCompetencyListResponse[]> = {
  status: true,
  message: "success get data competency",
  data: [
    {
      id: 1,
      name: "Communication",
      type: "behavioral"
    },
    {
      id: 2,
      name: "Teamwork",
      type: "behavioral"
    },
    {
      id: 3,
      name: "Leadership",
      type: "behavioral"
    },
    {
      id: 4,
      name: "Problem Solving",
      type: "behavioral"
    },
    {
      id: 5,
      name: "Time Management",
      type: "behavioral"
    },
    {
      id: 6,
      name: "Adaptability",
      type: "behavioral"
    },
    {
      id: 7,
      name: "Critical Thinking",
      type: "behavioral"
    },
    {
      id: 8,
      name: "JavaScript Programming",
      type: "technical"
    },
    {
      id: 9,
      name: "Database Management",
      type: "technical"
    },
    {
      id: 10,
      name: "System Architecture",
      type: "technical"
    },
    {
      id: 11,
      name: "Cloud Computing",
      type: "technical"
    },
    {
      id: 12,
      name: "Data Analysis",
      type: "technical"
    },
    {
      id: 13,
      name: "Network Security",
      type: "technical"
    },
    {
      id: 14,
      name: "Mobile Development",
      type: "technical"
    },
    {
      id: 15,
      name: "DevOps",
      type: "technical"
    }
  ]
};

export const apiGetCompetencyList = async (
  query?: IGetCompetencyListQuery
) => {
  try {
    // TODO: Uncomment when API is ready
    // const response = await api.get<
    //   IGlobalResponseDto<IGetCompetencyListResponse[]>
    // >("/cms/admin/master/competency", { params: query });
    // return response.data;

    // Return dummy data for now
    let filteredData = [...DUMMY_COMPETENCY_DATA.data];

    // Apply filters
    if (query?.type) {
      filteredData = filteredData.filter(item => item.type === query.type);
    }

    if (query?.search) {
      const searchLower = query.search.toLowerCase();
      filteredData = filteredData.filter(item => 
        item.name.toLowerCase().includes(searchLower)
      );
    }

    // Apply sorting
    if (query?.order_by) {
      filteredData.sort((a, b) => {
        if (query.order_by === "name") {
          return a.name.localeCompare(b.name);
        }
        return a.id - b.id; // default to id
      });
    }

    return {
      ...DUMMY_COMPETENCY_DATA,
      data: filteredData
    };
  } catch (error) {
    throw handleAxiosError(error);
  }
};
