export interface IQuestionTemplateDetailResponse {
  id: number;
  question_template_name: string | null;
  type: string | null;
  categories: { id: number | null; name: string | null }[];
  levels: { id: number | null; name: string | null }[];
  questions_id: number[];
  feature: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}
