export interface ICreateSectionBody {
  section_name: string;
  section_type: string;
  material_repository_id?: number;
  material_link?: string;
  passing_grade?: number;
  number_of_question?: number;
  with_test_timer?: boolean;
  duration?: number;
  assignment_instruction?: string;
  question_id?: number[];
  competency_id?: number[];
  category_id?: number[];
  level_id?: number[];
}

export interface ICreateSectionResponse {
  status: boolean;
  message: string;
  data?: any;
}

// Section type mapping for API
export const SECTION_TYPE_MAPPING = {
  "video": "MULMOD",
  "audio": "AUDIO", 
  "document": "PDF",
  "quiz": "QUIZ",
  "pre-test": "PRE-TEST",
  "post-test isian": "POST-TEST Isian",
  "post-test pilihan ganda": "POST-TEST Pilihan Ganda",
  "assignment": "ASSIGNMENT",
  "test": "TEST"
} as const;

export type SectionTypeKey = keyof typeof SECTION_TYPE_MAPPING;
export type SectionTypeValue = typeof SECTION_TYPE_MAPPING[SectionTypeKey];
