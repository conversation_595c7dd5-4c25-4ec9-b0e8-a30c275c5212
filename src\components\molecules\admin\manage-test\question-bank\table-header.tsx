'use client';

import React from 'react';
import QuestionBankTableHeaderSearch from './search';
import QuestionBankTableHeaderAction from './action';
import QuestionBankTableHeaderFilter from './filter';

const QuestionBankTableHeader = () => {
  const [openFilter, setOpenFilter] = React.useState<boolean>(false);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <QuestionBankTableHeaderSearch />
        <QuestionBankTableHeaderAction onOpenFilter={setOpenFilter} />
      </div>

      {openFilter ? <QuestionBankTableHeaderFilter /> : null}
    </div>
  );
};

export default QuestionBankTableHeader;
