import { IGetJobPositionListResponse } from "../manage-job/list";

export interface IGetLearningCodeResponse {
  learning_id: number;
  learning_code: string;
  learning_code_name: string;
  related_job_position: string[];
  assosiated_module: string[];
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string;
  is_active: boolean;
}

export interface IGetLearningLevelResponse {
  level_id: number;
  level: number;
  level_name: string;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string;
  is_active: boolean;
}

// Learning code list
export interface IGetLearningCodeListQuery {
  search?: string;
  search_by?:
    | "code"
    | "name"
    | "job_name"
    | "modul_name"
    | "created_by"
    | "updated_by";
  page?: number | null;
  limit?: number | null;
}

export interface IGetLearningCodeListResponse {
  ID: number | null;
  code: string | null;
  name: string | null;
  related_job: { job_name_id: number | null; job_name: string | null }[];
  related_modul: { modul_id: number | null; modul_name: string | null }[];
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
  status: string | null;
}

// Learning code detail
export interface IGetLearningCodeDetailParams {
  id?: number | null;
}

export interface IGetLearningCodeDetailResponse {
  ID: number | null;
  code: string | null;
  name: string | null;
  related_job: IGetJobPositionListResponse[];
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
  status: string | null;
}

// Learning level list

export interface IGetLearningLevelListQuery {
  search?: string;
  search_by?: "level" | "name" | "modul_name" | "created_by" | "updated_by";
  page?: number | null;
  limit?: number | null;
}

export interface IGetLearningLevelListResponse {
  id: number | null;
  name: string | null;
  level: number | null;
  status: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}

export interface IGetLearningLevelDetailParams {
  id?: number | null;
}

export interface IGetLearningLevelDetailResponse {
  id: number | null;
  name: string | null;
  level: number | null;
  status: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}
