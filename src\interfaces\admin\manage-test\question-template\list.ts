export type TQTFeature = 'OnlineLearning' | 'InClassTraining';

export type TQTSearchBy =
  | 'template_id'
  | 'template_name'
  | 'question'
  | 'created_by'
  | 'updated_by';

export interface IGetQuestionTemplateListQuery {
  category_id?: number;
  level_id?: number;
  template_type?: string;
  feature?: TQTFeature;
  search?: string | number;
  search_by?: TQTSearchBy;
  page?: number;
  limit?: number;
}

export interface IGetQuestionTemplateListResponse {
  id: number;
  question_template_name: string | null;
  type: string | null;
  categories: { id: number | null; name: string | null }[];
  levels: { id: number | null; name: string | null }[];
  questions: { id: number | null; question: string | null }[];
  feature: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}
