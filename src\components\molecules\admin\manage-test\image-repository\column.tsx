import { BaseButton } from '@/components/atoms/button';
import { ColumnDef } from '@tanstack/react-table';
import dayjs from 'dayjs';
import { Download, Pencil, Trash2 } from 'lucide-react';
import PillDropdown from '../common/dropdown';
import { IGetImageRepositoryListResponse } from '@/interfaces/admin/manage-test/image-repository/list';

interface Props {
  onEdit: (row: IGetImageRepositoryListResponse) => void;
  onDelete: (row: IGetImageRepositoryListResponse) => void;
  onDownload: (row: IGetImageRepositoryListResponse) => void;
}

const makeOptions = (names: string[], id: string) =>
  names.filter(Boolean).map((name) => ({ value: String(name), id }));

const SmartPill: React.FC<{
  id: string;
  names: string[];
  summaryLabel: string;
}> = ({ id, names, summaryLabel }) => {
  const n = names.length;
  if (n === 0) return <span>-</span>;
  if (n === 1) {
    return (
      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-[#DEDEDE] text-[#3C3C3C]">
        {names[0]}
      </span>
    );
  }
  const limited = names.slice(0, 5);
  return (
    <PillDropdown
      id={id}
      selected={summaryLabel}
      options={makeOptions(limited, id)}
    />
  );
};

const summary = (n: number, word: string) => `${n} ${word}`;

const formatSize = (size?: number | null) => {
  if (size == null) return '-';
  const kb = size / 1024;
  const mb = kb / 1024;
  if (mb >= 1) return `${mb.toFixed(1)} MB`;
  if (kb >= 1) return `${kb.toFixed(0)} KB`;
  return `${size} B`;
};

export const getColumnsImageRepository = ({
  onEdit,
  onDelete,
  onDownload,
}: Props): ColumnDef<IGetImageRepositoryListResponse>[] => {
  return [
    {
      accessorKey: 'id',
      header: 'Image ID',
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.id}
          </span>
        );
      },
    },

    {
      accessorKey: 'categories',
      header: 'Category',
      cell({ row }) {
        const names = (row.original.categories ?? [])
          .map((c) => c?.name)
          .filter(Boolean) as string[];
        const label = summary(names.length, 'Category');
        return (
          <SmartPill
            id={String(row.original.id)}
            names={names}
            summaryLabel={label}
          />
        );
      },
    },

    {
      accessorKey: 'levels',
      header: 'Level',
      cell({ row }) {
        const names = (row.original.levels ?? [])
          .map((l) => l?.name)
          .filter(Boolean) as string[];
        const label = summary(names.length, 'Levels');
        return (
          <SmartPill
            id={String(row.original.id)}
            names={names}
            summaryLabel={label}
          />
        );
      },
    },

    {
      accessorKey: 'image_name',
      header: 'Image Name',
      cell({ row }) {
        return (
          <div className="w-[200px] text-wrap line-clamp-2">
            {row.original.image_name ?? '-'}
          </div>
        );
      },
    },

    {
      accessorKey: 'file_format',
      header: 'Format',
      cell({ row }) {
        return <span>{row.original.file_format ?? '-'}</span>;
      },
    },

    {
      accessorKey: 'file_size',
      header: 'Size',
      cell({ row }) {
        return <span>{formatSize(row.original.file_size)}</span>;
      },
    },

    {
      accessorKey: 'associated',
      header: 'Associated Question',
      cell({ row }) {
        const names = (row.original.associated ?? [])
          .map((a) => a?.question)
          .filter(Boolean) as string[];
        const label = summary(names.length, 'Questions');
        return (
          <SmartPill
            id={String(row.original.id)}
            names={names}
            summaryLabel={label}
          />
        );
      },
    },

    {
      accessorKey: 'created_at',
      header: 'Uploaded At',
      cell({ row }) {
        const v = row.original.created_at;
        return <span>{v ? dayjs(v).format('DD MMM YYYY HH:mm') : '-'}</span>;
      },
    },

    { accessorKey: 'created_by', header: 'Uploaded By' },

    {
      accessorKey: 'last_updated',
      header: 'Last Updated',
      cell({ row }) {
        const v = row.original.last_updated;
        return <span>{v ? dayjs(v).format('DD MMM YYYY HH:mm') : '-'}</span>;
      },
    },

    { accessorKey: 'updated_by', header: 'Updated By' },

    {
      id: 'action',
      header: 'Action',
      cell({ row }) {
        return (
          <div className="flex items-center justify-start">
            <BaseButton
              variant="ghost"
              className="border-none"
              onClick={() => onDownload(row.original)}
            >
              <Download size={20} />
            </BaseButton>
            <BaseButton
              variant="ghost"
              className="border-none"
              onClick={() => onEdit(row.original)}
            >
              <Pencil
                size={20}
                className="fill-gray-700 text-white"
                strokeWidth={1}
              />
            </BaseButton>
            <BaseButton
              variant="ghost"
              className="border-none"
              onClick={() => onDelete(row.original)}
            >
              <Trash2
                size={20}
                className="text-gray-700"
                strokeWidth={2.5}
              />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};
