"use client";

import React, { useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { BaseButton } from "@/components/atoms/button";
import { Plus, Trash2 } from "lucide-react";
import {
  BaseTable,
  BaseTableBody,
  BaseTableCell,
  BaseTableHead,
  BaseTableHeader,
  BaseTableRow,
} from "@/components/atoms/table";
import { BaseLabel } from "@/components/atoms/label";
import QuestionBankSelectionModal from "./question-bank-selection-modal";
import QuestionTemplateSelectionModal from "./question-template-selection-modal";
import { IGetQuestionBankListResponse } from "@/interfaces/admin/manage-test/question-bank/list";
import { IGetQuestionTemplateListResponse } from "@/interfaces/admin/manage-test/question-template/list";

interface QuestionSelectorProps {
  name: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
}

interface SelectedQuestion {
  id: number;
  question: string;
  type: string;
  source: "bank" | "template";
  templateName?: string;
  options?: {
    a: string;
    b: string;
    c: string;
    d: string;
  };
  correctAnswer?: string;
}

const QuestionSelector: React.FC<QuestionSelectorProps> = ({
  name,
  label = "Questions",
  required = false,
  disabled = false,
}) => {
  const { control, setValue, watch } = useFormContext();
  const [isQuestionBankModalOpen, setIsQuestionBankModalOpen] = useState(false);
  const [isQuestionTemplateModalOpen, setIsQuestionTemplateModalOpen] = useState(false);

  const selectedQuestions: SelectedQuestion[] = watch(name) || [];

  const handleAddQuestionsFromBank = (questions: IGetQuestionBankListResponse[]) => {
    const newQuestions: SelectedQuestion[] = questions.map((q) => ({
      id: q.id,
      question: q.question || "",
      type: q.type || "",
      source: "bank",
      options: {
        a: q.option_a || "",
        b: q.option_b || "",
        c: q.option_c || "",
        d: q.option_d || "",
      },
      correctAnswer: q.correct_answer || "",
    }));

    const existingIds = selectedQuestions.map(q => q.id);
    const filteredNewQuestions = newQuestions.filter(q => !existingIds.includes(q.id));
    
    const updatedQuestions = [...selectedQuestions, ...filteredNewQuestions];
    setValue(name, updatedQuestions);
    
    // Update questionId array for API
    const questionIds = updatedQuestions.map(q => q.id.toString());
    setValue("questionId", questionIds);
    
    // Update numberOfQuestions
    setValue("numberOfQuestions", updatedQuestions.length);
  };

  const handleAddQuestionsFromTemplate = (template: IGetQuestionTemplateListResponse) => {
    const newQuestions: SelectedQuestion[] = template.questions.map((q) => ({
      id: q.id || 0,
      question: q.question || "",
      type: template.type || "",
      source: "template",
      templateName: template.question_template_name || "",
    }));

    const existingIds = selectedQuestions.map(q => q.id);
    const filteredNewQuestions = newQuestions.filter(q => !existingIds.includes(q.id));
    
    const updatedQuestions = [...selectedQuestions, ...filteredNewQuestions];
    setValue(name, updatedQuestions);
    
    // Update questionId array for API
    const questionIds = updatedQuestions.map(q => q.id.toString());
    setValue("questionId", questionIds);
    
    // Update numberOfQuestions
    setValue("numberOfQuestions", updatedQuestions.length);
  };

  const handleRemoveQuestion = (questionId: number) => {
    const updatedQuestions = selectedQuestions.filter(q => q.id !== questionId);
    setValue(name, updatedQuestions);
    
    // Update questionId array for API
    const questionIds = updatedQuestions.map(q => q.id.toString());
    setValue("questionId", questionIds);
    
    // Update numberOfQuestions
    setValue("numberOfQuestions", updatedQuestions.length);
  };

  const handleClearAll = () => {
    setValue(name, []);
    setValue("questionId", []);
    setValue("numberOfQuestions", 0);
  };

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <BaseLabel className="text-sm font-medium">
              {label}
              {required && <span className="text-red-500 ml-1">*</span>}
            </BaseLabel>
            {selectedQuestions.length > 0 && (
              <BaseButton
                type="button"
                variant="outline"
                size="sm"
                onClick={handleClearAll}
                disabled={disabled}
                className="text-red-600 border-red-200 hover:bg-red-50"
              >
                <Trash2 className="w-4 h-4 mr-1" />
                Clear All
              </BaseButton>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <BaseButton
              type="button"
              variant="outline"
              onClick={() => setIsQuestionBankModalOpen(true)}
              disabled={disabled}
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add from Question Bank
            </BaseButton>
            <BaseButton
              type="button"
              variant="outline"
              onClick={() => setIsQuestionTemplateModalOpen(true)}
              disabled={disabled}
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add from Question Template
            </BaseButton>
          </div>

          {/* Selected Questions Table */}
          {selectedQuestions.length > 0 ? (
            <div className="border rounded-lg">
              <BaseTable>
                <BaseTableHeader>
                  <BaseTableRow>
                    <BaseTableHead className="w-16">No</BaseTableHead>
                    <BaseTableHead className="w-20">ID</BaseTableHead>
                    <BaseTableHead>Question</BaseTableHead>
                    <BaseTableHead className="w-24">Type</BaseTableHead>
                    <BaseTableHead className="w-24">Source</BaseTableHead>
                    <BaseTableHead className="w-16">Action</BaseTableHead>
                  </BaseTableRow>
                </BaseTableHeader>
                <BaseTableBody>
                  {selectedQuestions.map((question, index) => (
                    <BaseTableRow key={question.id}>
                      <BaseTableCell className="font-medium">
                        {index + 1}
                      </BaseTableCell>
                      <BaseTableCell>{question.id}</BaseTableCell>
                      <BaseTableCell className="max-w-md">
                        <div className="truncate" title={question.question}>
                          {question.question}
                        </div>
                        {question.source === "template" && question.templateName && (
                          <div className="text-xs text-gray-500 mt-1">
                            Template: {question.templateName}
                          </div>
                        )}
                      </BaseTableCell>
                      <BaseTableCell>
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                          {question.type}
                        </span>
                      </BaseTableCell>
                      <BaseTableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          question.source === "bank" 
                            ? "bg-green-100 text-green-800" 
                            : "bg-purple-100 text-purple-800"
                        }`}>
                          {question.source === "bank" ? "Question Bank" : "Template"}
                        </span>
                      </BaseTableCell>
                      <BaseTableCell>
                        <BaseButton
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveQuestion(question.id)}
                          disabled={disabled}
                          className="text-red-600 hover:text-red-800 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </BaseButton>
                      </BaseTableCell>
                    </BaseTableRow>
                  ))}
                </BaseTableBody>
              </BaseTable>
            </div>
          ) : (
            <div className="border-2 border-dashed border-gray-200 rounded-lg p-8 text-center">
              <div className="text-gray-500">
                <Plus className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No questions selected</p>
                <p className="text-xs text-gray-400 mt-1">
                  Click the buttons above to add questions from Question Bank or Question Template
                </p>
              </div>
            </div>
          )}

          {/* Summary */}
          {selectedQuestions.length > 0 && (
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="text-sm text-blue-800">
                <strong>Total Questions: {selectedQuestions.length}</strong>
                <div className="mt-1 text-xs">
                  From Question Bank: {selectedQuestions.filter(q => q.source === "bank").length} | 
                  From Templates: {selectedQuestions.filter(q => q.source === "template").length}
                </div>
              </div>
            </div>
          )}

          {error && (
            <p className="text-sm text-red-600">{error.message}</p>
          )}

          {/* Modals */}
          <QuestionBankSelectionModal
            isOpen={isQuestionBankModalOpen}
            onClose={() => setIsQuestionBankModalOpen(false)}
            onAddQuestions={handleAddQuestionsFromBank}
            excludeIds={selectedQuestions.map(q => q.id)}
          />

          <QuestionTemplateSelectionModal
            isOpen={isQuestionTemplateModalOpen}
            onClose={() => setIsQuestionTemplateModalOpen(false)}
            onAddQuestions={handleAddQuestionsFromTemplate}
          />
        </div>
      )}
    />
  );
};

export default QuestionSelector;
