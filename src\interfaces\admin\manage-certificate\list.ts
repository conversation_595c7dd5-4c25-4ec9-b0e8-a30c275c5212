export interface ICertificate {
  id: number | null;
  user_id: number | null;
  npk: string | null;
  name: string | null;
  module_id: number | null;
  module_name: string | null;
  module_type: string | null;
  level_id: number | null;
  level: number | null;
  module_assignment_id: number | null;
  attempt: string | null;
  issued_date: string | null;
  expired_date: string | null;
  status: string | null;
}

export interface IGetListCertificateQuery {
  page?: number;
  limit?: number;
  search?: string;
  search_by?: "npk" | "user_id" | "name" | "module_name" | "level" | "attempt";
  module_type?: string[] | string;
  status?: "active" | "expired";
  start_issue_date?: string; // yyyy-mm-dd
  end_issue_date?: string; // yyyy-mm-dd
  start_expired_date?: string; // yyyy-mm-dd
  end_expired_date?: string; // yyyy-mm-dd
}

export interface IGetListCertificateResponse {
  id: number | null;
  user_id: number | null;
  npk: string | null;
  name: string | null;
  module_id: number | null;
  module_name: string | null;
  module_type: string | null;
  level_id: number | null;
  level: number | null;
  module_assignment_id: number | null;
  attempt: string | null;
  issued_date: string | null;
  expired_date: string | null;
  status: string | null;
}

export interface ICertificateFilter {
  module_type: string[];
  status: "active" | "expired" | undefined;
  start_issue_date: string | undefined;
  end_issue_date: string | undefined;
  start_expired_date: string | undefined;
  end_expired_date: string | undefined;
}

export interface IDetailCertificate {
  user_id: number;
  user_name: string;
  user_email: string;
  user_avatar: null;
  issued_date: Date;
  expired_date: Date | null;
  module_name: string;
  module_description: string;
  link: string;
  skill_name: string;
}
