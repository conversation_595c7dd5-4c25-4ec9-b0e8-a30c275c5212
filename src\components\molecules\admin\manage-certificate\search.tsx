import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { SEARCH_BY_OPTIONS } from "@/utils/data/certificate";
import { useManageCertificateQueryStore } from "@/store/admin/manage-certificate/query";
import { Search } from "lucide-react";
import lodash from "lodash";
import { useShallow } from "zustand/react/shallow";

const ManageCertificateTableHeaderSearch = () => {
  const { certificateQuery, setCertificateQuery } =
    useManageCertificateQueryStore(
      useShallow((state) => ({
        certificateQuery: state.certificateQuery,
        setCertificateQuery: state.setCertificateQuery,
      }))
    );

  const handleQueryChange = (query: Partial<typeof certificateQuery>) => {
    setCertificateQuery(query);
  };

  return (
    <div className="text-[#3C3C3C] font-semibold rounded-lg flex items-center gap-1 relative w-[40%] bg-white px-3">
      <div>
        <BaseSelect
          value={certificateQuery.search_by || "-"}
          onValueChange={(value) =>
            handleQueryChange({ search_by: value as any })
          }
        >
          <BaseSelectTrigger className="w-44 border-none px-1">
            <BaseSelectValue placeholder="Search By">
              {SEARCH_BY_OPTIONS.find(
                (it) => it.value === certificateQuery.search_by
              )?.label || "Search By"}
            </BaseSelectValue>
          </BaseSelectTrigger>
          <BaseSelectContent>
            {/* <BaseSelectItem value={"-"} disabled>
              Search By
            </BaseSelectItem> */}
            {SEARCH_BY_OPTIONS.map((option) => (
              <BaseSelectItem key={option.value} value={option.value}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>
      <BaseSeparator orientation="vertical" />
      <BaseInput
        className="border-none h-12 focus-visible:border-none focus-visible:ring-0"
        placeholder="Search..."
        defaultValue={certificateQuery.search || ""}
        onChange={lodash.debounce(
          (e) => handleQueryChange({ page: 1, search: e?.target?.value }),
          800
        )}
      />
      <Search size={24} />
      <BaseSeparator orientation="vertical" className="bg-red-500 w-1" />
    </div>
  );
};

export default ManageCertificateTableHeaderSearch;
