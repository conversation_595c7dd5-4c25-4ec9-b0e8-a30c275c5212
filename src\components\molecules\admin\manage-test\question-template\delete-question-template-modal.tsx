'use client';

import { BaseButton } from '@/components/atoms/button';
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogHeader,
  BaseDialogTitle,
} from '@/components/atoms/dialog';
import { DialogClose } from '@/components/ui/dialog';
import { useDeleteQuestionTemplateMutation } from '@/services/mutation/admin/manage-test/question-template';
import { useQuestionTemplateModal } from '@/store/admin/manage-test/question-template/modal';
import { Trash2 } from 'lucide-react';
import React from 'react';
import toast from 'react-hot-toast';
import { useShallow } from 'zustand/react/shallow';

const QuestionTemplateDeleteConfirmationModal = () => {
  const {
    openedQuestionTemplate,
    openDeleteModal,
    setOpenDeleteModal,
    setOpenedQuestionTemplate,
  } = useQuestionTemplateModal(
    useShallow(
      ({
        openedQuestionTemplate,
        openDeleteModal,
        setOpenDeleteModal,
        setOpenedQuestionTemplate,
      }) => ({
        openedQuestionTemplate,
        openDeleteModal,
        setOpenDeleteModal,
        setOpenedQuestionTemplate,
      })
    )
  );

  const del = useDeleteQuestionTemplateMutation();

  const handleOpenChange = (state: boolean) => {
    if (!state) setOpenedQuestionTemplate(null);
    setOpenDeleteModal(state);
  };

  const handleDeleteData = () => {
    const id = openedQuestionTemplate?.id;
    if (!id) {
      toast.error('Invalid template');
      return;
    }

    del.mutate(
      { id },
      {
        onSuccess: () => {
          handleOpenChange(false);
        },
      }
    );
  };

  return (
    <BaseDialog
      open={openDeleteModal}
      onOpenChange={handleOpenChange}
    >
      <BaseDialogContent
        className="h-fit min-w-4/12"
        showCloseButton={false}
      >
        <BaseDialogHeader>
          <div className="bg-red-200 w-fit p-2 rounded-full border-8 border-red-100 bg">
            <Trash2
              className="text-red-400"
              size={28}
            />
          </div>
          <BaseDialogTitle className="flex flex-col gap-4 text-xl mt-3">
            Hapus Question Template?
          </BaseDialogTitle>
          <BaseDialogDescription className="text-gray-500 text-sm -mt-1">
            Question Template yang dipilih akan dihapus secara permanen.
            Tindakan ini tidak bisa dibatalkan.
          </BaseDialogDescription>
        </BaseDialogHeader>
        <div className="flex justify-end gap-3 mt-4">
          <DialogClose asChild>
            <BaseButton
              className="w-34 h-11"
              variant={'outline'}
            >
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            className="w-34 h-11"
            variant={'destructive'}
            onClick={handleDeleteData}
            disabled={del.isPending}
          >
            Hapus
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default QuestionTemplateDeleteConfirmationModal;
