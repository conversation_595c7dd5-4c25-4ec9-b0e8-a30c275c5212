import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  Base<PERSON>ialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { DialogClose } from "@/components/ui/dialog";
import React from "react";
import { useForm, FormProvider } from "react-hook-form";
import LevelSelector from "./form/level-selector";
import CategorySelector from "./form/category-selector";
import UploadedFilesList, { UploadedFile } from "./form/uploaded-file-list";
import FileUploader, { FILE_CONFIGS, MaterialType } from "./form/file-uploader";
import Pagination from "./form/pagination";
import { IInsertLearningMaterialData } from "@/interfaces/admin/manage-material/list";
import { useInsertLearningMaterialMutation } from "@/services/mutation/admin/manage-material";
import { notifyHotError } from "../../toast/hot-toast";
import Spinner from "@/components/atoms/spinner";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

interface Option {
  value: string;
  label: string;
}
interface FormData {
  categories: Option[];
  level: Option[];
  files: UploadedFile[];
}

// Main Modal Component
interface ManageMaterialNewModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: MaterialType;
}

const ManageMaterialNewModal = ({
  isOpen,
  onClose,
  type,
}: ManageMaterialNewModalProps) => {
  const config = FILE_CONFIGS[type];

  return (
    <BaseDialog open={isOpen} onOpenChange={onClose}>
      <BaseDialogContent className="w-full max-w-[600px] sm:max-w-[808px] max-h-[90vh] overflow-y-auto">
        <BaseDialogHeader>
          <BaseDialogTitle className="flex items-center justify-between">
            <span>{config.title}</span>
          </BaseDialogTitle>
        </BaseDialogHeader>
        <MaterialForm type={type} onClose={onClose} />
      </BaseDialogContent>
    </BaseDialog>
  );
};

// Form Component
interface MaterialFormProps {
  type: MaterialType;
  onClose: () => void;
}

const schema = yup.object({
  categories: yup
    .array()
    .min(1, "Please select at least one category")
    .required(),
  level: yup.array().min(1, "Please select at least one level").required(),
  files: yup.array().min(1, "Please upload at least one file").required(),
});

const MaterialForm = ({ type, onClose }: MaterialFormProps) => {
  const form = useForm<yup.InferType<typeof schema>>({
    defaultValues: {
      categories: [],
      level: [],
      files: [],
    },
    resolver: yupResolver(schema),
  });

  const { mutateAsync: insertLearningMaterial, isPending: pendingInsert } =
    useInsertLearningMaterialMutation();

  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([]);
  const [currentPage, setCurrentPage] = React.useState(1);
  const filesPerPage = 3;
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const config = FILE_CONFIGS[type];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    form.setValue("files", files);

    files.forEach((file) => {
      const newFile: UploadedFile = {
        id: Math.random().toString(36).substr(2, 9),
        name: file.name,
        size: file.size,
        type: file.type,
        status: "success",
        progress: 100,
        file,
      };

      // directly upload files
      setUploadedFiles((prev) => [...prev, newFile]);

      // Simulate upload progress
      // const interval = setInterval(() => {
      //   setUploadedFiles((prev) =>
      //     prev.map((f) =>
      //       f.id === newFile.id
      //         ? {
      //             ...f,
      //             progress: Math.min((f.progress || 0) + 10, 100),
      //             status: (f.progress || 0) >= 90 ? "success" : "uploading",
      //             uploadDate:
      //               (f.progress || 0) >= 90 ? "22 Jun, 2022" : undefined,
      //           }
      //         : f
      //     )
      //   );
      // }, 200);

      // setTimeout(() => clearInterval(interval), 2000);
    });
  };

  const handleRetryUpload = (fileId: string) => {
    setUploadedFiles((prev) =>
      prev.map((f) =>
        f.id === fileId ? { ...f, status: "uploading", progress: 0 } : f
      )
    );
  };

  const handleDeleteFile = (fileId: string) => {
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  const onSubmit = async (data: FormData) => {
    const submitData = {
      ...data,
      files: uploadedFiles,
    };

    const formData = new FormData();
    const file = submitData.files[0].file as File;

    const payloadData: IInsertLearningMaterialData = {
      category_id: submitData.categories.map((category) =>
        Number(category.value)
      ),
      level_id: submitData.level.map((level) => Number(level.value)),
      feature: "OnlineLearning",
    };
    formData.append("file", file);
    formData.append("file_type", type);
    formData.append("data", JSON.stringify(payloadData));

    try {
      const response = await insertLearningMaterial(formData);
      console.log("Response:", response);
      onClose();
    } catch (error: any) {
      notifyHotError(error.message ?? "Failed to insert learning material");
    }
  };

  const totalFiles = uploadedFiles.length;
  const totalPages = Math.ceil(totalFiles / filesPerPage);
  const startIndex = (currentPage - 1) * filesPerPage;
  const endIndex = startIndex + filesPerPage;
  const currentFiles = uploadedFiles.slice(startIndex, endIndex);

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Category Selection */}
        <CategorySelector />

        {/* Level Selection */}
        <LevelSelector />

        {/* File Upload */}
        {uploadedFiles.length === 0 && (
          <FileUploader
            config={config}
            form={form}
            onFileUpload={handleFileUpload}
            ref={fileInputRef}
          />
        )}

        {/* Uploaded Files List */}
        {uploadedFiles.length > 0 && (
          <UploadedFilesList
            files={currentFiles}
            onRetry={handleRetryUpload}
            onDelete={handleDeleteFile}
          />
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalFiles={totalFiles}
            onPageChange={setCurrentPage}
          />
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4">
          <DialogClose asChild>
            <BaseButton variant="outline" className="px-8">
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            type="submit"
            className="px-8 bg-orange-500 hover:bg-orange-600"
            disabled={pendingInsert}
          >
            {pendingInsert ? (
              <Spinner />
            ) : (
              config.title.replace("Add New ", "Add ")
            )}
          </BaseButton>
        </div>
      </form>
    </FormProvider>
  );
};

export default ManageMaterialNewModal;
