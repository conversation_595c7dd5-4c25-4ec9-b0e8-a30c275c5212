'use client';

import React, { useEffect } from 'react';
import { BaseButton } from '@/components/atoms/button';
import { BaseSeparator } from '@/components/atoms/separator';
import MultipleSelectorComponent from '@/components/atoms/multiple-selector';
import { Option } from '@/components/atoms/multiselect';
import { InputSelect } from '../common/select';
import { useImageRepositoryFilterStore } from '@/store/admin/manage-test/image-repository/filter';
import { useShallow } from 'zustand/react/shallow';
import {
  useGetCategoryListQuery,
  useGetStartingLevelListQuery,
} from '@/services/query/admin/master';

const ImageRepositoryTableHeaderFilter = () => {
  const {
    draft,
    setDraft,
    resetDraft,
    applyDraft,
    initDraftFromQuery,
    resetQuery,
  } = useImageRepositoryFilterStore(
    useShallow(
      ({
        draft,
        setDraft,
        resetDraft,
        applyDraft,
        initDraftFromQuery,
        resetQuery,
      }) => ({
        draft,
        setDraft,
        resetDraft,
        applyDraft,
        initDraftFromQuery,
        resetQuery,
      })
    )
  );

  useEffect(() => {
    initDraftFromQuery();
  }, [initDraftFromQuery]);

  const startingLearningLevels = useGetStartingLevelListQuery({
    order_by: 'name',
    order: 'asc',
  });
  const masterCategory = useGetCategoryListQuery();

  const levelOptions: Option[] =
    startingLearningLevels?.data?.data?.map?.((it: any) => ({
      value: String(it?.id ?? ''),
      label: String(it?.name ?? ''),
    })) ?? [];

  const categoryOptions: Option[] =
    masterCategory?.data?.data?.map?.((it: any) => ({
      value: String(it?.id ?? ''),
      label: String(it?.category_name ?? ''),
    })) ?? [];

  const findLabel = (value: string) =>
    categoryOptions.find((o) => o.value === value)?.label ?? value;

  const selectedCategoryValue: Option[] = draft.category_id
    ? [
        {
          value: String(draft.category_id),
          label: findLabel(String(draft.category_id)),
        },
      ]
    : [];

  const selectedLevel = draft.level_id ? String(draft.level_id) : '';

  const handleApply = () => {
    applyDraft();
  };

  return (
    <div className="bg-white rounded-[8px]">
      <div className="flex items-center justify-between p-4">
        <span className="font-semibold text-comp-content-primary">Filter</span>

        <div className="flex items-center gap-2">
          <BaseButton
            className="px-5 w-[95px] text-destructive border border-destructive hover:text-destructive hover:border-destructive h-[40px] text-xs font-medium"
            variant="outline"
            onClick={() => {
              resetDraft();
              resetQuery();
            }}
          >
            Reset
          </BaseButton>

          <BaseButton
            className="px-5 w-[95px] h-[40px] text-xs font-medium"
            onClick={handleApply}
          >
            Apply
          </BaseButton>
        </div>
      </div>

      <BaseSeparator />

      <div className="grid grid-cols-1 gap-3 p-4">
        <MultipleSelectorComponent
          onSelectAll={() => {}}
          options={categoryOptions}
          value={selectedCategoryValue}
          onSelect={(opts) => {
            const first = opts?.[0];
            setDraft({
              page: 1,
              category_id: first ? Number(first.value) : undefined,
            });
          }}
          title="Category"
          placeholder="Select Category"
        />

        <InputSelect
          label="Level"
          placeholder="Select Level"
          value={selectedLevel}
          options={levelOptions}
          onChange={(v: string) =>
            setDraft({ page: 1, level_id: v ? Number(v) : undefined })
          }
        />
      </div>
    </div>
  );
};

export default ImageRepositoryTableHeaderFilter;
