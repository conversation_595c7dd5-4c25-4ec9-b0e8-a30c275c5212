"use client";

import { useState } from "react";
import { Control, FieldErrors } from "react-hook-form";
import { AddSectionFormData } from "@/components/organisms/admin/manage-section/add-new-section";
import { BaseInput } from "@/components/atoms/input";
import { BaseButton } from "@/components/atoms/button";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import {
  BaseTable,
  BaseTableBody,
  BaseTableCell,
  BaseTableHead,
  BaseTableHeader,
  BaseTableRow,
} from "@/components/atoms/table";
import { Controller } from "react-hook-form";
import { cn } from "@/lib/utils";
import { Trash2, Plus } from "lucide-react";
import CategorySelector from "../../manage-material/form/category-selector";
import LevelSelector from "../../manage-material/form/level-selector";
import MaterialSelector from "../form/material-selector";
import QuestionBankModal from "./question-bank-modal";
import QuestionTemplateModal from "./question-template-modal";
import { IGetQuestionBankListResponse } from "@/interfaces/admin/manage-test/question-bank/list";
import { IGetQuestionTemplateListResponse } from "@/interfaces/admin/manage-test/question-template/list";

interface SectionInformationProps {
  control: Control<AddSectionFormData>;
  errors: FieldErrors<AddSectionFormData>;
  form: any;
}

const TYPE = [
  {
    value: "video",
    label: "Video",
  },
  {
    value: "audio",
    label: "Audio",
  },
  {
    value: "document",
    label: "PDF",
  },
  {
    value: "quiz",
    label: "Quiz",
  },
  {
    value: "pre-test",
    label: "Pre-Test",
  },
  {
    value: "post-test isian",
    label: "Post-Test Isian",
  },
  {
    value: "post-test pilihan ganda",
    label: "Post-Test Pilihan Ganda",
  },
  {
    value: "assignment",
    label: "Assignment",
  },
];

const SectionInformation = ({
  control,
  errors,
  form,
}: SectionInformationProps) => {
  const [showQuestionBankModal, setShowQuestionBankModal] = useState(false);
  const [showQuestionTemplateModal, setShowQuestionTemplateModal] =
    useState(false);
  const [selectedQuestions, setSelectedQuestions] = useState<
    (IGetQuestionBankListResponse | IGetQuestionTemplateListResponse)[]
  >([]);

  const sectionType = form.watch("sectionType");

  // Transform questions to the format expected by the form
  const transformQuestionsToFormData = (
    questions: (
      | IGetQuestionBankListResponse
      | IGetQuestionTemplateListResponse
    )[]
  ) => {
    return questions.map((q) => {
      if ("question" in q) {
        // Question Bank
        return {
          id: q.id,
          question: q.question || "",
          type: q.type || "",
          source: "bank" as const,
          options: {
            a: q.option_a || "",
            b: q.option_b || "",
            c: q.option_c || "",
            d: q.option_d || "",
          },
          correctAnswer: q.correct_answer || "",
        };
      } else {
        // Question Template
        return {
          id: q.id,
          question: q.question_template_name,
          type: q.type,
          source: "template" as const,
          templateName: q.question_template_name,
        };
      }
    });
  };

  const handleAddQuestionsFromBank = (
    questions: IGetQuestionBankListResponse[]
  ) => {
    const newQuestions = [...selectedQuestions, ...questions];
    setSelectedQuestions(newQuestions);

    // Update form data
    const formData = transformQuestionsToFormData(newQuestions);
    form.setValue("selectedQuestions", formData);
    form.setValue(
      "questionId",
      newQuestions.map((q) => q.id.toString())
    );
    form.setValue("numberOfQuestions", newQuestions.length);
  };

  const handleAddQuestionsFromTemplate = (
    templates: IGetQuestionTemplateListResponse[]
  ) => {
    const newQuestions = [...selectedQuestions, ...templates];
    setSelectedQuestions(newQuestions);

    // Update form data
    const formData = transformQuestionsToFormData(newQuestions);
    form.setValue("selectedQuestions", formData);
    form.setValue(
      "questionId",
      newQuestions.map((q) => q.id.toString())
    );
    form.setValue("numberOfQuestions", newQuestions.length);
  };

  const handleRemoveQuestion = (questionId: number) => {
    const newQuestions = selectedQuestions.filter((q) => q.id !== questionId);
    setSelectedQuestions(newQuestions);

    // Update form data
    const formData = transformQuestionsToFormData(newQuestions);
    form.setValue("selectedQuestions", formData);
    form.setValue(
      "questionId",
      newQuestions.map((q) => q.id.toString())
    );
    form.setValue("numberOfQuestions", newQuestions.length);
  };

  const getExcludedIds = () => {
    return selectedQuestions.map((q) => q.id);
  };

  return (
    <div className="bg-white">
      <h2 className="text-lg font-medium text-gray-900 mb-6">
        Section Information
      </h2>

      <div className="flex flex-col gap-y-3">
        {/* Section Name */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-5 gap-y-3">
          <div>
            <label className="block text-xs font-medium text-comp-content-primary mb-1">
              Section Name
            </label>
            <Controller
              name="sectionName"
              control={control}
              render={({ field }) => (
                <BaseInput
                  {...field}
                  placeholder="Input section name"
                  className={errors.sectionName ? "border-red-500" : ""}
                />
              )}
            />
            {errors.sectionName && (
              <p className="text-red-500 text-sm mt-1">
                {errors.sectionName.message}
              </p>
            )}
          </div>
          {/* Section ID */}
          <div>
            <label className="block text-xs font-medium text-comp-content-primary mb-1">
              Section ID
            </label>
            <Controller
              name="sectionId"
              control={control}
              render={({ field }) => (
                <BaseInput
                  {...field}
                  placeholder="ID"
                  className={errors.sectionId ? "border-red-500" : ""}
                  disabled
                />
              )}
            />
            {errors.sectionId && (
              <p className="text-red-500 text-sm mt-1">
                {errors.sectionId.message}
              </p>
            )}
          </div>
        </div>

        <CategorySelector labelClassName="block text-xs font-medium text-comp-content-primary mb-1" />

        <LevelSelector
          showLevel
          labelClassName="block text-xs font-medium text-comp-content-primary mb-1"
        />

        {/* Section Type */}
        <div>
          <label className="block text-xs font-medium text-comp-content-primary mb-1">
            Section Type
          </label>
          <Controller
            name="sectionType"
            control={control}
            render={({ field }) => {
              const { onChange, ...restField } = field;
              const handleChange = (value: string) => {
                // Reset materialId when section type changes
                form.setValue("materialId", "");
                onChange(value);
              };

              return (
                <BaseSelect
                  value={restField.value}
                  onValueChange={handleChange}
                >
                  <BaseSelectTrigger
                    className={cn(
                      "w-full",
                      errors.sectionType ? "border-red-500" : ""
                    )}
                  >
                    <BaseSelectValue placeholder="Select section type" />
                  </BaseSelectTrigger>
                  <BaseSelectContent>
                    {TYPE.map((type) => (
                      <BaseSelectItem key={type.value} value={type.value}>
                        {type.label}
                      </BaseSelectItem>
                    ))}
                  </BaseSelectContent>
                </BaseSelect>
              );
            }}
          />
          {errors.sectionType && (
            <p className="text-red-500 text-sm mt-1">
              {errors.sectionType.message}
            </p>
          )}
        </div>

        {/* Material Selection, Quiz Settings, or Assignment */}
        <div>
          <Controller
            name="sectionType"
            control={control}
            render={({ field: { value: sectionType } }) => {
              // Quiz/Test types: quiz, pre-test, post-test isian, post-test pilihan ganda
              if (
                [
                  "quiz",
                  "pre-test",
                  "post-test isian",
                  "post-test pilihan ganda",
                ].includes(sectionType)
              ) {
                return (
                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-4">
                      {/* Passing Grade */}
                      <div>
                        <label className="block text-xs font-medium text-comp-content-primary mb-1">
                          Passing Grade
                        </label>
                        <Controller
                          name="passingGrade"
                          control={control}
                          render={({ field }) => (
                            <BaseInput
                              {...field}
                              type="number"
                              placeholder="Input passing grade"
                              onChange={(e) =>
                                field.onChange(Number(e.target.value))
                              }
                              className={
                                errors.passingGrade ? "border-red-500" : ""
                              }
                            />
                          )}
                        />
                        {errors.passingGrade && (
                          <p className="text-red-500 text-sm mt-1">
                            {errors.passingGrade.message}
                          </p>
                        )}
                      </div>

                      {/* Number of Questions */}
                      <div>
                        <label className="block text-xs font-medium text-comp-content-primary mb-1">
                          Number of Questions
                        </label>
                        <Controller
                          name="numberOfQuestions"
                          control={control}
                          render={({ field }) => (
                            <BaseInput
                              {...field}
                              type="number"
                              placeholder="Auto-calculated"
                              value={form.watch("questionId")?.length || 0}
                              disabled
                              className={cn(
                                "bg-gray-100",
                                errors.numberOfQuestions ? "border-red-500" : ""
                              )}
                            />
                          )}
                        />
                        {errors.numberOfQuestions && (
                          <p className="text-red-500 text-sm mt-1">
                            {errors.numberOfQuestions.message}
                          </p>
                        )}
                      </div>

                      {/* with test timer (yes or no) */}
                      <div>
                        <label className="block text-xs font-medium text-comp-content-primary mb-1">
                          With Test Timer
                        </label>
                        <Controller
                          name="withTestTimer"
                          control={control}
                          render={({ field }) => (
                            <BaseSelect
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <BaseSelectTrigger
                                className={cn(
                                  "w-full",
                                  errors.withTestTimer ? "border-red-500" : ""
                                )}
                              >
                                <BaseSelectValue placeholder="Select section type">
                                  {field.value
                                    ? (field.value as string).toUpperCase()
                                    : "Select option"}
                                </BaseSelectValue>
                              </BaseSelectTrigger>
                              <BaseSelectContent>
                                <BaseSelectItem value="yes">YES</BaseSelectItem>
                                <BaseSelectItem value="no">NO</BaseSelectItem>
                              </BaseSelectContent>
                            </BaseSelect>
                          )}
                        />
                        {errors.withTestTimer && (
                          <p className="text-red-500 text-sm mt-1">
                            {errors.withTestTimer.message}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                );
              }

              // Assignment type
              if (sectionType === "assignment") {
                return (
                  <div>
                    <label className="block text-xs font-medium text-comp-content-primary mb-1">
                      Assignment Instruction{" "}
                      <span className="text-red-500">*</span>
                    </label>
                    <Controller
                      name="assignmentInstruction"
                      control={control}
                      render={({ field }) => (
                        <textarea
                          {...field}
                          placeholder="Enter assignment instructions (max 500 characters)"
                          maxLength={500}
                          rows={4}
                          className={cn(
                            "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",
                            errors.assignmentInstruction ? "border-red-500" : ""
                          )}
                        />
                      )}
                    />
                    {errors.assignmentInstruction && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors.assignmentInstruction.message}
                      </p>
                    )}
                    <p className="text-gray-500 text-xs mt-1">
                      {form.watch("assignmentInstruction")?.length || 0}/500
                      characters
                    </p>
                  </div>
                );
              }

              // Material selection for video, audio, document
              if (["video", "audio", "document"].includes(sectionType)) {
                return (
                  <div className="space-y-4">
                    <MaterialSelector
                      sectionType={
                        sectionType as "video" | "audio" | "document"
                      }
                      labelClassName="block text-xs font-medium text-comp-content-primary mb-1"
                      name="materialId"
                    />
                  </div>
                );
              }

              // Default case (shouldn't happen with proper validation)
              return <div></div>;
            }}
          />
        </div>
      </div>

      {/* Question Bank Modal */}
      <QuestionBankModal
        isOpen={showQuestionBankModal}
        onClose={() => setShowQuestionBankModal(false)}
        onAddQuestions={handleAddQuestionsFromBank}
        excludeIds={getExcludedIds()}
      />

      {/* Question Template Modal */}
      <QuestionTemplateModal
        isOpen={showQuestionTemplateModal}
        onClose={() => setShowQuestionTemplateModal(false)}
        onAddQuestions={handleAddQuestionsFromTemplate}
      />
    </div>
  );
};

export default SectionInformation;
