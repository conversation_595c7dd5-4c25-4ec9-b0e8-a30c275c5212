import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  apiCreateQuestionTemplate,
  apiDeleteQuestionTemplate,
  apiUpdateQuestionTemplate,
} from '@/services/api/admin/manage-test/question-template';
import {
  IUpdateQuestionTemplateBody,
  IUpdateQuestionTemplateParams,
} from '@/interfaces/admin/manage-test/question-template/update';
import { ICreateQuestionTemplateBody } from '@/interfaces/admin/manage-test/question-template/new';
import toast from 'react-hot-toast';

export const useCreateQuestionTemplateMutation = () => {
  const qc = useQueryClient();

  return useMutation({
    mutationKey: ['question-template', 'create'],
    mutationFn: (body: ICreateQuestionTemplateBody) =>
      apiCreateQuestionTemplate(body),
    onSuccess: () => {
      toast.success('Question Template created');
      qc.invalidateQueries({ queryKey: ['question-template', 'list'] });
    },
    onError: () => {
      toast.error('Failed to create Question Template');
    },
  });
};

export const useUpdateQuestionTemplateMutation = () => {
  const qc = useQueryClient();

  return useMutation({
    mutationKey: ['question-template', 'update'],
    mutationFn: ({
      params,
      body,
    }: {
      params: IUpdateQuestionTemplateParams;
      body: IUpdateQuestionTemplateBody;
    }) => apiUpdateQuestionTemplate(params, body),
    onSuccess: (_data, variables) => {
      toast.success('Question Template updated');
      qc.invalidateQueries({ queryKey: ['question-template', 'list'] });
      if (variables?.params?.id) {
        qc.invalidateQueries({
          queryKey: ['question-template', 'detail', variables.params.id],
        });
      }
    },
    onError: () => {
      toast.error('Failed to update Question Template');
    },
  });
};

export const useDeleteQuestionTemplateMutation = () => {
  const qc = useQueryClient();

  return useMutation({
    mutationKey: ['question-template', 'delete'],
    mutationFn: ({ id }: { id: number }) => apiDeleteQuestionTemplate({ id }),
    onSuccess: (_data, variables) => {
      toast.success('Question Template deleted');
      qc.invalidateQueries({ queryKey: ['question-template', 'list'] });
      if (variables?.id) {
        qc.invalidateQueries({
          queryKey: ['question-template', 'detail', variables.id],
        });
      }
    },
    onError: () => {
      toast.error('Failed to delete Question Template');
    },
  });
};
