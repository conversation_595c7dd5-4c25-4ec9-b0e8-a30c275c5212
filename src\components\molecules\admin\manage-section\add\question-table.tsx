"use client";

import React from "react";
import { IGetQuestionBankListResponse } from "@/interfaces/admin/manage-test/question-bank/list";
import {
  BaseTable,
  BaseTableBody,
  BaseTableCell,
  BaseTableHead,
  BaseTableHeader,
  BaseTableRow,
} from "@/components/atoms/table";
import { OrangeCheckbox } from "@/components/atoms/checkbox/orange-checkbox";
import PillDropdown from "@/components/atoms/dropdown/pill-dropdown";

interface QuestionTableProps {
  questions: IGetQuestionBankListResponse[];
  selectedQuestions: Record<number, number[]>;
  currentPage: number;
  withCheckbox?: boolean;
  isAllSelected: boolean;
  onSelectAll: (checked: boolean) => void;
  onSelectQuestion: (questionId: number, checked: boolean) => void;
}

export const QuestionTable: React.FC<QuestionTableProps> = ({
  questions,
  selectedQuestions,
  currentPage,
  withCheckbox = false,
  isAllSelected,
  onSelectAll,
  onSelectQuestion,
}) => {
  return (
    <BaseTable className="min-w-full">
      <BaseTableHeader>
        <BaseTableRow>
          {withCheckbox && (
            <BaseTableHead className="w-12">
              <OrangeCheckbox
                checked={isAllSelected}
                onCheckedChange={onSelectAll}
              />
            </BaseTableHead>
          )}
          <BaseTableHead className="w-24">Question ID</BaseTableHead>
          <BaseTableHead className="w-40">Category</BaseTableHead>
          <BaseTableHead className="w-40">Level</BaseTableHead>
          <BaseTableHead className="w-32">Question Type</BaseTableHead>
          <BaseTableHead className="w-64">Question</BaseTableHead>
          <BaseTableHead className="w-32">Option A</BaseTableHead>
          <BaseTableHead className="w-32">Option B</BaseTableHead>
          <BaseTableHead className="w-32">Option C</BaseTableHead>
          <BaseTableHead className="w-32">Option D</BaseTableHead>
          <BaseTableHead className="w-24">Key Answer</BaseTableHead>
          <BaseTableHead className="w-24">With Image?</BaseTableHead>
          <BaseTableHead className="w-40">Associated Section</BaseTableHead>
          <BaseTableHead className="w-32">Created At</BaseTableHead>
          <BaseTableHead className="w-32">Created By</BaseTableHead>
          <BaseTableHead className="w-32">Last Updated</BaseTableHead>
          <BaseTableHead className="w-32">Updated By</BaseTableHead>
        </BaseTableRow>
      </BaseTableHeader>
      <BaseTableBody>
        {questions.map((question) => (
          <BaseTableRow key={question.id}>
            {withCheckbox && (
              <BaseTableCell>
                <OrangeCheckbox
                  checked={selectedQuestions[currentPage]?.includes(
                    question.id
                  )}
                  onCheckedChange={(checked) =>
                    onSelectQuestion(question.id, checked as boolean)
                  }
                />
              </BaseTableCell>
            )}

            <BaseTableCell className="font-mono text-sm">
              {question.id}
            </BaseTableCell>
            <BaseTableCell>
              <PillDropdown
                selected={`${question.categories?.length} Category`}
                options={question.categories?.map((category) => {
                  return {
                    value: category.id?.toString() || "",
                    label: category.name || "",
                  };
                })}
              />
            </BaseTableCell>
            <BaseTableCell>
              <PillDropdown
                selected={`${question.levels?.length} Level`}
                options={question.levels?.map((level) => {
                  return {
                    value: level.id?.toString() || "",
                    label: level.level?.toString() || "",
                  };
                })}
              />
            </BaseTableCell>
            <BaseTableCell>
              <span className="text-sm">{question.type || "-"}</span>
            </BaseTableCell>
            <BaseTableCell>
              <div className="max-w-xs text-wrap w-full min-w-[16rem]">
                <p className="text-sm line-clamp-2">
                  {question.question || "-"}
                </p>
              </div>
            </BaseTableCell>
            <BaseTableCell>
              <div className="max-w-xs text-wrap w-full">
                <p className="text-sm line-clamp-2">
                  {question.option_a || "-"}
                </p>
              </div>
            </BaseTableCell>
            <BaseTableCell>
              <div className="max-w-xs text-wrap w-full">
                <p className="text-sm line-clamp-2">
                  {question.option_b || "-"}
                </p>
              </div>
            </BaseTableCell>
            <BaseTableCell>
              <div className="max-w-xs text-wrap w-full">
                <p className="text-sm line-clamp-2">
                  {question.option_c || "-"}
                </p>
              </div>
            </BaseTableCell>
            <BaseTableCell>
              <div className="max-w-xs text-wrap w-full">
                <p className="text-sm line-clamp-2">
                  {question.option_d || "-"}
                </p>
              </div>
            </BaseTableCell>
            <BaseTableCell>
              <span className="text-sm">{question.correct_answer || "-"}</span>
            </BaseTableCell>
            <BaseTableCell>
              <span className="text-sm">
                {question.image_id ? "YES" : "NO"}
              </span>
            </BaseTableCell>
            <BaseTableCell>
              <PillDropdown
                selected={`${question.associated.length} Sections`}
                options={question.associated.map((section) => ({
                  value: section.section_id?.toString() || "",
                  label: section.section_name || "",
                }))}
              />
            </BaseTableCell>
            <BaseTableCell className="text-sm">
              {question.created_at || "-"}
            </BaseTableCell>
            <BaseTableCell className="text-sm">
              {question.created_by || "-"}
            </BaseTableCell>
            <BaseTableCell className="text-sm">
              {question.last_updated || "-"}
            </BaseTableCell>
            <BaseTableCell className="text-sm">
              {question.updated_by || "-"}
            </BaseTableCell>
          </BaseTableRow>
        ))}
      </BaseTableBody>
    </BaseTable>
  );
};
