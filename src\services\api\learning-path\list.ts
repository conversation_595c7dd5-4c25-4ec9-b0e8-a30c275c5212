"use server";

import {
  IGetLearningCodeListQuery,
  IGetLearningCodeListResponse,
  IGetLearningLevelListQuery,
  IGetLearningLevelListResponse,
} from "@/interfaces/admin/manage-learning-path/list";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetLearningCodeList = async (
  query: IGetLearningCodeListQuery
) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetLearningCodeListResponse[]>
    >("/cms/admin/learning/code/list", {
      params: query,
    });

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};

export const apiGetLearningLevelList = async (
  query: IGetLearningLevelListQuery
) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetLearningLevelListResponse[]>
    >("/cms/admin/learning/level/list", {
      params: query,
    });

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
