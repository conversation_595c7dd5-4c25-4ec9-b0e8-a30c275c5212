import { IGetImageRepositoryListResponse } from '@/interfaces/admin/manage-test/image-repository/list';
import { create } from 'zustand';

interface IImageRepositoryModal {
  openedImageRepository: IGetImageRepositoryListResponse | null;
  setOpenedImageRepository: (
    data: IGetImageRepositoryListResponse | null
  ) => void;
  openAddModal: boolean;
  setOpenAddModal: (open: boolean) => void;
  openDeleteModal: boolean;
  setOpenDeleteModal: (open: boolean) => void;
}

export const useImageRepositoryModal = create<IImageRepositoryModal>()(
  (set) => ({
    openedImageRepository: null,
    setOpenedImageRepository: (data: IGetImageRepositoryListResponse | null) =>
      set({ openedImageRepository: data }),
    openAddModal: false,
    setOpenAddModal: (open: boolean) => set({ openAddModal: open }),
    openDeleteModal: false,
    setOpenDeleteModal: (open: boolean) => set({ openDeleteModal: open }),
  })
);
