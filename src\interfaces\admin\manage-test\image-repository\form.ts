import * as yup from 'yup';

export const createImageRepositoryBodySchema = yup.object({
  id: yup.string().optional(),
  image: yup.mixed().required(),
  image_name: yup.string().required(),
  level: yup
    .array(
      yup.object({
        label: yup.string(),
        value: yup.string(),
      })
    )
    .required()
    .min(1, 'Level is required'),
  category: yup
    .array(
      yup.object({
        label: yup.string(),
        value: yup.string(),
      })
    )
    .required()
    .min(1, 'Category is required'),
});

export interface ICreateImageRepositoryBody
  extends yup.InferType<typeof createImageRepositoryBodySchema> {}
