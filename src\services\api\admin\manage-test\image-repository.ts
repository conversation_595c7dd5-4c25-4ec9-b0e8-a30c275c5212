'use server';

import { api } from '@/services/satellite';
import { handleAxiosError } from '@/utils/common/axios';
import { IGlobalResponseDto } from '@/interfaces/global/response';
import {
  IGetImageRepositoryListQuery,
  IGetImageRepositoryListResponse,
} from '@/interfaces/admin/manage-test/image-repository/list';
import {
  IGetImageRepositoryDetailParams,
  IImageRepositoryDetailResponse,
} from '@/interfaces/admin/manage-test/image-repository/detail';
import { ICreateImageRepositoryBodyData } from '@/interfaces/admin/manage-test/image-repository/new';
import {
  IUpdateImageRepositoryBodyData,
  IUpdateImageRepositoryParams,
} from '@/interfaces/admin/manage-test/image-repository/update';

const BASE = '/cms/admin/learning/img-repository';

export const apiGetImageRepositoryList = async (
  query?: IGetImageRepositoryListQuery
) => {
  try {
    const res = await api.get<
      IGlobalResponseDto<IGetImageRepositoryListResponse[]>
    >(`${BASE}/list`, { params: query });
    return res.data;
  } catch (err) {
    throw handleAxiosError(err);
  }
};

export const apiGetImageRepositoryDetail = async (
  params: IGetImageRepositoryDetailParams
) => {
  try {
    const res = await api.get<
      IGlobalResponseDto<IImageRepositoryDetailResponse>
    >(`${BASE}/detail/${params.id}`);
    return res.data;
  } catch (err) {
    throw handleAxiosError(err);
  }
};

export const apiCreateImageRepository = async (
  body: ICreateImageRepositoryBodyData,
  file?: File | null
) => {
  try {
    const formData = new FormData();
    formData.append('data', JSON.stringify(body));
    if (file) formData.append('file', file);

    const res = await api.post<IGlobalResponseDto<null>>(
      `${BASE}/insert`,
      formData,
      { headers: { 'Content-Type': 'multipart/form-data' } }
    );
    return res.data;
  } catch (err) {
    throw handleAxiosError(err);
  }
};

export const apiUpdateImageRepository = async (
  params: IUpdateImageRepositoryParams,
  body: IUpdateImageRepositoryBodyData,
  file?: File | null
) => {
  try {
    const formData = new FormData();
    formData.append('data', JSON.stringify(body));
    if (file) formData.append('file', file);

    const res = await api.post<IGlobalResponseDto<null>>(
      `${BASE}/update/${params.id}`,
      formData,
      { headers: { 'Content-Type': 'multipart/form-data' } }
    );
    return res.data;
  } catch (err) {
    throw handleAxiosError(err);
  }
};

export const apiDeleteImageRepository = async (
  params: IUpdateImageRepositoryParams
) => {
  try {
    const res = await api.post<IGlobalResponseDto<null>>(
      `${BASE}/delete/${params.id}`
    );
    return res.data;
  } catch (err) {
    throw handleAxiosError(err);
  }
};
