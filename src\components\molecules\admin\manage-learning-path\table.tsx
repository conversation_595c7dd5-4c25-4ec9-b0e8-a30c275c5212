"use client";

import React from "react";
import { DataTable } from "../../global/table";
import ManageLearningPathTableHeader from "./table-header";
import {
  getColumnsManageLearningCode,
  getColumnsManageLearningLevel,
} from "./column";
import ManageLearningPathDeleteConfirmationModal from "./delete-confirmation-modal";
import { useManageLearningPathTabStore } from "@/store/admin/manage-learning-path/tab";
import {
  useGetLearningCodeListQuery,
  useGetLearningLevelListQuery,
} from "@/services/query/learning-path/list";
import { useManageLearningPathFilterStore } from "@/store/admin/manage-learning-path/filter";
import { useShallow } from "zustand/react/shallow";

import { useManageLearningPathModalStore } from "@/store/admin/manage-learning-path/modal";
import {
  IGetLearningCodeListResponse,
  IGetLearningLevelListResponse,
} from "@/interfaces/admin/manage-learning-path/list";
import ManageLearningPathSetActiveConfirmationModal from "./set-active-confirmation-modal";
import { useRouter } from "next/navigation";

const ManageLearningPathTable = () => {
  const activeTab = useManageLearningPathTabStore((state) => state.activeTab);
  const {
    learningCodeQuery,
    learningLevelQuery,
    setLearningCodeQuery,
    setLearningLevelQuery,
    setCurrentData,
  } = useManageLearningPathFilterStore(
    useShallow(
      ({
        learningCodeQuery,
        learningLevelQuery,
        setLearningCodeQuery,
        setLearningLevelQuery,
        setCurrentData,
      }) => ({
        learningCodeQuery,
        learningLevelQuery,
        setLearningCodeQuery,
        setLearningLevelQuery,
        setCurrentData,
      })
    )
  );

  const {
    setOpenDeleteModal,
    setOpenAddLearningLevelModal,
    setOpenSetActiveModal,
  } = useManageLearningPathModalStore(
    useShallow(
      ({
        setOpenDeleteModal,
        setOpenAddLearningLevelModal,
        setOpenSetActiveModal,
      }) => ({
        setOpenDeleteModal,
        setOpenAddLearningLevelModal,
        setOpenSetActiveModal,
      })
    )
  );

  const learningCode = useGetLearningCodeListQuery(
    learningCodeQuery,
    activeTab === "learning-code"
  );

  const learningLevel = useGetLearningLevelListQuery(
    learningLevelQuery,
    activeTab === "learning-level"
  );

  const router = useRouter();

  const learningCodeColumns = React.useMemo(
    () =>
      getColumnsManageLearningCode({
        onEdit: (data) => {
          const d = data as IGetLearningCodeListResponse;
          router.push(
            `/admin/manage-learning-path/learning-code/add?id=${d.ID}`
          );
        },
        onDelete: (data) => {
          const d = data as IGetLearningCodeListResponse;

          setCurrentData(d.ID);
          setOpenDeleteModal(true);
        },
        onStatusChange: (data) => {
          const d = data as IGetLearningCodeListResponse;

          setCurrentData(d.ID);
          setOpenSetActiveModal(true);
        },
      }),
    [learningCode.data]
  );

  const learningLevelColumns = React.useMemo(
    () =>
      getColumnsManageLearningLevel({
        onEdit: (data) => {
          const d = data as IGetLearningLevelListResponse;

          setCurrentData(d.id);
          setOpenAddLearningLevelModal(true);
        },
        onDelete: (data) => {
          const d = data as IGetLearningLevelListResponse;

          setCurrentData(d.id);
          setOpenDeleteModal(true);
        },
        onStatusChange: (data) => {
          const d = data as IGetLearningLevelListResponse;

          setCurrentData(d.id);
          setOpenSetActiveModal(true);
        },
      }),
    [learningLevel.data]
  );

  const handlePageChange = (
    activeTab: "learning-code" | "learning-level",
    page: number
  ) => {
    if (activeTab === "learning-code") {
      setLearningCodeQuery({ ...learningCodeQuery, page });
    } else {
      setLearningLevelQuery({ ...learningLevelQuery, page });
    }
  };

  return (
    <div className="flex flex-col gap-4 h-full">
      <ManageLearningPathTableHeader />
      <ManageLearningPathDeleteConfirmationModal />
      <ManageLearningPathSetActiveConfirmationModal />

      {activeTab === "learning-code" ? (
        <DataTable
          columns={learningCodeColumns}
          data={learningCode.data?.data ?? []}
          pagination={learningCode.data?.pagination}
          onPageChange={(page) => handlePageChange("learning-code", page)}
        />
      ) : (
        <DataTable
          columns={learningLevelColumns}
          data={learningLevel.data?.data ?? []}
          pagination={learningLevel.data?.pagination}
          onPageChange={(page) => handlePageChange("learning-level", page)}
        />
      )}
    </div>
  );
};

export default ManageLearningPathTable;
