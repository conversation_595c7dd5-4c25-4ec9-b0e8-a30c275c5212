"use client";

import React, { useState } from "react";
import { DataTable } from "../../global/table";
import { getCertificateColumns } from "./column";
import ManageCertificateTableHeader from "./table-header";
import { useRouter } from "next/navigation";
import { useGetListCertificatesQuery } from "@/services/query/admin/manage-certificate";
import { useManageCertificateQueryStore } from "@/store/admin/manage-certificate/query";
import { useShallow } from "zustand/react/shallow";
import { useUpdateCertificateStatusMutation } from "@/services/mutation/admin/manage-certificate";
import { IGetListCertificateResponse } from "@/interfaces/admin/manage-certificate/list";
import ConfirmationModal from "../../modal/confirmation-modal";
import { notifyHotError, notifyHotSuccess } from "../../toast/hot-toast";
import { encryptAES } from "@/utils/common/encryption";

const ManageCertificateTable = () => {
  const router = useRouter();
  const [selectedCertificate, setSelectedCertificate] =
    useState<IGetListCertificateResponse | null>(null);
  const [isModalUpdateStatusOpen, setIsModalUpdateStatusOpen] = useState(false);

  const { certificateQuery, setCertificateQuery } =
    useManageCertificateQueryStore(
      useShallow((state) => ({
        certificateQuery: state.certificateQuery,
        setCertificateQuery: state.setCertificateQuery,
      }))
    );

  const moduleType = Array.isArray(certificateQuery.module_type)
    ? certificateQuery.module_type.join(",")
    : certificateQuery.module_type;

  const { data, isLoading, error } = useGetListCertificatesQuery({
    ...certificateQuery,
    module_type: moduleType,
  });

  const { mutateAsync: updateCertificateStatus } =
    useUpdateCertificateStatusMutation();

  const columns = React.useMemo(
    () =>
      getCertificateColumns({
        onView: (certificate) => {
          if (!certificate.id) return;
          console.log("decryptAES", encryptAES(certificate.id.toString()));
          router.push(
            `/admin/manage-certificate/certificate/${encryptAES(
              certificate.id.toString()
            )}`
          );
        },
        onToggleActive: (certificate) => {
          setSelectedCertificate(certificate);
          setIsModalUpdateStatusOpen(true);
        },
      }),
    []
  );

  const handlePageChange = (page: number) => {
    setCertificateQuery({ page });
  };

  const handleUpdateStatusConfirm = async () => {
    if (!selectedCertificate?.id) return;
    try {
      const res = await updateCertificateStatus({
        id: selectedCertificate.id,
        body: {
          status:
            selectedCertificate.status === "active" ? "expired" : "active",
        },
      });
      setIsModalUpdateStatusOpen(false);
      setSelectedCertificate(null);
      notifyHotSuccess(res.message || "Status updated successfully");
    } catch (error: any) {
      notifyHotError(error.message || "Failed to update status");
    }
  };

  if (error) {
    return (
      <div className="flex flex-col gap-4 h-full">
        <ManageCertificateTableHeader />
        <div className="flex items-center justify-center h-64">
          <p className="text-red-500">
            Error loading certificates: {error.message}
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4 h-full">
        <ManageCertificateTableHeader />
        <div className="flex items-center justify-center h-64">
          <p className="text-gray-500">Loading certificates...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 h-full">
      <ConfirmationModal
        isOpen={isModalUpdateStatusOpen}
        onOpenChange={(open) => setIsModalUpdateStatusOpen(open)}
        onConfirm={handleUpdateStatusConfirm}
        title="Update Status?"
        description={`Are you sure you want to ${
          selectedCertificate?.status === "active" ? "deactivate" : "activate"
        } ${selectedCertificate?.name}?`}
      />
      <ManageCertificateTableHeader />
      <DataTable
        columns={columns}
        data={data?.data || []}
        pagination={
          data?.pagination || {
            current_page: 1,
            total_page: 1,
            total_data: 0,
            next: null,
            prev: null,
          }
        }
        onPageChange={handlePageChange}
      />
    </div>
  );
};

export default ManageCertificateTable;
