// Section update interfaces
export interface IUpdateSectionParams {
  id: number;
}

export interface IUpdateSectionBody {
  section_name?: string;
  section_type?: "video" | "test" | "document" | "audio";
  material_repository_id?: number;
  material_link?: string;
  passing_grade?: number;
  number_of_question?: number;
  with_test_timer?: boolean;
  duration?: number;
  assignment_instruction?: string;
  question_id?: number[];
  competency_id?: number[];
  category_id?: number[];
  level_id?: number[];
}

export interface IUpdateSectionResponse {
  message: string;
}
