'use client';

import { BaseInput } from '@/components/atoms/input';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/atoms/select';
import { BaseSeparator } from '@/components/atoms/separator';
import { Search } from 'lucide-react';
import React from 'react';
import { useShallow } from 'zustand/react/shallow';
import lodash from 'lodash';
import { useImageRepositoryFilterStore } from '@/store/admin/manage-test/image-repository/filter';
import { TIRSearchBy } from '@/interfaces/admin/manage-test/image-repository/list';

const SEARCH_BY_OPTIONS: { label: string; value: TIRSearchBy }[] = [
  { label: 'Image ID', value: 'image_id' },
  { label: 'Image Name', value: 'image_name' },
  { label: 'File Format', value: 'file_format' },
  { label: 'Question (Associated)', value: 'question' },
  { label: 'Created By', value: 'created_by' },
  { label: 'Updated By', value: 'updated_by' },
];

const ImageRepositoryTableHeaderSearch = () => {
  const { draft, setSearch, setSearchBy } = useImageRepositoryFilterStore(
    useShallow(({ draft, setSearch, setSearchBy }) => ({
      draft,
      setSearch,
      setSearchBy,
    }))
  );

  return (
    <div className="text-[#3C3C3C] font-semibold rounded-lg flex items-center gap-1 relative w-[40%] bg-white px-3">
      <div>
        <BaseSelect
          value={draft.search_by ?? 'image_name'}
          onValueChange={(v) => setSearchBy(v as TIRSearchBy)}
        >
          <BaseSelectTrigger className="w-44 border-none px-1">
            <BaseSelectValue placeholder="Search By" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {SEARCH_BY_OPTIONS.map((opt) => (
              <BaseSelectItem
                key={opt.value}
                value={opt.value}
              >
                {opt.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>

      <BaseSeparator orientation="vertical" />

      <BaseInput
        className="border-none h-12 focus-visible:border-none focus-visible:ring-0"
        onChange={lodash.debounce((e: React.ChangeEvent<HTMLInputElement>) => {
          const raw = e?.target?.value;
          setSearch(raw);
        }, 800)}
      />

      <Search size={24} />

      <BaseSeparator
        orientation="vertical"
        className="bg-red-500 w-1"
      />
    </div>
  );
};

export default ImageRepositoryTableHeaderSearch;
