import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseInput } from "@/components/atoms/input";
import { BaseLabel } from "@/components/atoms/label";
import MultipleSelector from "@/components/atoms/multiselect";
import { BaseSeparator } from "@/components/atoms/separator";
import { BaseTextarea } from "@/components/atoms/textarea";
import { DialogClose } from "@/components/ui/dialog";

import {
  createFaqBodySchema,
  ICreateFaqBody,
  ICreateFaqPayload,
} from "@/interfaces/admin/manage-faq/new";
import {
  useInsertFaqMutation,
  useUpdateFaqMutation,
} from "@/services/mutation/admin/manage-faq";
import {
  useDetailFaqQuery,
  useGetListTagQuery,
} from "@/services/query/admin/manage-faq";
import { useManageFaqModal } from "@/store/admin/manage-faq/modal";
import { yupResolver } from "@hookform/resolvers/yup";
import { Image, Upload } from "lucide-react";
import React, { useEffect, useState } from "react";
import {
  useForm,
  useFormContext,
  Path,
  FormProvider,
  Controller,
} from "react-hook-form";
import toast from "react-hot-toast";
import { useShallow } from "zustand/react/shallow";
import { cn } from "@/lib/utils";

const ManageFaqNewModal = () => {
  const { openAddFaq, setOpenAddFaq, openedFaqId, setOpenedFaqId } =
    useManageFaqModal(
      useShallow(
        ({ openAddFaq, setOpenAddFaq, openedFaqId, setOpenedFaqId }) => ({
          openAddFaq,
          setOpenAddFaq,
          openedFaqId,
          setOpenedFaqId,
        })
      )
    );

  const { data, isPending } = useGetListTagQuery({});
  const { mutateAsync: createFaq, isPending: createFaqPending } =
    useInsertFaqMutation();
  const { mutateAsync: updateFaq, isPending: updateFaqPending } =
    useUpdateFaqMutation();
  const { data: detailFaq } = useDetailFaqQuery(openedFaqId);

  const tagOptions =
    data?.data?.map((item) => ({
      value: item.tag_id,
      label: item.tag_name,
    })) ?? [];

  const initialData = {
    question: "",
    answer: "",
    tag_id: [],
    image: "",
  };

  const detailData = {
    question: detailFaq?.data?.question ?? "",
    answer: detailFaq?.data?.answer ?? "",
    tag_id:
      detailFaq?.data?.tag.map((item) => ({
        value: item.tag_id,
        label: item.tag_name,
      })) ?? [],
    image: "",
  };

  const form = useForm({
    resolver: yupResolver(createFaqBodySchema),
    defaultValues: openedFaqId ? detailData : initialData,
  });

  const handleInsertFaq = async (formData: FormData) => {
    try {
      await createFaq(formData);
      setOpenAddFaq(false);
      toast.success("FAQ added successfully");
    } catch (error) {
      toast.error("Failed to add FAQ");
    }
  };

  const handleUpdateFaq = async (formData: FormData, id: string) => {
    try {
      await updateFaq({ formData, id });
      setOpenAddFaq(false);
      toast.success("FAQ updated successfully");
    } catch (error) {
      toast.error("Failed to update FAQ");
    }
  };

  const onSubmit = async (data: ICreateFaqBody) => {
    try {
      const image = data?.image as FileList;
      const imageName = image[0].name;
      const payload: ICreateFaqPayload = {
        question: data.question,
        answer: data.answer,
        tags: data.tag_id.map((item) => item.value),
        imgfaq: imageName,
      };
      const formData = new FormData();
      formData.append("data", JSON.stringify(payload));
      formData.append("file", image[0]);

      if (openedFaqId) {
        await handleUpdateFaq(formData, openedFaqId);
      } else {
        await handleInsertFaq(formData);
      }
    } catch (error) {
      toast.error("Failed to add or update FAQ");
    }
  };

  useEffect(() => {
    if (openedFaqId && detailFaq) {
      const newData = {
        question: detailFaq?.data?.question ?? "",
        answer: detailFaq?.data?.answer ?? "",
        tag_id: detailFaq?.data?.tag.map((item) => ({
          value: item.tag_id,
          label: item.tag_name,
        })),
        image: detailFaq?.data?.imgfaq ?? "",
      };
      form.reset(newData);
    }
  }, [openedFaqId, detailFaq]);

  return (
    <BaseDialog
      open={openAddFaq}
      onOpenChange={(open) => {
        setOpenAddFaq(open);
        if (!open) {
          form.reset();
          setOpenedFaqId("");
        }
      }}
    >
      <BaseDialogContent>
        <BaseDialogHeader>
          <BaseDialogTitle className="flex flex-col gap-4">
            <span>{openedFaqId ? "Edit" : "Add New"} FAQ</span>
            <BaseSeparator />
          </BaseDialogTitle>
        </BaseDialogHeader>
        <FormProvider {...form}>
          <form
            className="flex flex-col gap-4"
            onSubmit={form.handleSubmit(onSubmit)}
          >
            <InputString
              label="Question"
              id="question"
              placeholder="Place Your Text"
              required
            />
            <InputString
              label="Answer"
              id="answer"
              placeholder="Place Your Text"
              required
            />
            <InputFile id="image" label="Image" />
            <Controller
              name="tag_id"
              control={form.control}
              render={({ field }) => (
                <MultipleSelector
                  value={field.value}
                  isPending={isPending}
                  options={tagOptions}
                  onChange={field.onChange}
                  placeholder="Select Tag"
                  loadingIndicator={
                    <p className="py-2 text-center text-sm leading-10 text-muted-foreground">
                      loading...
                    </p>
                  }
                  emptyIndicator={
                    <p className="w-full text-center text-lg leading-10 text-muted-foreground">
                      no results found.
                    </p>
                  }
                />
              )}
            />

            <BaseSeparator className="mt-1 -mb-2" />
            <div className="flex justify-end gap-3 -mb-3">
              <DialogClose asChild>
                <BaseButton
                  className="h-11 w-32"
                  variant={"outline"}
                  disabled={createFaqPending || updateFaqPending}
                >
                  Cancel
                </BaseButton>
              </DialogClose>
              <BaseButton
                className="h-11 w-32"
                type="submit"
                disabled={createFaqPending || updateFaqPending}
              >
                {openedFaqId ? "Update" : "Add"} FAQ
              </BaseButton>
            </div>
          </form>
        </FormProvider>
      </BaseDialogContent>
    </BaseDialog>
  );
};

const InputString = <T extends ICreateFaqBody>({
  label,
  id,
  placeholder,
  optional = false,
  readonly = false,
  required = false,
}: {
  label: string;
  id: keyof T;
  placeholder?: string;
  optional?: boolean;
  readonly?: boolean;
  required?: boolean;
}) => {
  const form = useFormContext<T>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id as string}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
        {required && <span className="text-xs text-red-500 -ml-1">*</span>}
      </BaseLabel>
      <BaseTextarea
        id={id as string}
        placeholder={placeholder}
        {...form.register(id as Path<T>)}
        className="disabled:bg-gray-100 resize-none"
        readOnly={readonly}
        disabled={readonly}
        maxLength={100}
      />
    </div>
  );
};

const InputFile = ({
  id,
  label,
  placeholder,
  optional,
}: {
  label: string;
  id: keyof ICreateFaqBody;
  placeholder?: string;
  optional?: boolean;
}) => {
  const form = useFormContext<ICreateFaqBody>();
  const [image, setImage] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);

  const validImageTypes = [
    "image/png",
    "image/jpeg",
    "image/jpg",
    "image/webp",
  ];
  const maxFileSize = 5 * 1024 * 1024; // 5MB

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    if (!file) return;

    // Reset previous errors
    setError(null);

    // Check file type
    if (!validImageTypes.includes(file.type)) {
      setError("Please upload a valid image file (PNG, JPG, JPEG, or WebP)");
      return;
    }

    // Check file size (5MB max)
    if (file.size > maxFileSize) {
      setError("File size should not exceed 5MB");
      return;
    }

    setImage(file);
  };

  const fileSize = image ? (image.size / 1024).toFixed(2) : "";

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel
        className={cn(
          "w-full py-1 rounded-md border border-dashed border-gray-300 flex justify-center items-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 text-sm",
          error && "border-red-500"
        )}
        htmlFor={id}
      >
        <Upload
          size={12}
          className={cn("text-gray-700", error && "text-red-500")}
        />
        {image ? "Change Image" : "Upload Image"}
      </BaseLabel>
      <BaseInput
        id={id}
        placeholder={placeholder}
        {...form.register(id as Path<ICreateFaqBody>, {
          validate: (value: any) => {
            if (value && value instanceof FileList && value.length > 0) {
              const file = value[0];
              if (!validImageTypes.includes(file.type)) {
                return "Invalid file type. Please upload an image (PNG, JPG, JPEG, or WebP)";
              }
              if (file.size > maxFileSize) {
                return "File size should not exceed 5MB";
              }
            }
            return true;
          },
        })}
        type="file"
        className="sr-only"
        accept="image/png, image/jpeg, image/jpg, image/webp"
        onChange={handleFileChange}
      />

      {error && <p className="text-xs text-red-500 mt-1">{error}</p>}

      {image && !error && (
        <div className="flex gap-2 mt-2 text-gray-600 text-xs items-center">
          <Image className="text-gray-600" />
          <div className="flex flex-col gap-0">
            <span>{image.name}</span>
            <span className="text-gray-400">{fileSize} KB</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ManageFaqNewModal;
