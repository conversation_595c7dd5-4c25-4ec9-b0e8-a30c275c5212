import { Pool, PoolClient } from 'pg';
import { sanitizeForLogging, extractRouteName } from './waf-detector';

// Database connection pool
let pool: Pool | null = null;

function getPool(): Pool {
  if (!pool) {
    pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });
  }
  return pool;
}

async function executeQuery<T = any>(
  query: string,
  params: any[] = []
): Promise<T[]> {
  const client: PoolClient = await getPool().connect();
  
  try {
    // Set the schema search path
    await client.query('SET search_path TO dev, public;');
    const result = await client.query(query, params);
    return result.rows;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  } finally {
    client.release();
  }
}

export interface WafLogEntry {
  routeName: string;
  method: string;
  headers: Record<string, any>;
  body: any;
  response: string;
}

/**
 * Logs WAF blocked request to the database
 */
export async function logWafBlock(
  method: string,
  url: string,
  baseURL: string,
  headers: Record<string, any>,
  requestBody: any,
  responseData: string,
  supportId?: string,
  detectionMethod?: string
): Promise<void> {
  try {
    
    // Prepare log entry
    const routeName = extractRouteName(url, baseURL);
    const sanitizedHeaders = sanitizeForLogging(headers);
    const sanitizedBody = sanitizeForLogging(requestBody);
    
    // Convert body to string if it's an object
    let bodyString = '';
    if (sanitizedBody) {
      if (typeof sanitizedBody === 'string') {
        bodyString = sanitizedBody;
      } else {
        bodyString = JSON.stringify(sanitizedBody);
      }
    }
    
    // Insert into database using raw SQL
    const insertQuery = `
      INSERT INTO waf_logs (route_name, method, headers, body, response, created_at)
      VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
    `;
    
    const params = [
      routeName,
      method.toUpperCase(),
      JSON.stringify(sanitizedHeaders),
      bodyString,
      responseData
    ];
    
    await executeQuery(insertQuery, params);
    
    // Also log to console for immediate visibility
    console.error('🚫 WAF BLOCK LOGGED TO DATABASE:', {
      timestamp: new Date().toISOString(),
      route: routeName,
      method: method.toUpperCase(),
      responseLength: responseData.length,
      supportId: supportId || 'N/A',
      detectionMethod: detectionMethod || 'unknown'
    });
    
  } catch (error) {
    console.error('Failed to log WAF block to database:', error);
    
    // Fallback: log to console at least
    console.error('🚫 WAF BLOCK (DB FAILED):', {
      timestamp: new Date().toISOString(),
      route: extractRouteName(url, baseURL),
      method: method.toUpperCase(),
      supportId: supportId || 'N/A',
      detectionMethod: detectionMethod || 'unknown',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Retrieves WAF logs from database with optional filtering
 */
export async function getWafLogs(
  limit: number = 50,
  offset: number = 0,
  routeFilter?: string
): Promise<any[]> {
  try {
    let query = `
      SELECT id, route_name, method, headers, body, response, created_at
      FROM waf_logs
    `;
    
    const params: any[] = [];
    
    if (routeFilter) {
      query += ` WHERE route_name ILIKE $1`;
      params.push(`%${routeFilter}%`);
    }
    
    query += ` ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    params.push(limit, offset);
    
    return await executeQuery(query, params);
  } catch (error) {
    console.error('Failed to retrieve WAF logs:', error);
    return [];
  }
}

/**
 * Gets WAF logs statistics
 */
export async function getWafLogsStats(): Promise<{
  totalBlocks: number;
  todayBlocks: number;
  topRoutes: Array<{ route_name: string; count: number }>;
}> {
  try {
    // Total blocks
    const totalResult = await executeQuery(
      'SELECT COUNT(*) as total FROM waf_logs'
    );
    const totalBlocks = parseInt(totalResult[0]?.total || '0');
    
    // Today's blocks
    const todayResult = await executeQuery(
      'SELECT COUNT(*) as today FROM waf_logs WHERE DATE(created_at) = CURRENT_DATE'
    );
    const todayBlocks = parseInt(todayResult[0]?.today || '0');
    
    // Top blocked routes
    const topRoutesResult = await executeQuery(`
      SELECT route_name, COUNT(*) as count
      FROM waf_logs
      GROUP BY route_name
      ORDER BY count DESC
      LIMIT 10
    `);
    
    return {
      totalBlocks,
      todayBlocks,
      topRoutes: topRoutesResult.map(row => ({
        route_name: row.route_name,
        count: parseInt(row.count)
      }))
    };
  } catch (error) {
    console.error('Failed to get WAF logs stats:', error);
    return {
      totalBlocks: 0,
      todayBlocks: 0,
      topRoutes: []
    };
  }
}

/**
 * Cleans up old WAF logs (older than specified days)
 */
export async function cleanupOldWafLogs(daysToKeep: number = 30): Promise<number> {
  try {
    const deleteQuery = `
      DELETE FROM waf_logs
      WHERE created_at < CURRENT_DATE - INTERVAL '${daysToKeep} days'
    `;
    
    const result = await executeQuery(deleteQuery);
    const deletedCount = result.length;
    
    console.log(`Cleaned up ${deletedCount} old WAF logs (older than ${daysToKeep} days)`);
    return deletedCount;
  } catch (error) {
    console.error('Failed to cleanup old WAF logs:', error);
    return 0;
  }
}