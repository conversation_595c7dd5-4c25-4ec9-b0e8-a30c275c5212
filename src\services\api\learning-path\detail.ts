"use server";

import {
  IGetLearningCodeDetailParams,
  IGetLearningCodeDetailResponse,
  IGetLearningLevelDetailParams,
  IGetLearningLevelDetailResponse,
} from "@/interfaces/admin/manage-learning-path/list";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetLearningCodeDetail = async (
  params: IGetLearningCodeDetailParams
) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetLearningCodeDetailResponse>
    >(`/cms/admin/learning/code/detail/${params.id}`);

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};

export const apiGetLearningLevelDetail = async (
  params: IGetLearningLevelDetailParams
) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetLearningLevelDetailResponse>
    >(`/cms/admin/learning/level/detail/${params.id}`);

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
