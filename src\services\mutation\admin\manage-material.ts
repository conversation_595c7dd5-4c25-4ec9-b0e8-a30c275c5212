import {
  apiGetLearningMaterialUrl,
  apiInsertLearningMaterial,
  apiUpdateLearningMaterial,
} from "@/services/api/admin/manage-material";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useGetMaterialUrlMutation = () => {
  return useMutation({
    mutationKey: ["get-material-url"],
    mutationFn: async (params: { url: string }) => {
      return await apiGetLearningMaterialUrl(params);
    },
  });
};

export const useInsertLearningMaterialMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["insert-learning-material"],
    mutationFn: async (body: FormData) => {
      return await apiInsertLearningMaterial(body);
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: ["material"],
      });
    },
  });
};

export const useUpdateLearningMaterialMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["update-learning-material"],
    mutationFn: async ({ id, body }: { id: number; body: FormData }) => {
      return await apiUpdateLearningMaterial(id, body);
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: ["material"],
      });
    },
  });
};
