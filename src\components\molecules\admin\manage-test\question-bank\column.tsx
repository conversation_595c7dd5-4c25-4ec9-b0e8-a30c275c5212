import { BaseButton } from '@/components/atoms/button';
import { IGetQuestionBankListResponse } from '@/interfaces/admin/manage-test/question-bank/list';
import { ColumnDef } from '@tanstack/react-table';
import dayjs from 'dayjs';
import { <PERSON><PERSON>he<PERSON>, Pencil, Trash2 } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import PillDropdown from '../common/dropdown';
import React from 'react';

interface Props {
  onEdit: (row: IGetQuestionBankListResponse) => void;
  onDelete: (row: IGetQuestionBankListResponse) => void;
  isSelectable?: boolean;
  onSelect?: (rows: IGetQuestionBankListResponse[]) => void;
}

const makeOptions = (names: string[], id: string) =>
  names.filter(Boolean).map((name) => ({ value: String(name), id }));

const SmartPill: React.FC<{
  id: string;
  names: string[];
  summaryLabel: string;
}> = ({ id, names, summaryLabel }) => {
  const n = names.length;
  if (n === 0) return <span>-</span>;
  if (n === 1) {
    return (
      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-[#DEDEDE] text-[#3C3C3C]">
        {names[0]}
      </span>
    );
  }
  return (
    <PillDropdown
      id={id}
      selected={summaryLabel}
      options={makeOptions(names, id)}
    />
  );
};

const summary = (n: number, word: string) => `${n} ${word}`;

const mapTypeLabel = (code?: string | null) => {
  const map: Record<string, string> = {
    pilihan_ganda: 'Pilihan Ganda',
    benar_salah: 'Benar Salah',
    isian: 'Isian',
  };
  if (!code) return '-';
  return map[code] ?? code;
};

export const getColumnsQuestionBank = ({
  onEdit,
  onDelete,
  onSelect,
  isSelectable,
}: Props): ColumnDef<IGetQuestionBankListResponse>[] => {
  const selectableCheckbox: ColumnDef<IGetQuestionBankListResponse>[] =
    isSelectable
      ? [
          {
            id: 'select',
            header: ({ table }) => (
              <div className="flex items-center justify-center">
                <Checkbox
                  checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && 'indeterminate')
                  }
                  onCheckedChange={(checked) => {
                    if (checked) {
                      const all = table
                        .getRowModel()
                        .rows.map((r) => r.original);
                      onSelect?.(all);
                    } else {
                      onSelect?.([]);
                    }
                    table.toggleAllPageRowsSelected(!!checked);
                  }}
                  aria-label="Select all"
                />
              </div>
            ),
            cell: ({ row }) => (
              <div className="flex items-center justify-center">
                <Checkbox
                  checked={row.getIsSelected()}
                  onCheckedChange={(value) => {
                    onSelect?.([row.original]);
                    row.toggleSelected(!!value);
                  }}
                  aria-label="Select row"
                />
              </div>
            ),
            enableSorting: false,
            enableHiding: false,
          },
        ]
      : [];

  const actionButton: ColumnDef<IGetQuestionBankListResponse>[] = !isSelectable
    ? [
        {
          id: 'action',
          header: 'Action',
          cell({ row }) {
            return (
              <div className="flex items-center justify-start">
                <BaseButton
                  variant="ghost"
                  className="border-none"
                  onClick={() => onEdit(row.original)}
                >
                  <Pencil
                    size={20}
                    className="fill-gray-700 text-white"
                    strokeWidth={1}
                  />
                </BaseButton>
                <BaseButton
                  variant="ghost"
                  className="border-none"
                  onClick={() => onDelete(row.original)}
                >
                  <Trash2
                    size={20}
                    className="text-gray-700"
                    strokeWidth={2.5}
                  />
                </BaseButton>
              </div>
            );
          },
        },
      ]
    : [];

  return [
    ...selectableCheckbox,

    {
      accessorKey: 'id',
      header: 'Question ID',
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.id}
          </span>
        );
      },
    },

    {
      accessorKey: 'categories',
      header: 'Category',
      cell({ row }) {
        const names = (row.original.categories ?? [])
          .map((c) => c?.name)
          .filter(Boolean) as string[];
        const label = summary(names.length, 'Category');
        return (
          <SmartPill
            id={String(row.original.id)}
            names={names}
            summaryLabel={label}
          />
        );
      },
    },

    {
      accessorKey: 'levels',
      header: 'Level',
      cell({ row }) {
        const names = (row.original.levels ?? [])
          .map((l) => l?.level)
          .filter(Boolean) as string[];
        const label = summary(names.length, 'Levels');
        return (
          <SmartPill
            id={String(row.original.id)}
            names={names}
            summaryLabel={label}
          />
        );
      },
    },

    {
      accessorKey: 'type',
      header: 'Question Type',
      cell({ row }) {
        return <span>{mapTypeLabel(row.original.type)}</span>;
      },
    },

    {
      accessorKey: 'question',
      header: 'Question',
      cell({ row }) {
        return (
          <div className="w-[320px] text-wrap line-clamp-2">
            {row.original.question ?? '-'}
          </div>
        );
      },
    },

    {
      accessorKey: 'option_a',
      header: 'Option A',
      cell({ row }) {
        return <span>{row.original.option_a ?? '-'}</span>;
      },
    },
    {
      accessorKey: 'option_b',
      header: 'Option B',
      cell({ row }) {
        return <span>{row.original.option_b ?? '-'}</span>;
      },
    },
    {
      accessorKey: 'option_c',
      header: 'Option C',
      cell({ row }) {
        return <span>{row.original.option_c ?? '-'}</span>;
      },
    },
    {
      accessorKey: 'option_d',
      header: 'Option D',
      cell({ row }) {
        return <span>{row.original.option_d ?? '-'}</span>;
      },
    },

    {
      accessorKey: 'correct_answer',
      header: 'Key Answer',
      cell({ row }) {
        return (
          <div className="uppercase">{row.original.correct_answer ?? '-'}</div>
        );
      },
    },

    {
      id: 'with_image',
      header: 'With Image?',
      cell({ row }) {
        const enabled = !!(row.original as any)?.image_id;
        return (
          <CircleCheck
            fill={enabled ? '#F7941E' : '#D1D5DB'}
            stroke="white"
          />
        );
      },
    },

    {
      accessorKey: 'associated',
      header: 'Associated Section',
      cell({ row }) {
        const names = (row.original.associated ?? [])
          .map((a) => a?.section_name)
          .filter(Boolean) as string[];
        const label = summary(names.length, 'Sections');
        return (
          <SmartPill
            id={String(row.original.id)}
            names={names}
            summaryLabel={label}
          />
        );
      },
    },

    {
      accessorKey: 'correct_answer_percentage',
      header: 'Correct Answer Percentage',
      cell({ row }) {
        const p = row.original.correct_answer_percentage;
        return <span>{typeof p === 'number' ? `${p}%` : '-'}</span>;
      },
    },

    {
      accessorKey: 'created_at',
      header: 'Created At',
      cell({ row }) {
        const v = row.original.created_at;
        return <span>{v ? dayjs(v).format('DD MMM YYYY HH:mm') : '-'}</span>;
      },
    },

    { accessorKey: 'created_by', header: 'Created By' },

    {
      accessorKey: 'last_updated',
      header: 'Last Updated',
      cell({ row }) {
        const v = row.original.last_updated;
        return <span>{v ? dayjs(v).format('DD MMM YYYY HH:mm') : '-'}</span>;
      },
    },

    { accessorKey: 'updated_by', header: 'Updated By' },

    ...actionButton,
  ];
};
