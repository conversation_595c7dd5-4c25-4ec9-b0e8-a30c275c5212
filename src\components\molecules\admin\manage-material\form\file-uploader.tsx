"use client";

import { BaseLabel } from "@/components/atoms/label";
import React, { forwardRef } from "react";
import { Upload } from "lucide-react";
import IconCloudUpload from "@/assets/icons/IconCloudUpload";

export const FILE_CONFIGS = {
  video: {
    title: "Add New Video",
    uploadLabel: "Upload Video",
    acceptedTypes: ".mp4",
    maxSize: "120MB",
    supportedText: "Supported file types: MP4, max. size 120MB",
    icon: "🎥",
  },
  audio: {
    title: "Add New Audio",
    uploadLabel: "Upload Audio",
    acceptedTypes: ".mp3",
    maxSize: "50MB",
    supportedText: "Supported file types: MP3, max. size 50MB",
    icon: "🎵",
  },
  document: {
    title: "Add New Document",
    uploadLabel: "Upload Document",
    acceptedTypes: ".pdf",
    maxSize: "10MB",
    supportedText: "Supported file types: PDF, max. size 10MB",
    icon: "📄",
  },
  scorm: {
    title: "Add New SCORM",
    uploadLabel: "Upload SCORM",
    acceptedTypes: ".zip",
    maxSize: "10MB",
    supportedText: "Supported file types: ZIP, max. size 10MB",
    icon: "📦",
  },
};

export type MaterialType = keyof typeof FILE_CONFIGS;

interface FileUploaderProps {
  config: (typeof FILE_CONFIGS)[MaterialType];
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  form: any;
}

const FileUploader = forwardRef<HTMLInputElement, FileUploaderProps>(
  ({ config, onFileUpload, form }, ref) => {
    const handleBrowseClick = () => {
      // ref is forwarded, so we need to type-cast to use .current
      if (ref && "current" in ref) {
        ref.current?.click();
      }
    };

    return (
      <div className="space-y-3">
        <BaseLabel className="text-sm font-medium">
          {config.uploadLabel}
        </BaseLabel>

        <label className="block w-full outline-none border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
          <div className="flex items-center justify-center gap-3">
            <IconCloudUpload className="text-[#C0C0C0]" />

            <div className="space-y-2">
              <p className="text-gray-600 text-sm">
                <span className="font-medium">Drag & drop</span> your files or{" "}
                <button
                  type="button"
                  onClick={handleBrowseClick}
                  className="text-orange-500 hover:text-orange-600 font-medium underline"
                >
                  browse
                </button>
              </p>
            </div>
          </div>

          <input
            ref={ref}
            type="file"
            accept={config.acceptedTypes}
            onChange={onFileUpload}
            className="hidden"
          />
        </label>
        <p className="text-sm text-gray-500">{config.supportedText}</p>
        {form.formState.errors.files && (
          <p className="text-red-500 text-sm mt-1">
            {form.formState.errors.files.message}
          </p>
        )}
      </div>
    );
  }
);

FileUploader.displayName = "FileUploader";

export default FileUploader;
