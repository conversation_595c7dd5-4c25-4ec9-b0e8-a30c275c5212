'use client';

import { BaseLabel } from '@/components/atoms/label';
import MultipleSelector, { Option } from '@/components/atoms/multiselect';

interface MultipleSelectorProps {
  options: Option[];
  value?: Option[];
  title?: string;
  placeholder?: string;
  selectAllTitle?: string;
  onSelectAll?: VoidFunction;
  onSelect?: (value: Option[]) => void;
}

const MultipleSelectorComponent = ({
  value,
  title,
  options,
  selectAllTitle,
  placeholder = 'Select',
  onSelectAll,
  onSelect,
}: MultipleSelectorProps) => {
  return (
    <div className="space-y-3 w-full">
      {title ? (
        <BaseLabel className="text-sm font-medium">{title}</BaseLabel>
      ) : null}

      <MultipleSelector
        value={value}
        options={options.map((it) => ({
          label: it.label,
          value: it.value,
        }))}
        onChange={onSelect}
        placeholder={placeholder}
        badgeClassName="bg-base-gray-20 text-comp-content-primary"
        className="bg-white"
        loadingIndicator={
          <p className="py-2 text-center text-sm leading-10 text-muted-foreground">
            loading...
          </p>
        }
        emptyIndicator={
          <p className="w-full text-center text-lg leading-10 text-muted-foreground">
            no results found.
          </p>
        }
      />

      {/* Select All Checkbox */}
      {selectAllTitle ? (
        <label className="flex items-center gap-2 cursor-pointer">
          <input
            type="checkbox"
            checked={value?.length === options.length}
            onChange={onSelectAll}
            className="w-4 h-4 text-orange-500 border-gray-300 rounded focus:ring-orange-500"
          />
          <span className="text-sm text-gray-700">{selectAllTitle}</span>
        </label>
      ) : null}
    </div>
  );
};

export default MultipleSelectorComponent;
