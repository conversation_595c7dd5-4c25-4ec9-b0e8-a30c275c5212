import {
  IGetSectionListQuery,
  IGetSectionDetailParams,
  IGetSectionExportQuery,
} from "@/interfaces/admin/manage-section/list";
import {
  apiGetSectionList,
  apiGetSectionDetail,
  apiExportSection,
} from "@/services/api/admin/manage-section";
import { keepPreviousData, useQuery } from "@tanstack/react-query";

export const sectionQueryKeys = {
  list: (params?: IGetSectionListQuery) => ["section", "list", params],
  detail: (params: IGetSectionDetailParams) => ["section", "detail", params],
  export: (params?: IGetSectionExportQuery) => ["section", "export", params],
};

export const useGetSectionListQuery = (
  params?: IGetSectionListQuery,
  enabled = true
) => {
  const defaultParams = {
    page: 1,
    limit: 10,
  };

  // Filter out empty values to avoid sending unnecessary parameters
  const filteredParams = Object.fromEntries(
    Object.entries(params ?? {}).filter(([_, value]) => {
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      return value !== "" && value !== undefined && value !== null;
    })
  );

  const mergedParams = { ...defaultParams, ...filteredParams };
  return useQuery({
    queryKey: sectionQueryKeys.list(mergedParams),
    queryFn: async () => {
      return await apiGetSectionList(mergedParams);
    },
    placeholderData: keepPreviousData,
    enabled,
  });
};

export const useGetSectionDetailQuery = (
  params: IGetSectionDetailParams,
  enabled = true
) => {
  return useQuery({
    queryKey: sectionQueryKeys.detail(params),
    queryFn: async () => {
      return await apiGetSectionDetail(params);
    },
    enabled: enabled && !!params.id,
    gcTime: 0,
  });
};

export const useExportSectionQuery = (
  params?: IGetSectionExportQuery,
  enabled = false // Usually disabled by default for export queries
) => {
  return useQuery({
    queryKey: sectionQueryKeys.export(params),
    queryFn: async () => {
      return await apiExportSection(params);
    },
    enabled,
    gcTime: 0, // Don't cache export data
  });
};
