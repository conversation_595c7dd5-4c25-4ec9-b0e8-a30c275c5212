import {
  IGetLearningCodeDetailParams,
  IGetLearningLevelDetailParams,
} from "@/interfaces/admin/manage-learning-path/list";
import {
  apiGetLearningCodeDetail,
  apiGetLearningLevelDetail,
} from "@/services/api/learning-path/detail";
import { useQuery } from "@tanstack/react-query";

export const useGetLearningCodeDetailQuery = (
  params: IGetLearningCodeDetailParams
) => {
  return useQuery({
    queryKey: ["learning-code", "detail", params],
    enabled: !!params.id,
    queryFn: async () => {
      return await apiGetLearningCodeDetail(params);
    },
    gcTime: 0,
  });
};

export const useGetLearningLevelDetailQuery = (
  params: IGetLearningLevelDetailParams
) => {
  return useQuery({
    queryKey: ["learning-level", "detail", params],
    enabled: !!params.id,
    queryFn: async () => {
      return await apiGetLearningLevelDetail(params);
    },
    gcTime: 0,
  });
};
