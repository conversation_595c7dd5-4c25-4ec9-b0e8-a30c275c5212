// components/image-repo-combobox.tsx
'use client';

import * as React from 'react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import { ChevronsUpDown, Check } from 'lucide-react';
import { BaseLabel } from '@/components/atoms/label';
import { cn } from '@/lib/utils';

export type ImageRepoOption = { value: string; label: string; link?: string };

type Props = {
  label?: string;
  placeholder?: string;
  value?: string;
  options: ImageRepoOption[];
  onChange: (nextId: string, selected?: ImageRepoOption) => void;
  onSearchChange?: (term: string) => void;
  maxHeight?: number;
};

export default function ImageRepoCombobox({
  label = 'Image',
  placeholder = 'Select image',
  value,
  options,
  onChange,
  onSearchChange,
  maxHeight = 280,
}: Readonly<Props>) {
  const [open, setOpen] = React.useState(false);
  const selected = React.useMemo(
    () => options.find((o) => o.value === value),
    [options, value]
  );

  return (
    <div className="flex flex-col gap-1 w-full">
      <BaseLabel className="text-sm">{label}</BaseLabel>
      <Popover
        open={open}
        onOpenChange={setOpen}
      >
        <PopoverTrigger asChild>
          <button
            type="button"
            className="w-full min-h-11 inline-flex items-center justify-between rounded-md border px-3 py-2 text-sm"
          >
            <span
              className={cn('truncate', !selected && 'text-muted-foreground')}
            >
              {selected ? selected.label : placeholder}
            </span>
            <ChevronsUpDown
              size={16}
              className="opacity-50"
            />
          </button>
        </PopoverTrigger>
        <PopoverContent
          className="p-0"
          style={{ width: 'var(--radix-popover-trigger-width)' }}
        >
          <Command>
            <CommandInput
              placeholder="Search image..."
              onValueChange={(val) => onSearchChange?.(val)} // server-side search
            />
            <div style={{ maxHeight, overflow: 'auto' }}>
              <CommandEmpty className="px-3 py-2">No image found.</CommandEmpty>
              <CommandGroup>
                {options.map((opt) => (
                  <CommandItem
                    key={opt.value}
                    value={opt.label}
                    onSelect={() => {
                      onChange(opt.value, opt);
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        value === opt.value ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                    <span className="truncate">{opt.label}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
            </div>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
