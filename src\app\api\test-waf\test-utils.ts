/**
 * WAF Test Responses - Various patterns for testing WAF detection
 */
export const WAF_TEST_RESPONSES = {
  standard: `<html>

<head>
    <title>Request Rejected</title>
</head>

<body>The requested URL was rejected. Please consult with your
    administrator.<br><br>Your support ID is: 16146255080533291147<br><br><a href='javascript:history.back();'>[Go
        Back]</a></body>

</html>`,
  
  minimal: `
    <html>
    <head><title>Blocked</title></head>
    <body>
      <h1>Blocked</h1>
      <p>blocked by web application firewall</p>
      <p>Reference: WAF-2024-001</p>
    </body>
    </html>
  `,
  
  altSupportId: `
    <!DOCTYPE html>
    <html>
    <head><title>Request Blocked</title></head>
    <body>
      <h1>Request Blocked</h1>
      <p>This request has been blocked by the security system.</p>
      <p>Ticket Number: TKT-789456123</p>
      <p>Please contact support if needed.</p>
    </body>
    </html>
  `,
  
  consultation: `
    <html>
    <head><title>Access Restricted</title></head>
    <body>
      <h1>Access Restricted</h1>
      <p>Your access has been restricted.</p>
      <p>Please consult the administrator for assistance.</p>
      <p>Support ID: ADM-555666777</p>
    </body>
    </html>
  `,
  
  normalError: `
    <!DOCTYPE html>
    <html>
    <head><title>Server Error</title></head>
    <body>
      <h1>Internal Server Error</h1>
      <p>The server encountered an unexpected condition.</p>
      <p>Error Code: 500</p>
    </body>
    </html>
  `
} as const;

/**
 * Create a mock Axios response for testing
 */
export function createMockWafResponse(type: keyof typeof WAF_TEST_RESPONSES) {
  return {
    data: WAF_TEST_RESPONSES[type],
    status: 200,
    statusText: 'OK',
    headers: {
      'content-type': 'text/html; charset=utf-8',
      'server': 'nginx/1.18.0'
    },
    config: {
      method: 'get',
      url: '/api/test',
      baseURL: 'http://localhost:3000',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'axios/1.6.0'
      },
      data: undefined
    },
    request: {}
  };
}

/**
 * Create a mock Axios error for testing
 */
export function createMockWafError(type: keyof typeof WAF_TEST_RESPONSES) {
  const mockResponse = createMockWafResponse(type);
  
  return {
    message: 'Request failed with status code 403',
    name: 'AxiosError',
    code: 'ERR_BAD_REQUEST',
    config: mockResponse.config,
    request: mockResponse.request,
    response: {
      ...mockResponse,
      status: 403,
      statusText: 'Forbidden'
    },
    isAxiosError: true,
    toJSON: () => ({})
  };
}