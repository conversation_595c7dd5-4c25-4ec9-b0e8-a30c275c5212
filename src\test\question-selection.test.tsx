/**
 * Test file for Question Selection Components
 * Tests the question selection functionality for quiz/test sections
 */

import { describe, it, expect } from '@jest/globals';

// Mock question bank data
const mockQuestionBankData = [
  {
    id: 1,
    question: "What is React?",
    type: "multiple_choice",
    option_a: "A library",
    option_b: "A framework",
    option_c: "A language",
    option_d: "A database",
    correct_answer: "A",
    categories: [{ id: 1, name: "Programming" }],
    levels: [{ id: 1, level: "Beginner" }],
  },
  {
    id: 2,
    question: "What is JSX?",
    type: "multiple_choice",
    option_a: "JavaScript XML",
    option_b: "Java Syntax",
    option_c: "JSON XML",
    option_d: "JavaScript Extension",
    correct_answer: "A",
    categories: [{ id: 1, name: "Programming" }],
    levels: [{ id: 2, level: "Intermediate" }],
  },
];

// Mock question template data
const mockQuestionTemplateData = [
  {
    id: 1,
    question_template_name: "React Basics Quiz",
    type: "quiz",
    categories: [{ id: 1, name: "Programming" }],
    levels: [{ id: 1, name: "<PERSON><PERSON><PERSON>" }],
    questions: [
      { id: 3, question: "What is a component?" },
      { id: 4, question: "What is state?" },
      { id: 5, question: "What are props?" },
    ],
  },
];

// Question selection transformation functions
const transformQuestionBankToSelected = (questions: typeof mockQuestionBankData) => {
  return questions.map(q => ({
    id: q.id,
    question: q.question,
    type: q.type,
    source: "bank" as const,
    options: {
      a: q.option_a,
      b: q.option_b,
      c: q.option_c,
      d: q.option_d,
    },
    correctAnswer: q.correct_answer,
  }));
};

const transformQuestionTemplateToSelected = (template: typeof mockQuestionTemplateData[0]) => {
  return template.questions.map(q => ({
    id: q.id,
    question: q.question,
    type: template.type,
    source: "template" as const,
    templateName: template.question_template_name,
  }));
};

const generateQuestionIds = (selectedQuestions: any[]) => {
  return selectedQuestions.map(q => q.id.toString());
};

const calculateNumberOfQuestions = (selectedQuestions: any[]) => {
  return selectedQuestions.length;
};

describe('Question Selection Functionality', () => {
  describe('Question Bank Selection', () => {
    it('should transform question bank data correctly', () => {
      const selected = transformQuestionBankToSelected(mockQuestionBankData);
      
      expect(selected).toHaveLength(2);
      expect(selected[0]).toEqual({
        id: 1,
        question: "What is React?",
        type: "multiple_choice",
        source: "bank",
        options: {
          a: "A library",
          b: "A framework",
          c: "A language",
          d: "A database",
        },
        correctAnswer: "A",
      });
    });

    it('should generate question IDs correctly', () => {
      const selected = transformQuestionBankToSelected(mockQuestionBankData);
      const questionIds = generateQuestionIds(selected);
      
      expect(questionIds).toEqual(["1", "2"]);
    });

    it('should calculate number of questions correctly', () => {
      const selected = transformQuestionBankToSelected(mockQuestionBankData);
      const count = calculateNumberOfQuestions(selected);
      
      expect(count).toBe(2);
    });
  });

  describe('Question Template Selection', () => {
    it('should transform question template data correctly', () => {
      const selected = transformQuestionTemplateToSelected(mockQuestionTemplateData[0]);
      
      expect(selected).toHaveLength(3);
      expect(selected[0]).toEqual({
        id: 3,
        question: "What is a component?",
        type: "quiz",
        source: "template",
        templateName: "React Basics Quiz",
      });
    });

    it('should handle template with multiple questions', () => {
      const selected = transformQuestionTemplateToSelected(mockQuestionTemplateData[0]);
      const questionIds = generateQuestionIds(selected);
      
      expect(questionIds).toEqual(["3", "4", "5"]);
      expect(calculateNumberOfQuestions(selected)).toBe(3);
    });
  });

  describe('Mixed Selection (Bank + Template)', () => {
    it('should handle mixed question sources', () => {
      const bankQuestions = transformQuestionBankToSelected(mockQuestionBankData);
      const templateQuestions = transformQuestionTemplateToSelected(mockQuestionTemplateData[0]);
      const allQuestions = [...bankQuestions, ...templateQuestions];
      
      expect(allQuestions).toHaveLength(5);
      expect(allQuestions.filter(q => q.source === "bank")).toHaveLength(2);
      expect(allQuestions.filter(q => q.source === "template")).toHaveLength(3);
    });

    it('should prevent duplicate questions', () => {
      const existingIds = [1, 3];
      const newQuestions = mockQuestionBankData.filter(q => !existingIds.includes(q.id));
      
      expect(newQuestions).toHaveLength(1);
      expect(newQuestions[0].id).toBe(2);
    });

    it('should generate correct API payload', () => {
      const bankQuestions = transformQuestionBankToSelected(mockQuestionBankData);
      const templateQuestions = transformQuestionTemplateToSelected(mockQuestionTemplateData[0]);
      const allQuestions = [...bankQuestions, ...templateQuestions];
      
      const payload = {
        question_id: generateQuestionIds(allQuestions).map(id => parseInt(id)),
        number_of_question: calculateNumberOfQuestions(allQuestions),
        passing_grade: 70,
        with_test_timer: true,
      };
      
      expect(payload).toEqual({
        question_id: [1, 2, 3, 4, 5],
        number_of_question: 5,
        passing_grade: 70,
        with_test_timer: true,
      });
    });
  });

  describe('Form Validation', () => {
    it('should validate minimum question requirement', () => {
      const emptyQuestions: any[] = [];
      const isValid = emptyQuestions.length >= 1;
      
      expect(isValid).toBe(false);
    });

    it('should validate with selected questions', () => {
      const selectedQuestions = transformQuestionBankToSelected(mockQuestionBankData);
      const isValid = selectedQuestions.length >= 1;
      
      expect(isValid).toBe(true);
    });

    it('should validate question data structure', () => {
      const selectedQuestions = transformQuestionBankToSelected(mockQuestionBankData);
      
      selectedQuestions.forEach(question => {
        expect(question).toHaveProperty('id');
        expect(question).toHaveProperty('question');
        expect(question).toHaveProperty('type');
        expect(question).toHaveProperty('source');
        expect(['bank', 'template']).toContain(question.source);
      });
    });
  });

  describe('Search and Filter Functionality', () => {
    it('should filter questions by search term', () => {
      const searchTerm = "React";
      const filtered = mockQuestionBankData.filter(q => 
        q.question.toLowerCase().includes(searchTerm.toLowerCase())
      );
      
      expect(filtered).toHaveLength(1);
      expect(filtered[0].question).toBe("What is React?");
    });

    it('should filter questions by category', () => {
      const categoryId = 1;
      const filtered = mockQuestionBankData.filter(q => 
        q.categories.some(cat => cat.id === categoryId)
      );
      
      expect(filtered).toHaveLength(2);
    });

    it('should filter questions by level', () => {
      const levelId = 1;
      const filtered = mockQuestionBankData.filter(q => 
        q.levels.some(level => level.id === levelId)
      );
      
      expect(filtered).toHaveLength(1);
      expect(filtered[0].question).toBe("What is React?");
    });

    it('should filter questions by type', () => {
      const questionType = "multiple_choice";
      const filtered = mockQuestionBankData.filter(q => q.type === questionType);
      
      expect(filtered).toHaveLength(2);
    });
  });

  describe('Question Source Identification', () => {
    it('should correctly identify question bank sources', () => {
      const selected = transformQuestionBankToSelected(mockQuestionBankData);
      const bankQuestions = selected.filter(q => q.source === "bank");
      
      expect(bankQuestions).toHaveLength(2);
      bankQuestions.forEach(q => {
        expect(q.source).toBe("bank");
        expect(q.options).toBeDefined();
        expect(q.correctAnswer).toBeDefined();
      });
    });

    it('should correctly identify template sources', () => {
      const selected = transformQuestionTemplateToSelected(mockQuestionTemplateData[0]);
      const templateQuestions = selected.filter(q => q.source === "template");
      
      expect(templateQuestions).toHaveLength(3);
      templateQuestions.forEach(q => {
        expect(q.source).toBe("template");
        expect(q.templateName).toBe("React Basics Quiz");
      });
    });
  });

  describe('Auto-Calculation Features', () => {
    it('should auto-calculate number of questions when adding from bank', () => {
      let selectedQuestions: any[] = [];
      const newQuestions = transformQuestionBankToSelected([mockQuestionBankData[0]]);
      
      selectedQuestions = [...selectedQuestions, ...newQuestions];
      
      expect(calculateNumberOfQuestions(selectedQuestions)).toBe(1);
    });

    it('should auto-calculate number of questions when adding from template', () => {
      let selectedQuestions: any[] = [];
      const newQuestions = transformQuestionTemplateToSelected(mockQuestionTemplateData[0]);
      
      selectedQuestions = [...selectedQuestions, ...newQuestions];
      
      expect(calculateNumberOfQuestions(selectedQuestions)).toBe(3);
    });

    it('should update question IDs when questions change', () => {
      const bankQuestions = transformQuestionBankToSelected([mockQuestionBankData[0]]);
      const templateQuestions = transformQuestionTemplateToSelected(mockQuestionTemplateData[0]);
      
      let allQuestions = [...bankQuestions];
      let questionIds = generateQuestionIds(allQuestions);
      expect(questionIds).toEqual(["1"]);
      
      allQuestions = [...allQuestions, ...templateQuestions];
      questionIds = generateQuestionIds(allQuestions);
      expect(questionIds).toEqual(["1", "3", "4", "5"]);
    });
  });
});

console.log('✅ Question Selection Tests Setup Complete');
console.log('📋 Test Coverage:');
console.log('  - Question Bank data transformation');
console.log('  - Question Template data transformation');
console.log('  - Mixed source question handling');
console.log('  - Duplicate question prevention');
console.log('  - API payload generation');
console.log('  - Form validation requirements');
console.log('  - Search and filter functionality');
console.log('  - Question source identification');
console.log('  - Auto-calculation features');
console.log('  - Question ID array generation');
