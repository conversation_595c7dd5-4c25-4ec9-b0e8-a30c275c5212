import { ICreateQuestionBankBody } from '@/interfaces/admin/manage-test/question-bank/form';
import { ICreateQuestionBankBodyData } from '@/interfaces/admin/manage-test/question-bank/new';

export function buildCreateQuestionBankPayload(
  v: ICreateQuestionBankBody,
  feature: 'OnlineLearning' | 'InClassTraining' = 'OnlineLearning'
): { body: ICreateQuestionBankBodyData; file?: File | null } {
  const typeMap = {
    pilihan_ganda: '<PERSON><PERSON><PERSON> Ganda',
    benar_salah: 'Benar Salah',
    isian: 'Isian',
  } as const;

  const qt = v.questionType;

  let correct_answer: string | undefined;
  if (qt === 'pilihan_ganda') {
    correct_answer = v.keyAnswer ? v.keyAnswer.toUpperCase() : undefined;
  } else if (qt === 'benar_salah') {
    correct_answer = v.keyAnswer ?? undefined;
  } else {
    correct_answer = v.keyAnswer?.trim() || undefined;
  }

  const [a, b, c, d] = v.option ?? [];

  const body: ICreateQuestionBankBodyData = {
    question: v.question,
    type: typeMap[v.questionType],
    feature,
    option_a: a || undefined,
    option_b: b || undefined,
    option_c: c || undefined,
    option_d: d || undefined,
    correct_answer,
    category_id: (v.category ?? []).map((o) => o.value),
    level_id: (v.level ?? []).map((l) => l.value!),
    ...(v.howToAddQuestionImage === 'upload_image' && v.imageName
      ? { image_name: v.imageName }
      : {}),
    ...(v.howToAddQuestionImage === 'select_image' && v.imageFromRepository
      ? { image_id: Number(v.imageFromRepository) }
      : {}),
  };

  const file =
    v.howToAddQuestionImage === 'upload_image'
      ? (v.image as File | null)
      : null;

  return { body, file };
}
