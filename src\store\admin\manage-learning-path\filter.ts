import {
  IGetJobPositionListQuery,
  IGetJobPositionListResponse,
} from "@/interfaces/admin/manage-job/list";
import {
  IGetLearningCodeListQuery,
  IGetLearningLevelListQuery,
} from "@/interfaces/admin/manage-learning-path/list";
import { IGlobalPaginationDto } from "@/interfaces/global/pagination";
import { create } from "zustand";

interface IManageLearningPathFilter {
  learningCodeQuery: IGetLearningCodeListQuery;
  setLearningCodeQuery: (query: IGetLearningCodeListQuery) => void;
  learningLevelQuery: IGetLearningLevelListQuery;
  setLearningLevelQuery: (query: IGetLearningLevelListQuery) => void;
  currentData: number | null;
  setCurrentData: (data: number | null) => void;
  selectedJobPositions: IGetJobPositionListResponse[];
  setSelectedJobPositions: (data: IGetJobPositionListResponse[]) => void;
  selectedJobQuery: IGetJobPositionListQuery;
  setSelectedJobQuery: (query: IGetJobPositionListQuery) => void;
  selectedJobPagination: IGlobalPaginationDto;
  setSelectedJobPagination: (data: IGlobalPaginationDto) => void;
}

export const useManageLearningPathFilterStore =
  create<IManageLearningPathFilter>()((set) => ({
    learningCodeQuery: {},
    setLearningCodeQuery: (query: IGetLearningCodeListQuery) =>
      set({ learningCodeQuery: query }),
    learningLevelQuery: {},
    setLearningLevelQuery: (query: IGetLearningLevelListQuery) =>
      set({ learningLevelQuery: query }),
    currentData: null,
    setCurrentData: (data: number | null) => set({ currentData: data }),
    selectedJobPositions: [],
    setSelectedJobPositions: (data: IGetJobPositionListResponse[]) =>
      set({ selectedJobPositions: data }),
    selectedJobQuery: {},
    setSelectedJobQuery: (query: IGetJobPositionListQuery) =>
      set({ selectedJobQuery: query }),
    selectedJobPagination: {
      current_page: 1,
      next: null,
      prev: null,
      total_data: 0,
      total_page: 0,
    },
    setSelectedJobPagination: (data: IGlobalPaginationDto) =>
      set({ selectedJobPagination: data }),
  }));
