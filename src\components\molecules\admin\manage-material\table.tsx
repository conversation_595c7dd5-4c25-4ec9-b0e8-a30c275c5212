"use client";

import React from "react";
import { DataTable } from "../../global/table";

import { useShallow } from "zustand/react/shallow";
import { getMaterialColumns } from "./column";
import {
  IGetListMaterialQuery,
  IGetListMaterialResponse,
} from "@/interfaces/admin/manage-material/list";
import ManageMaterialTableHeader from "./table-header";
import { useManageMaterialTabStore } from "@/store/admin/manage-material/tab";
import { useManageMaterialModal } from "@/store/admin/manage-material/modal";
import DeleteConfirmationModal from "./delete-material-confirmation-modal";
import EditMaterialModal from "./edit-modal";
import PreviewMaterialModal from "./preview-modal";
import {
  useDeleteMaterialMutation,
  useGetListMaterialQuery,
} from "@/services/query/admin/manage-material";
import { useManageMaterialQueryStore } from "@/store/admin/manage-material/query";
import { useGetMaterialUrlMutation } from "@/services/mutation/admin/manage-material";
import { notifyHotError, notifyHotSuccess } from "../../toast/hot-toast";

const DUMMY_PAGINATION = {
  current_page: 1,
  total_page: 1,
  total_data: 1,
  next: null,
  prev: null,
};

const ManageMaterialTable = () => {
  const activeTab = useManageMaterialTabStore((state) => state.activeTab);

  const {
    setOpenedMaterial,
    setOpenDeleteModal,
    openDeleteModal,
    openedMaterial,
  } = useManageMaterialModal(
    useShallow(
      ({
        setOpenedMaterial,
        setOpenDeleteModal,
        openDeleteModal,
        openedMaterial,
      }) => ({
        setOpenedMaterial,
        setOpenDeleteModal,
        openDeleteModal,
        openedMaterial,
      })
    )
  );

  const { materialQuery } = useManageMaterialQueryStore(
    useShallow((state) => ({
      materialQuery: state.materialQuery,
    }))
  );

  const { mutateAsync: deleteMaterial, isPending: pendingDelete } =
    useDeleteMaterialMutation();

  const { data: material } = useGetListMaterialQuery({
    ...materialQuery,
    type: activeTab as "video" | "audio" | "document",
  });

  const { mutateAsync: getMaterialUrl } = useGetMaterialUrlMutation();

  const [openEditModal, setOpenEditModal] = React.useState(false);
  const [openPreviewModal, setOpenPreviewModal] = React.useState(false);

  const handleDownload = async (material: IGetListMaterialResponse) => {
    try {
      const res = await getMaterialUrl({ url: material.link });
      const url = res.data.url;
      const link = document.createElement("a");
      link.href = url;
      link.target = "_blank";
      link.download = material.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      notifyHotError("Failed to download material");
    }
  };

  const columns = React.useMemo(
    () =>
      getMaterialColumns({
        onEdit(material) {
          setOpenedMaterial(material);
          setOpenEditModal(true);
        },
        onDownload(material) {
          handleDownload(material);
        },
        onDelete(material) {
          setOpenedMaterial(material);
          setOpenDeleteModal(true);
        },
        onPreview(material) {
          setOpenedMaterial(material);
          setOpenPreviewModal(true);
        },
        type: activeTab,
      }),
    [activeTab]
  );

  const handleDeleteMaterial = async () => {
    if (!openedMaterial?.id) return;
    try {
      await deleteMaterial(openedMaterial?.id);
      notifyHotSuccess("Material deleted successfully");
      setOpenDeleteModal(false);
    } catch (error) {
      notifyHotError("Failed to delete material");
    }
  };

  // Determine material type based on activeTab or material data
  const getMaterialType = (): "video" | "audio" | "document" => {
    if (activeTab === "video") return "video";
    if (activeTab === "audio") return "audio";
    return "document";
  };

  return (
    <div className="flex flex-col gap-4 h-full">
      <ManageMaterialTableHeader />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={openDeleteModal}
        onOpenChange={() => setOpenDeleteModal(false)}
        onConfirm={handleDeleteMaterial}
        title="Hapus Material"
        description="Apakah Anda yakin ingin menghapus material ini?"
        isLoading={pendingDelete}
      />

      {/* Edit Material Modal */}
      <EditMaterialModal
        isOpen={openEditModal}
        onClose={() => {
          setOpenEditModal(false);
          setOpenedMaterial(null);
        }}
        material={openedMaterial}
        type={getMaterialType()}
      />

      {/* Preview Material Modal */}
      <PreviewMaterialModal
        isOpen={openPreviewModal}
        onClose={() => {
          setOpenPreviewModal(false);
          setOpenedMaterial(null);
        }}
        material={openedMaterial}
        type={getMaterialType()}
      />

      <DataTable
        key={activeTab}
        columns={columns}
        data={(material?.data as any) || []}
        pagination={DUMMY_PAGINATION}
        onPageChange={(page) => console.log(page)}
      />
    </div>
  );
};

export default ManageMaterialTable;
