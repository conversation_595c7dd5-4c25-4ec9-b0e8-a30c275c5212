// Section list interfaces
export interface IGetSectionListQuery {
  search?: string;
  search_by?:
    | ""
    | "sub_section_id"
    | "sub_section_name"
    | "soft_competency_name"
    | "tech_competency_name"
    | "modul_name"
    | "created_by"
    | "updated_by";
  level_id?: number;
  type?: string; // e.g., "video,test" - multiple types separated by comma
  category_id?: number;
  page?: number;
  limit?: number;
}

export interface ICategory {
  id: number;
  name: string;
}

export interface ILevel {
  id: number;
  level: number;
}

export interface IModul {
  id: number;
  name: string;
}

export interface IGetSectionListResponse {
  id: number;
  section_name: string;
  section_type: string;
  duration: number;
  categories: ICategory[];
  levels: ILevel[];
  soft_competency: any[];
  tech_competency: any[];
  modul: IModul[];
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
  is_active?: boolean;
}

// Section detail interfaces
export interface IGetSectionDetailParams {
  id: number;
}

export interface IGetSectionDetailResponse {
  section_id: number;
  section_name: string;
  section_type: "video" | "test" | "document" | "audio";
  material_repository_id?: number;
  material_link?: string;
  passing_grade?: number;
  number_of_question?: number;
  with_test_timer?: boolean;
  duration?: number;
  assignment_instruction?: string;
  question_ids?: number[];
  competency_ids?: number[];
  category_ids?: number[];
  level_ids?: number[];
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string;
  is_active: boolean;
}

// Section export interfaces
export interface IGetSectionExportQuery {
  search?: string;
  search_by?: "soft_competency_name" | "section_name" | "section_type";
  level_id?: number;
  type?: string; // e.g., "video,test"
  category_id?: number;
}

// Legacy interface for backward compatibility
export interface Section {
  sectionId: number;
  sectionName: string;
  sectionType: "Video" | "Document" | "Quiz" | string;
  duration: string; // e.g. "00 hrs 11 mnt 00 sec"
  technicalCompetencies: string | null;
  softCompetencies: string | null;
  attachedSubModule: string; // e.g. "5 Sub-Module"
  lastUpdated: string; // ISO date string or formatted date
  updatedBy: string;
}
