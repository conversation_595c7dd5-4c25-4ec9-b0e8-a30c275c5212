import { BaseButton } from '@/components/atoms/button';
import { ColumnDef } from '@tanstack/react-table';
import dayjs from 'dayjs';
import { Pencil, Trash2 } from 'lucide-react';
import PillDropdown from '../common/dropdown';
import { IGetQuestionTemplateListResponse } from '@/interfaces/admin/manage-test/question-template/list';

interface Props {
  onEdit: (row: IGetQuestionTemplateListResponse) => void;
  onDelete: (row: IGetQuestionTemplateListResponse) => void;
}

const makeOptions = (names: string[], id: string) =>
  names.filter(Boolean).map((name) => ({ value: String(name), id }));

const SmartPill: React.FC<{
  id: string;
  names: string[];
  summaryLabel: string;
}> = ({ id, names, summaryLabel }) => {
  const n = names.length;
  if (n === 0) return <span>-</span>;
  if (n === 1) {
    return (
      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-[#DEDEDE] text-[#3C3C3C]">
        {names[0]}
      </span>
    );
  }
  return (
    <PillDropdown
      id={id}
      selected={summaryLabel}
      options={makeOptions(names, id)}
    />
  );
};

const mapTypeLabel = (code?: string | null) => {
  const map: Record<string, string> = {
    pilihan_ganda: 'Pilihan Ganda',
    benar_salah: 'Benar Salah',
    isian: 'Isian',
  };
  if (!code) return '-';
  return map[code] ?? code;
};

const summary = (n: number, word: string) => `${n} ${word}`;

export const getColumnsQuestionTemplate = ({
  onEdit,
  onDelete,
}: Props): ColumnDef<IGetQuestionTemplateListResponse>[] => {
  return [
    {
      accessorKey: 'id',
      header: 'Question Template ID',
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.id}
          </span>
        );
      },
    },

    {
      accessorKey: 'categories',
      header: 'Category',
      cell({ row }) {
        const names = (row.original.categories ?? [])
          .map((c) => c?.name)
          .filter(Boolean) as string[];
        const label = summary(names.length, 'Category');
        return (
          <SmartPill
            id={String(row.original.id)}
            names={names}
            summaryLabel={label}
          />
        );
      },
    },

    {
      accessorKey: 'levels',
      header: 'Level',
      cell({ row }) {
        const names = (row.original.levels ?? [])
          .map((l) => l?.name)
          .filter(Boolean) as string[];
        const label = summary(names.length, 'Levels');
        return (
          <SmartPill
            id={String(row.original.id)}
            names={names}
            summaryLabel={label}
          />
        );
      },
    },

    {
      accessorKey: 'question_template_name',
      header: 'Template Name',
      cell({ row }) {
        return (
          <div className="w-[320px] text-wrap line-clamp-2">
            {row.original.question_template_name ?? '-'}
          </div>
        );
      },
    },

    {
      accessorKey: 'questions',
      header: 'Questions',
      cell({ row }) {
        const names = (row.original.questions ?? [])
          .map((q) => q?.question)
          .filter(Boolean) as string[];
        const label = summary(names.length, 'Questions');
        return (
          <SmartPill
            id={String(row.original.id)}
            names={names}
            summaryLabel={label}
          />
        );
      },
    },

    {
      accessorKey: 'type',
      header: 'Template Type',
      cell({ row }) {
        return <span>{mapTypeLabel(row.original.type)}</span>;
      },
    },

    {
      accessorKey: 'created_at',
      header: 'Created At',
      cell({ row }) {
        const v = row.original.created_at;
        return <span>{v ? dayjs(v).format('DD MMM YYYY HH:mm') : '-'}</span>;
      },
    },
    { accessorKey: 'created_by', header: 'Created By' },
    {
      accessorKey: 'last_updated',
      header: 'Last Updated',
      cell({ row }) {
        const v = row.original.last_updated;
        return <span>{v ? dayjs(v).format('DD MMM YYYY HH:mm') : '-'}</span>;
      },
    },
    { accessorKey: 'updated_by', header: 'Updated By' },

    {
      id: 'action',
      header: 'Action',
      cell({ row }) {
        return (
          <div className="flex items-center justify-start">
            <BaseButton
              variant="ghost"
              className="border-none"
              onClick={() => onEdit(row.original)}
            >
              <Pencil
                size={20}
                className="fill-gray-700 text-white"
                strokeWidth={1}
              />
            </BaseButton>
            <BaseButton
              variant="ghost"
              className="border-none"
              onClick={() => onDelete(row.original)}
            >
              <Trash2
                size={20}
                className="text-gray-700"
                strokeWidth={2.5}
              />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};
