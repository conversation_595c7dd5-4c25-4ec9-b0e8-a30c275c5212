export interface WafDetectionResult {
  isWafBlock: boolean;
  responseData: string;
  supportId?: string;
  detectionMethod: string;
}

/**
 * Detects WAF blocks based on comprehensive HTML response patterns
 * Enhanced to precisely detect various WAF block scenarios
 */
export function detectWafBlock(response: any, error: any): WafDetectionResult {
  // Check for WAF block in successful response (status 200 with HTML content)
  if (response?.status === 200 && response?.data) {
    const responseData = typeof response.data === 'string' ? response.data : '';
    const wafResult = analyzeWafResponse(responseData);
    
    if (wafResult.isWafBlock) {
      return {
        ...wafResult,
        responseData: responseData
      };
    }
  }
  
  // Check for WAF block in error responses
  if (error?.response?.data) {
    const errorData = typeof error.response.data === 'string' ? error.response.data : '';
    const wafResult = analyzeWafResponse(errorData);
    
    if (wafResult.isWafBlock) {
      return {
        ...wafResult,
        responseData: errorData
      };
    }
  }
  
  return { 
    isWafBlock: false, 
    responseData: '',
    detectionMethod: 'none'
  };
}

/**
 * Comprehensive WAF response analysis
 * Detects multiple WAF block patterns with high precision
 */
function analyzeWafResponse(responseData: string): Omit<WafDetectionResult, 'responseData'> {
  if (!responseData || typeof responseData !== 'string') {
    return { isWafBlock: false, detectionMethod: 'invalid_data' };
  }
  
  // Normalize response data for analysis (remove extra whitespace, case insensitive)
  const normalizedData = responseData.replace(/\s+/g, ' ').toLowerCase();
  
  // Pattern 1: Exact title match with body content
  const titlePattern = /<title>\s*request\s+rejected\s*<\/title>/i;
  const bodyPattern = /the\s+requested\s+url\s+was\s+rejected/i;
  
  if (titlePattern.test(responseData) && bodyPattern.test(responseData)) {
    const supportId = extractSupportId(responseData);
    return {
      isWafBlock: true,
      supportId: supportId,
      detectionMethod: 'title_and_body_match'
    };
  }
  
  // Pattern 2: HTML structure analysis - detect minimal HTML with rejection message
  const htmlPattern = /<html[\s\S]*<head[\s\S]*<title>\s*request\s+rejected\s*<\/title>[\s\S]*<\/head>[\s\S]*<body[\s\S]*rejected[\s\S]*<\/body>[\s\S]*<\/html>/i;
  
  if (htmlPattern.test(responseData)) {
    const supportId = extractSupportId(responseData);
    return {
      isWafBlock: true,
      supportId: supportId,
      detectionMethod: 'html_structure_match'
    };
  }
  
  // Pattern 3: Support ID presence with rejection keywords
  const supportIdPattern = /support\s+id\s+is:\s*(\d+)/i;
  const rejectionKeywords = ['rejected', 'blocked', 'denied', 'forbidden'];
  
  if (supportIdPattern.test(responseData) && 
      rejectionKeywords.some(keyword => normalizedData.includes(keyword))) {
    const supportId = extractSupportId(responseData);
    return {
      isWafBlock: true,
      supportId: supportId,
      detectionMethod: 'support_id_with_rejection'
    };
  }
  
  // Pattern 4: Administrator consultation message
  const adminPattern = /consult\s+with\s+your\s+administrator/i;
  const goBackPattern = /\[go\s+back\]/i;
  
  if (adminPattern.test(responseData) && goBackPattern.test(responseData)) {
    const supportId = extractSupportId(responseData);
    return {
      isWafBlock: true,
      supportId: supportId,
      detectionMethod: 'admin_consultation_pattern'
    };
  }
  
  // Pattern 5: Content-Type mismatch detection (expecting JSON but got HTML with rejection)
  const isHtmlContent = /<html/i.test(responseData) || /<head/i.test(responseData) || /<body/i.test(responseData);
  const hasRejectionContent = /rejected|blocked|denied/i.test(responseData);
  
  if (isHtmlContent && hasRejectionContent && responseData.length < 2000) {
    // Likely a WAF block if it's a small HTML response with rejection content
    const supportId = extractSupportId(responseData);
    return {
      isWafBlock: true,
      supportId: supportId,
      detectionMethod: 'html_rejection_mismatch'
    };
  }
  
  return { isWafBlock: false, detectionMethod: 'no_match' };
}

/**
 * Extracts support ID from WAF response
 */
function extractSupportId(responseData: string): string | undefined {
  // Pattern: "Your support ID is: 16146255080533291147"
  const supportIdMatch = responseData.match(/support\s+id\s+is:\s*(\d+)/i);
  if (supportIdMatch && supportIdMatch[1]) {
    return supportIdMatch[1];
  }
  
  // Alternative patterns
  const altPatterns = [
    /support\s*id:\s*(\d+)/i,
    /id:\s*(\d+)/i,
    /reference:\s*(\d+)/i,
    /ticket:\s*(\d+)/i
  ];
  
  for (const pattern of altPatterns) {
    const match = responseData.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }
  
  return undefined;
}

/**
 * Sanitizes sensitive data from headers and body for logging
 */
export function sanitizeForLogging(data: any): any {
  if (!data) return data;
  
  const sensitiveKeys = ['password', 'token', 'apikey', 'authorization', 'cookie', 'x-api-key'];
  
  if (typeof data === 'object') {
    const sanitized = { ...data };
    
    Object.keys(sanitized).forEach(key => {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      }
    });
    
    return sanitized;
  }
  
  return data;
}

/**
 * Extracts route name from URL
 */
export function extractRouteName(url: string, baseURL: string): string {
  try {
    if (!url) return 'unknown';
    
    // Remove base URL if present
    let routeName = url;
    if (baseURL && url.startsWith(baseURL)) {
      routeName = url.substring(baseURL.length);
    }
    
    // Remove query parameters
    const queryIndex = routeName.indexOf('?');
    if (queryIndex !== -1) {
      routeName = routeName.substring(0, queryIndex);
    }
    
    // Ensure it starts with /
    if (!routeName.startsWith('/')) {
      routeName = '/' + routeName;
    }
    
    return routeName;
  } catch (error) {
    console.error('Error extracting route name:', error);
    return 'unknown';
  }
}