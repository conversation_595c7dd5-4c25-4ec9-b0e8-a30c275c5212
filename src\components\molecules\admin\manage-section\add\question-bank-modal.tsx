"use client";

import { useState, useEffect } from "react";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseButton } from "@/components/atoms/button";
import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { Search, Filter, X } from "lucide-react";
import { useGetQuestionBankListQuery } from "@/services/query/admin/manage-test/question-bank";
import {
  IGetQuestionBankListResponse,
  TSearchBy,
} from "@/interfaces/admin/manage-test/question-bank/list";

import Pagination from "../common/pagination";
import {
  useGetCategoryListQuery,
  useGetStartingLevelListQuery,
} from "@/services/query/admin/master";
import { QuestionTable } from "./question-table";

interface QuestionBankModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddQuestions: (questions: IGetQuestionBankListResponse[]) => void;
  excludeIds?: number[];
}

const searchByOptions = [
  { value: "question", label: "Question" },
  { value: "question_id", label: "Question ID" },
  { value: "option_a", label: "Option A" },
  { value: "option_b", label: "Option B" },
  { value: "option_c", label: "Option C" },
  { value: "option_d", label: "Option D" },
  { value: "correct_answer", label: "Correct Answer" },
  { value: "created_by", label: "Created By" },
  { value: "updated_by", label: "Updated By" },
];

const questionTypes = [
  { value: "multiple_choice", label: "Multiple Choice" },
  { value: "true_false", label: "True/False" },
  { value: "essay", label: "Essay" },
];

const QuestionBankModal = ({
  isOpen,
  onClose,
  onAddQuestions,
  excludeIds = [],
}: QuestionBankModalProps) => {
  // const [selectedQuestions, setSelectedQuestions] = useState<number[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchBy, setSearchBy] = useState<TSearchBy>("question");
  const [categoryFilter, setCategoryFilter] = useState<string>("");
  const [levelFilter, setLevelFilter] = useState<string>("");
  const [questionTypeFilter, setQuestionTypeFilter] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [pageSize, setPageSize] = useState(10);
  const [selectedQuestions, setSelectedQuestions] = useState<
    Record<number, number[]>
  >({});

  // API queries
  const { data: questionBankData, isLoading } = useGetQuestionBankListQuery({
    search: searchQuery || undefined,
    search_by: searchBy,
    category_id: categoryFilter ? parseInt(categoryFilter) : undefined,
    level_id: levelFilter ? parseInt(levelFilter) : undefined,
    question_type: questionTypeFilter || undefined,
    feature: "OnlineLearning",
    page: currentPage,
    limit: pageSize,
  });

  const { data: categoriesData } = useGetCategoryListQuery();

  const { data: levelsData } = useGetStartingLevelListQuery();

  const questions = questionBankData?.data || [];
  const pagination = questionBankData?.pagination;
  const categories = categoriesData?.data || [];
  const levels = levelsData?.data || [];

  // Filter out excluded questions
  const availableQuestions = questions.filter(
    (q) => !excludeIds.includes(q.id)
  );

  useEffect(() => {
    if (!isOpen) {
      setSelectedQuestions([]);
      setSearchQuery("");
      setCategoryFilter("");
      setLevelFilter("");
      setQuestionTypeFilter("");
      setCurrentPage(1);
      setShowFilters(false);
    }
  }, [isOpen]);

  const handleSelectAll = (checked: boolean) => {
    // setCurrentPageSelected((prev) => {
    //   const newState = { ...prev };
    //   availableQuestions.forEach((q) => {
    //     newState[q.id] = checked;
    //   });
    //   return newState;
    // });

    setSelectedQuestions((prev) => {
      const newSelections = { ...prev };
      const currentPageIds = availableQuestions.map((q) => q.id);

      if (checked) {
        newSelections[currentPage] = [
          ...(newSelections[currentPage] || []),
          ...currentPageIds,
        ];
      } else {
        newSelections[currentPage] = [];
      }

      // Remove duplicates
      newSelections[currentPage] = [...new Set(newSelections[currentPage])];
      return newSelections;
    });
  };

  const handleSelectQuestion = (questionId: number, checked: boolean) => {
    // setCurrentPageSelected((prev) => ({
    //   ...prev,
    //   [questionId]: checked,
    // }));

    setSelectedQuestions((prev) => {
      const newSelections = { ...prev };
      if (!newSelections[currentPage]) {
        newSelections[currentPage] = [];
      }

      if (checked) {
        newSelections[currentPage] = [
          ...newSelections[currentPage],
          questionId,
        ];
      } else {
        newSelections[currentPage] = newSelections[currentPage].filter(
          (id) => id !== questionId
        );
      }

      return newSelections;
    });
  };

  const handleAddQuestions = () => {
    const allQuestions = questionBankData?.data || [];
    const questionsToAdd = allQuestions.filter((q) =>
      selectedQuestions[currentPage].includes(q.id)
    );
    onAddQuestions(questionsToAdd);
    onClose();
  };

  const allSelectedQuestions = Object.values(selectedQuestions).flat();
  const isAllSelected =
    availableQuestions.length > 0 &&
    availableQuestions.every((q) =>
      selectedQuestions[currentPage]?.includes(q.id)
    );

  const handleClearFilters = () => {
    setSearchQuery("");
    setCategoryFilter("");
    setLevelFilter("");
    setQuestionTypeFilter("");
    setCurrentPage(1);
  };

  return (
    <BaseDialog open={isOpen} onOpenChange={onClose}>
      <BaseDialogContent className="sm:max-w-7xl sm:w-[80vw] max-h-[90vh] overflow-auto p-5 flex flex-col">
        <BaseDialogHeader className="flex flex-col items-start justify-between space-y-0">
          <BaseDialogTitle className="text-base font-medium text-comp-content-primary">
            Add Question from Question Bank
          </BaseDialogTitle>
          <hr className=" border-gray-200 -mx-5 w-[calc(100%+40px)]" />
        </BaseDialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          {/* Search and Filter Controls */}
          <div className="space-y-3">
            {/* Search Bar */}
            <div className="flex items-center gap-2">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <BaseInput
                  placeholder="Search questions..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <BaseSelect
                value={searchBy}
                onValueChange={(value) => setSearchBy(value as TSearchBy)}
              >
                <BaseSelectTrigger className="w-48">
                  <BaseSelectValue />
                </BaseSelectTrigger>
                <BaseSelectContent>
                  {searchByOptions.map((option) => (
                    <BaseSelectItem key={option.value} value={option.value}>
                      {option.label}
                    </BaseSelectItem>
                  ))}
                </BaseSelectContent>
              </BaseSelect>
              <BaseButton
                type="button"
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="w-4 h-4" />
                Filters
              </BaseButton>
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Advanced Filters</h4>
                  <BaseButton
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleClearFilters}
                    className="text-gray-500"
                  >
                    <X className="w-4 h-4 mr-1" />
                    Clear All
                  </BaseButton>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Category
                    </label>
                    <BaseSelect
                      value={categoryFilter}
                      onValueChange={setCategoryFilter}
                    >
                      <BaseSelectTrigger>
                        <BaseSelectValue placeholder="All Categories" />
                      </BaseSelectTrigger>
                      <BaseSelectContent>
                        <BaseSelectItem value="">All Categories</BaseSelectItem>
                        {categories.map((category) => (
                          <BaseSelectItem
                            key={category.id}
                            value={category.id.toString()}
                          >
                            {category.category_name}
                          </BaseSelectItem>
                        ))}
                      </BaseSelectContent>
                    </BaseSelect>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Level
                    </label>
                    <BaseSelect
                      value={levelFilter}
                      onValueChange={setLevelFilter}
                    >
                      <BaseSelectTrigger>
                        <BaseSelectValue placeholder="All Levels" />
                      </BaseSelectTrigger>
                      <BaseSelectContent>
                        <BaseSelectItem value="">All Levels</BaseSelectItem>
                        {levels.map((level) => (
                          <BaseSelectItem
                            key={level.id}
                            value={level.id.toString()}
                          >
                            {level.level}
                          </BaseSelectItem>
                        ))}
                      </BaseSelectContent>
                    </BaseSelect>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Question Type
                    </label>
                    <BaseSelect
                      value={questionTypeFilter}
                      onValueChange={setQuestionTypeFilter}
                    >
                      <BaseSelectTrigger>
                        <BaseSelectValue placeholder="All Types" />
                      </BaseSelectTrigger>
                      <BaseSelectContent>
                        <BaseSelectItem value="">All Types</BaseSelectItem>
                        {questionTypes.map((type) => (
                          <BaseSelectItem key={type.value} value={type.value}>
                            {type.label}
                          </BaseSelectItem>
                        ))}
                      </BaseSelectContent>
                    </BaseSelect>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Results Summary */}
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>
              {availableQuestions.length} questions available
              {excludeIds.length > 0 &&
                ` (${excludeIds.length} already selected)`}
            </span>
            <span>{allSelectedQuestions.length} selected</span>
          </div>

          {/* Questions Table */}
          <div className="flex-1 overflow-auto border rounded-lg flex flex-col">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-gray-500">Loading questions...</div>
              </div>
            ) : availableQuestions.length === 0 ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-gray-500">No questions found</div>
              </div>
            ) : (
              <QuestionTable
                questions={availableQuestions}
                selectedQuestions={selectedQuestions}
                currentPage={currentPage}
                isAllSelected={isAllSelected}
                onSelectAll={handleSelectAll}
                onSelectQuestion={handleSelectQuestion}
                withCheckbox={true}
              />
            )}
          </div>

          {/* Pagination */}
          {pagination && (
            <Pagination
              totalItems={pagination.total_data ?? 0}
              currentPage={currentPage}
              pageSize={pageSize}
              pageSizeOptions={[10, 20, 50]}
              onPageChange={setCurrentPage}
              onPageSizeChange={setPageSize}
            />
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <BaseButton variant="outline" onClick={onClose}>
            Cancel
          </BaseButton>
          <BaseButton
            onClick={handleAddQuestions}
            className="bg-orange-500 hover:bg-orange-600"
            disabled={allSelectedQuestions.length === 0}
          >
            Add Question ({allSelectedQuestions.length})
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default QuestionBankModal;
