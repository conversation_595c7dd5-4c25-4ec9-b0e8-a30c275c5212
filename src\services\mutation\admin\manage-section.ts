import {
  IUpdateSectionParams,
  IUpdateSectionBody,
} from "@/interfaces/admin/manage-section/update";
import { IGetSectionExportQuery } from "@/interfaces/admin/manage-section/list";
import { ICreateSectionBody } from "@/interfaces/admin/manage-section/new";
import {
  apiUpdateSection,
  apiExportSection,
  apiCreateSection,
} from "@/services/api/admin/manage-section";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { sectionQueryKeys } from "@/services/query/admin/manage-section";

export const useCreateSectionMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["section", "create"],
    mutationFn: async (body: ICreateSectionBody) => {
      return await apiCreateSection(body);
    },
    onSuccess: () => {
      // Invalidate section list queries to refresh data
      queryClient.invalidateQueries({
        queryKey: sectionQueryKeys.list(),
      });
    },
  });
};

export const useUpdateSectionMutation = () => {
  return useMutation({
    mutationKey: ["section", "update"],
    mutationFn: async ({
      params,
      body,
    }: {
      params: IUpdateSectionParams;
      body: IUpdateSectionBody;
    }) => {
      return await apiUpdateSection(params, body);
    },
  });
};

export const useExportSectionMutation = () => {
  return useMutation({
    mutationKey: ["section", "export"],
    mutationFn: async (params?: IGetSectionExportQuery) => {
      const result = await apiExportSection(params);

      const byteCharacters = atob(result.file);
      const byteNumbers = new Array(byteCharacters.length)
        .fill(0)
        .map((_, i) => byteCharacters.charCodeAt(i));
      const byteArray = new Uint8Array(byteNumbers);

      const blob = new Blob([byteArray]);
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", result.filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      return result;
    },
  });
};
