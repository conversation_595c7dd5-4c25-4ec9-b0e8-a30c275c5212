"use client";
import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import {
  IGetListMaterialResponse,
  IMaterial,
} from "@/interfaces/admin/manage-material/list";
import DocumentPreview from "./document-preview";
import VideoPreview from "./preview/video-preview";
import AudioPreview from "./preview/audio-preview";
import { useGetLearningMaterialUrlQuery } from "@/services/query/admin/manage-material";

// Types
type MaterialType = "video" | "audio" | "document";

// Dummy URLs for preview content
export const DUMMY_CONTENT_URLS = {
  video:
    "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
  audio: "https://onlinetestcase.com/wp-content/uploads/2023/06/100-KB-MP3.mp3",
  document: "https://www.sldttc.org/allpdf/21583473018.pdf",
};

const PREVIEW_TITLES = {
  video: "Preview Video",
  audio: "Preview Audio",
  document: "Preview Document",
};

// Main Modal Component
interface PreviewMaterialModalProps {
  isOpen: boolean;
  onClose: () => void;
  material: IGetListMaterialResponse | null;
  type: MaterialType;
}

export const PreviewMaterialModal = ({
  isOpen,
  onClose,
  material,
  type,
}: PreviewMaterialModalProps) => {
  const title = PREVIEW_TITLES[type];

  return (
    <BaseDialog open={isOpen} onOpenChange={onClose}>
      <BaseDialogContent className="w-full max-w-6xl sm:max-w-6xl h-full max-h-[90vh] p-0 overflow-y-auto flex flex-col">
        <BaseDialogHeader className="p-6 pb-0">
          <BaseDialogTitle className="flex items-center justify-between">
            <span>{title}</span>
          </BaseDialogTitle>
        </BaseDialogHeader>

        <div className="p-6 pt-4 min-h-0 flex-1 flex flex-col">
          <div className="max-h-[400px] overflow-hidden object-contain">
            <PreviewContent type={type} material={material} />
          </div>

          {/* Close Button */}
          <div className="flex justify-end mt-6">
            <BaseButton
              onClick={onClose}
              className="px-8 bg-orange-500 hover:bg-orange-600"
            >
              Close
            </BaseButton>
          </div>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

// Preview Content Component
interface PreviewContentProps {
  type: MaterialType;
  material: IGetListMaterialResponse | null;
}

const PreviewContent = ({ type, material }: PreviewContentProps) => {
  switch (type) {
    case "video":
      return <VideoPreview material={material} />;
    case "audio":
      return <AudioPreview material={material} />;
    case "document":
      return <DocumentPreview material={material} />;
    default:
      return null;
  }
};

export default PreviewMaterialModal;
