import { BaseButton } from "@/components/atoms/button";
import {
  BaseCommand,
  BaseCommandEmpty,
  BaseCommandGroup,
  BaseCommandInput,
  BaseCommandItem,
  BaseCommandList,
} from "@/components/atoms/command";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseInput } from "@/components/atoms/input";
import { BaseLabel } from "@/components/atoms/label";
import {
  BasePopover,
  BasePopoverContent,
  BasePopoverTrigger,
} from "@/components/atoms/popover";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { DialogClose } from "@/components/ui/dialog";
import {
  ICategory,
  ISubCategory,
} from "@/interfaces/admin/manage-category/list";

import {
  createLearningLevelFormSchema,
  ICreateLearningLevelForm,
} from "@/interfaces/admin/manage-learning-path/new";
import { cn } from "@/lib/utils";
import { useInsertLearningLevelMutation } from "@/services/mutation/learning-path/new";
import { useUpdateLearningLevelMutation } from "@/services/mutation/learning-path/update";
import { useGetLearningLevelDetailQuery } from "@/services/query/learning-path/detail";
import { useGetLearningLevelListQuery } from "@/services/query/learning-path/list";
import { useManageLearningPathFilterStore } from "@/store/admin/manage-learning-path/filter";

import { useManageLearningPathModalStore } from "@/store/admin/manage-learning-path/modal";
import { isNumberInput } from "@/utils/common/number";
import { yupResolver } from "@hookform/resolvers/yup";
import { Check, ChevronsUpDown } from "lucide-react";
import React, { useEffect } from "react";
import {
  useForm,
  useFormContext,
  Path,
  FormProvider,
  Controller,
  DefaultValues,
} from "react-hook-form";
import toast from "react-hot-toast";
import { useShallow } from "zustand/react/shallow";

const defaultValue: DefaultValues<ICreateLearningLevelForm> = {
  level_name: "",
  level: "",
  status: "active",
};

const ManageLearningLevelNewModal = () => {
  const { setOpenAddLearningLevelModal, openAddLearningLevelModal } =
    useManageLearningPathModalStore(
      useShallow(
        ({ openAddLearningLevelModal, setOpenAddLearningLevelModal }) => ({
          setOpenAddLearningLevelModal,
          openAddLearningLevelModal,
        })
      )
    );
  const { currentData, setCurrentData, learningLevelQuery } =
    useManageLearningPathFilterStore(
      useShallow(({ currentData, setCurrentData, learningLevelQuery }) => ({
        currentData,
        setCurrentData,
        learningLevelQuery,
      }))
    );

  const title = currentData ? "Update Learning Level" : "Add Learning Level";
  const createLearningLevel = useInsertLearningLevelMutation();
  const updateLearningLevel = useUpdateLearningLevelMutation();
  const getLearningLevels = useGetLearningLevelListQuery(
    learningLevelQuery,
    true
  );
  const getLearningLevelDetail = useGetLearningLevelDetailQuery({
    id: currentData,
  });

  const form = useForm({
    resolver: yupResolver(createLearningLevelFormSchema),
    mode: "all",
  });

  const handleOpenChangeModal = (state: boolean) => {
    if (!state) {
      setCurrentData(null);
      form.reset(defaultValue);
    }

    setOpenAddLearningLevelModal(state);
  };

  useEffect(() => {
    if (currentData && getLearningLevelDetail.data?.data) {
      const data = getLearningLevelDetail.data.data;

      const level = data.level === 0 ? "0" : "" + data.level;

      form.reset({
        level_name: data.name ?? "",
        status: (data.status as any) ?? "",
        level: level,
      });
    }
  }, [getLearningLevelDetail.data]);

  const handleSubmit = (data: ICreateLearningLevelForm) => {
    if (currentData) {
      updateLearningLevel.mutate(
        {
          params: {
            id: currentData,
          },
          body: {
            level: Number(data.level),
            status: data.status,
            name: data.level_name,
          },
        },
        {
          onSuccess: (data) => {
            toast.success(data.message);
            getLearningLevels.refetch();
            setCurrentData(null);
            setOpenAddLearningLevelModal(false);
          },
          onError: (data) => {
            toast.error(data.message);
          },
        }
      );

      return;
    }

    createLearningLevel.mutate(
      {
        level: Number(data.level),
        status: data.status,
        name: data.level_name,
      },
      {
        onSuccess: (data) => {
          toast.success(data.message);
          getLearningLevels.refetch();
          setOpenAddLearningLevelModal(false);
        },
        onError: (data) => {
          toast.error(data.message);
        },
      }
    );
  };

  return (
    <BaseDialog
      open={openAddLearningLevelModal}
      onOpenChange={handleOpenChangeModal}
    >
      <BaseDialogContent className="min-w-2/5">
        <BaseDialogHeader>
          <BaseDialogTitle className="flex flex-col gap-4">
            <span>{title}</span>
            <BaseSeparator />
          </BaseDialogTitle>
        </BaseDialogHeader>
        <FormProvider {...form}>
          <form
            className="flex flex-col gap-6"
            onSubmit={form.handleSubmit(handleSubmit)}
          >
            <div className="grid grid-cols-2 gap-4">
              <InputNumber id="level" label="Level" placeholder="Input level" />
              <InputSelect
                id="status"
                label="Status"
                placeholder="Select status"
                options={[
                  { value: "active", label: "Active" },
                  { value: "inactive", label: "Inactive" },
                ]}
              />
              <div className="col-span-2">
                <InputString
                  id="level_name"
                  label="Level Name"
                  placeholder="Input level name"
                />
              </div>
            </div>
            <BaseSeparator className="mt-4 -mb-2" />
            <div className="flex justify-end gap-3 -mb-3">
              <DialogClose asChild>
                <BaseButton
                  className="h-11 w-32"
                  variant={"outline"}
                  // onClick={() => setOpenAddUser(false)}
                >
                  Cancel
                </BaseButton>
              </DialogClose>
              <BaseButton
                className="h-11 w-32"
                type="submit"
                disabled={!form.formState.isValid}
              >
                {currentData ? "Update" : "Add"} Level
              </BaseButton>
            </div>
          </form>
        </FormProvider>
      </BaseDialogContent>
    </BaseDialog>
  );
};

interface IFormProps {
  isOpen: boolean;
  data: ICategory | ISubCategory | null;
  onCloseModal: VoidFunction;
}

const InputString = <T extends ICreateLearningLevelForm>({
  label,
  id,
  placeholder,
  optional = false,
  readonly = false,
}: {
  label: string;
  id: keyof T;
  placeholder?: string;
  optional?: boolean;
  readonly?: boolean;
}) => {
  const form = useFormContext<T>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id as string}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseInput
        id={id as string}
        placeholder={placeholder}
        {...form.register(id as Path<T>)}
        className="h-11 disabled:bg-gray-100"
        readOnly={readonly}
        disabled={readonly}
      />
    </div>
  );
};

const InputNumber = ({
  label,
  id,
  placeholder,
  optional = false,
}: {
  label: string;
  id: keyof ICreateLearningLevelForm;
  placeholder?: string;
  optional?: boolean;
}) => {
  const form = useFormContext<ICreateLearningLevelForm>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseInput
        id={id}
        placeholder={placeholder}
        {...form.register(id)}
        className="h-11"
        onKeyDown={(e) => {
          if (isNumberInput(e)) e.preventDefault();
        }}
        type="string"
      />
    </div>
  );
};

const InputSelect = ({
  label,
  id,
  placeholder,
  options,
  optional = false,
}: {
  label: string;
  id: Path<ICreateLearningLevelForm>;
  placeholder?: string;
  options: { value: string; label: string }[];
  optional?: boolean;
}) => {
  const form = useFormContext<ICreateLearningLevelForm>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>

      <Controller
        name={id as any}
        control={form.control}
        render={({ field }) => {
          return (
            <BasePopover>
              <BasePopoverTrigger asChild>
                <BaseButton
                  variant="outline"
                  className="w-full min-h-11 justify-between"
                >
                  {field.value != null
                    ? options.find((o) => o.value === String(field.value))
                        ?.label ?? placeholder
                    : placeholder}

                  <ChevronsUpDown className="opacity-50" />
                </BaseButton>
              </BasePopoverTrigger>
              <BasePopoverContent className="w-full p-0">
                <BaseCommand>
                  <BaseCommandInput
                    placeholder="Search data..."
                    className="h-9"
                  />
                  <BaseCommandList>
                    <BaseCommandEmpty>No data found.</BaseCommandEmpty>
                    <BaseCommandGroup>
                      {options.map((option) => (
                        <BaseCommandItem
                          key={`${id}-${option.value}`}
                          value={`${option.label}__${option.value}`}
                          onSelect={(currentValue) => {
                            field.onChange(currentValue.split("__")[1]);
                          }}
                        >
                          {option.label}
                          <Check
                            className={cn(
                              "ml-auto",
                              field.value === option.value
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                        </BaseCommandItem>
                      ))}
                    </BaseCommandGroup>
                  </BaseCommandList>
                </BaseCommand>
              </BasePopoverContent>
            </BasePopover>
          );
        }}
      />
    </div>
  );
};

export default ManageLearningLevelNewModal;
