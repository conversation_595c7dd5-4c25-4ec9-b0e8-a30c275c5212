import { ICreateImageRepositoryBody } from '@/interfaces/admin/manage-test/image-repository/form';
import { ICreateImageRepositoryBodyData } from '@/interfaces/admin/manage-test/image-repository/new';

export function buildCreateImageRepositoryPayload(
  v: ICreateImageRepositoryBody,
  feature: 'OnlineLearning' | 'InClassTraining' = 'OnlineLearning'
): { body: ICreateImageRepositoryBodyData; file?: File | null } {
  const body: ICreateImageRepositoryBodyData = {
    feature,
    image_name: v.image_name,
    category_id: v.category.map((o) => +o.value!),
    level_id: v.level.map((l) => +l.value!),
  };

  const file = v.image as File | null;

  return { body, file };
}
