"use server";

import {
  IGetSectionListQuery,
  IGetSectionListResponse,
  IGetSectionDetailParams,
  IGetSectionDetailResponse,
  IGetSectionExportQuery,
} from "@/interfaces/admin/manage-section/list";
import {
  IUpdateSectionParams,
  IUpdateSectionBody,
  IUpdateSectionResponse,
} from "@/interfaces/admin/manage-section/update";
import {
  ICreateSectionBody,
  ICreateSectionResponse,
} from "@/interfaces/admin/manage-section/new";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetSectionList = async (params?: IGetSectionListQuery) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetSectionListResponse[]>
    >("/cms/admin/learning/section/list", { params });

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiGetSectionDetail = async (params: IGetSectionDetailParams) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetSectionDetailResponse>
    >(`/cms/admin/learning/section/detail/${params.id}`);

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiUpdateSection = async (
  params: IUpdateSectionParams,
  body: IUpdateSectionBody
) => {
  try {
    const response = await api.put<IGlobalResponseDto<IUpdateSectionResponse>>(
      `/cms/admin/learning/section/update/${params.id}`,
      body
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiExportSection = async (params?: IGetSectionExportQuery) => {
  try {
    const response = await api.get("/cms/admin/learning/section/export", {
      params,
      responseType: "arraybuffer",
    });

    let filename = "section-export.xlsx";

    const disposition = response.headers["content-disposition"];
    if (disposition && disposition.includes("filename=")) {
      const match = disposition.match(/filename="?([^"]+)"?/);
      if (match?.[1]) {
        filename = match[1];
      }
    }

    const base64File = Buffer.from(response.data).toString("base64");

    return {
      filename,
      file: base64File,
    };
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiCreateSection = async (body: ICreateSectionBody) => {
  try {
    const response = await api.post<ICreateSectionResponse>(
      "/cms/admin/learning/section/insert",
      body
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};
