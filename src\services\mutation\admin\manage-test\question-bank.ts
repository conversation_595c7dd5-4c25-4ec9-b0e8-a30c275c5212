import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  apiCreateQuestionBank,
  apiDeleteQuestionBank,
  apiExportQuestionBankList,
  apiUpdateQuestionBank,
} from '@/services/api/admin/manage-test/question-bank';
import { ICreateQuestionBankBodyData } from '@/interfaces/admin/manage-test/question-bank/new';
import {
  IUpdateQuestionBankBodyData,
  IUpdateQuestionBankParams,
} from '@/interfaces/admin/manage-test/question-bank/update';
import { IGetQuestionBankListQuery } from '@/interfaces/admin/manage-test/question-bank/list';
import toast from 'react-hot-toast';

export const useCreateQuestionBankMutation = () => {
  const qc = useQueryClient();

  return useMutation({
    mutationKey: ['question-bank', 'create'],
    mutationFn: async ({
      body,
      file,
    }: {
      body: ICreateQuestionBankBodyData;
      file?: File | null;
    }) => apiCreateQuestionBank(body, file),
    onSuccess: () => {
      toast.success('Question Bank created');
      qc.invalidateQueries({ queryKey: ['question-bank', 'list'] });
    },
    onError: () => {
      toast.error('Failed to create Question Bank');
    },
  });
};

export const useUpdateQuestionBankMutation = () => {
  const qc = useQueryClient();

  return useMutation({
    mutationKey: ['question-bank', 'update'],
    mutationFn: async ({
      params,
      body,
      file,
    }: {
      params: IUpdateQuestionBankParams;
      body: IUpdateQuestionBankBodyData;
      file?: File | null;
    }) => apiUpdateQuestionBank(params, body, file),
    onSuccess: (_data, variables) => {
      toast.success('Question Bank updated');
      qc.invalidateQueries({ queryKey: ['question-bank', 'list'] });
      if (variables?.params?.id) {
        qc.invalidateQueries({
          queryKey: ['question-bank', 'detail', variables.params.id],
        });
      }
    },
    onError: () => {
      toast.error('Failed to update Question Bank');
    },
  });
};

export const useDeleteQuestionBankMutation = () => {
  const qc = useQueryClient();

  return useMutation({
    mutationKey: ['question-bank', 'delete'],
    mutationFn: async ({ id }: { id: number }) => apiDeleteQuestionBank({ id }),
    onSuccess: (_data, variables) => {
      toast.success('Question Bank deleted');
      qc.invalidateQueries({ queryKey: ['question-bank', 'list'] });
      if (variables?.id) {
        qc.invalidateQueries({
          queryKey: ['question-bank', 'detail', variables.id],
        });
      }
    },
    onError: () => {
      toast.error('Failed to delete Question Bank');
    },
  });
};

export const useExportQuestionBankMutation = () => {
  return useMutation({
    mutationKey: ['question-bank', 'export'],
    mutationFn: async (params?: IGetQuestionBankListQuery) => {
      const result = await apiExportQuestionBankList(params);

      const byteCharacters = atob(result.file);
      const byteNumbers = new Array(byteCharacters.length)
        .fill(0)
        .map((_, i) => byteCharacters.charCodeAt(i));
      const byteArray = new Uint8Array(byteNumbers);

      const blob = new Blob([byteArray]);
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', result.filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    },
    onSuccess: () => {
      toast.success('Download started');
    },
    onError: (err) => {
      console.error(err);
      toast.error('Failed to download file. Try again!');
    },
  });
};
