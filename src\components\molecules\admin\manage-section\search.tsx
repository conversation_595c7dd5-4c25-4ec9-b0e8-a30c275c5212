import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { Search } from "lucide-react";
import { useDebouncedCallback } from "use-debounce";
import { useManageSectionQueryStore } from "@/store/admin/manage-section/query";
import { useShallow } from "zustand/react/shallow";

const ManageSectionTableHeaderSearch = () => {
  const { sectionQuery, setSectionQuery } = useManageSectionQueryStore(
    useShallow(({ sectionQuery, setSectionQuery }) => ({
      sectionQuery,
      setSectionQuery,
    }))
  );

  const searchByOption = [
    { value: "sub_section_id", label: "Sub Section ID" },
    { value: "sub_section_name", label: "Sub Section Name" },
    { value: "soft_competency_name", label: "Soft Competency Name" },
    { value: "tech_competency_name", label: "Tech Competency Name" },
    { value: "modul_name", label: "Module Name" },
    { value: "created_by", label: "Created By" },
    { value: "updated_by", label: "Updated By" },
  ];

  const handleSearch = useDebouncedCallback((value: string) => {
    setSectionQuery({ search: value || undefined, page: 1 });
  }, 500);

  const handleSearchBy = useDebouncedCallback((value: string) => {
    setSectionQuery({
      search_by: (value as any) || undefined,
      page: 1,
    });
  }, 500);

  return (
    <div className="text-[#3C3C3C] font-semibold rounded-lg flex items-center gap-1 relative w-[40%] bg-white px-3">
      <div>
        <BaseSelect
          value={sectionQuery.search_by || ""}
          onValueChange={handleSearchBy}
        >
          <BaseSelectTrigger className="w-44 border-none px-1">
            <BaseSelectValue placeholder="Search By">
              {searchByOption.find((it) => it.value === sectionQuery.search_by)
                ?.label || "Search By"}
            </BaseSelectValue>
          </BaseSelectTrigger>
          <BaseSelectContent>
            {searchByOption.map((it) => (
              <BaseSelectItem key={it.value} value={it.value}>
                {it.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>
      <BaseSeparator orientation="vertical" />
      <BaseInput
        className="border-none h-12 focus-visible:border-none focus-visible:ring-0"
        placeholder="Search..."
        defaultValue={sectionQuery.search || ""}
        onChange={(e) => handleSearch(e.target.value)}
      />
      <Search size={24} />
      <BaseSeparator orientation="vertical" className="bg-red-500 w-1" />
    </div>
  );
};

export default ManageSectionTableHeaderSearch;
