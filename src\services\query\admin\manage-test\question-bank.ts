import { useQuery } from '@tanstack/react-query';
import {
  apiGetQuestionBankDetail,
  apiGetQuestionBankList,
} from '@/services/api/admin/manage-test/question-bank';
import { IGetQuestionBankListQuery } from '@/interfaces/admin/manage-test/question-bank/list';

export const useGetQuestionBankListQuery = (
  params?: IGetQuestionBankListQuery,
  enabled = true
) => {
  return useQuery({
    queryKey: ['question-bank', 'list', params],
    queryFn: () => apiGetQuestionBankList(params),
    enabled,
  });
};

export const useGetQuestionBankDetailQuery = (id?: number, enabled = !!id) => {
  return useQuery({
    queryKey: ['question-bank', 'detail', id],
    queryFn: () => apiGetQuestionBankDetail({ id: id as number }),
    enabled: enabled && !!id,
  });
};
